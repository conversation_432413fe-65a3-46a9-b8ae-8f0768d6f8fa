import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { GetSumaryRegularChangeItemRes } from '../../../../../services/api/models';
import { FormsModule } from '@angular/forms';
import { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';
import { CommonModule, DecimalPipe } from '@angular/common';
import { LoadingService } from '../../../../shared/services/loading.service';
import { RegularChangeItemService } from '../../../../../services/api/services';
import { finalize } from 'rxjs';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CheckUserDataService } from '../../services/check-user-data.service';
import { AccordionModule } from 'primeng/accordion';

@Component({
  selector: 'app-step-iii-choice',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    DecimalPipe,
    BaseFilePipe,
    AccordionModule
  ],
  templateUrl: './step-iii-choice.component.html',
  styleUrl: './step-iii-choice.component.scss'
})
export class StepIiiChoiceComponent implements OnInit {
  @Input() selectedData: GetSumaryRegularChangeItemRes[] = [];
  @Output() nextEvent = new EventEmitter();
  @Output() goBackEvent = new EventEmitter();
  constructor(
    private _regularChangeService: RegularChangeItemService,
    private router: Router,
    private messageService: MessageService,
    private checkUserDataService: CheckUserDataService
  ) { }

  ngOnInit(): void {
    this.getDataStep3();
    this.selectedData = this.checkUserDataService.getSelectedData();
  }
  next() {
    const hasUnsetPrice = this.selectedData.some(item =>
      item.RegularDetails?.some(detail => detail.CPrice === null)
    );

    if (hasUnsetPrice) {
      this.messageService.add({
        severity: 'warn',
        summary: '警告',
        detail: '有項目尚未設定價格，請確認後再繼續。'
      });
      return;
    }
    this.nextEvent.emit();
  }

  goBackHandle(step: number) {
    this.goBackEvent.emit(step)
  }

  getDataStep3() {
    LoadingService.loading(true);
    this._regularChangeService.apiRegularChangeItemGetSumaryRegularChangeItemPost$Json()
      .pipe(
        finalize(() => LoadingService.loading(false))
      )
      .subscribe(res => {
        if (res.Entries) {
          this.selectedData = res.Entries;
        }
      });
  }
  getTotalPrice(): number {
    return this.selectedData.reduce((total, item) => {
      const detailsTotal = item.RegularDetails?.reduce((detailTotal, detail) => {
        return detailTotal + (detail.CPrice || 0);
      }, 0) || 0;
      return total + detailsTotal;
    }, 0);
  }
  getItemTotal(item: any): number {
    return item.RegularDetails?.reduce((sum: number, detail: any) => {
      return sum + (detail.CPrice || 0);
    }, 0) || 0;
  }

}
