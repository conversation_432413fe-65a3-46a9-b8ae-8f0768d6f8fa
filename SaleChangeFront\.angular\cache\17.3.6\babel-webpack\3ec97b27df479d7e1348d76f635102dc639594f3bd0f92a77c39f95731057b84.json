{"ast": null, "code": "export const QUOTATION_TEMPLATE = `<!DOCTYPE html>\n<html>\n\n<head>\n  <meta charset=\"utf-8\">\n  <title>報價單列印模板</title>\n  <style>\n    body {\n      font-family: 'Microsoft JhengHei', '微軟正黑體', <PERSON><PERSON>, sans-serif;\n      margin: 20px;\n      font-size: 14px;\n      line-height: 1.6;\n    }\n\n    .header {\n      text-align: center;\n      margin-bottom: 30px;\n    }\n\n    .header h1 {\n      margin: 0;\n      font-size: 24px;\n      color: #333;\n      font-weight: bold;\n    }\n\n    .info-section {\n      margin-bottom: 20px;\n      border-bottom: 1px solid #ddd;\n      padding-bottom: 15px;\n    }\n\n    .info-row {\n      display: flex;\n      margin-bottom: 8px;\n    }\n\n    .info-label {\n      font-weight: bold;\n      width: 100px;\n      flex-shrink: 0;\n    }\n\n    .info-value {\n      flex: 1;\n    }\n\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n    }\n\n    th {\n      background-color: #27ae60;\n      color: white;\n      border: 1px solid #ddd;\n      padding: 10px 8px;\n      text-align: center;\n      font-weight: bold;\n    }\n\n    td {\n      border: 1px solid #ddd;\n      padding: 8px;\n    }\n\n    .text-center {\n      text-align: center;\n    }\n\n    .text-right {\n      text-align: right;\n    }\n\n    .total-section {\n      text-align: right;\n      margin-top: 20px;\n      padding-top: 15px;\n      border-top: 2px solid #27ae60;\n    }\n\n    .subtotal {\n      font-size: 14px;\n      margin-bottom: 5px;\n      color: #666;\n    }\n\n    .additional-fee {\n      font-size: 14px;\n      margin-bottom: 10px;\n      color: #666;\n    }\n\n    .total-amount {\n      font-size: 18px;\n      font-weight: bold;\n      color: #27ae60;\n      border-top: 1px solid #ddd;\n      padding-top: 10px;\n    }\n\n    .footer {\n      margin-top: 40px;\n      text-align: center;\n      font-size: 12px;\n      color: #666;\n    }\n\n    .signature-section {\n      margin-top: 40px;\n      page-break-inside: avoid;\n    }\n\n    .signature-box {\n      width: 300px;\n      margin: 0 auto;\n      text-align: center;\n    }\n\n    .signature-label {\n      font-weight: bold;\n      margin-bottom: 40px;\n      font-size: 16px;\n    }\n\n    .signature-line {\n      border-bottom: 2px solid #000;\n      height: 60px;\n      margin-bottom: 10px;\n      position: relative;\n    }\n\n    .signature-date {\n      font-size: 14px;\n      margin-top: 15px;\n    }\n\n    .signature-notes {\n      margin-top: 30px;\n      padding: 15px;\n      background-color: #f9f9f9;\n      border-left: 4px solid #27ae60;\n    }\n\n    .signature-notes p {\n      margin: 0 0 10px 0;\n      font-weight: bold;\n    }\n\n    .signature-notes ul {\n      margin: 0;\n      padding-left: 20px;\n    }\n\n    .signature-notes li {\n      margin-bottom: 5px;\n      line-height: 1.4;\n    }\n\n    @media print {\n      body {\n        margin: 0;\n      }\n\n      .header {\n        page-break-inside: avoid;\n      }\n\n      .signature-section {\n        page-break-inside: avoid;\n      }\n    }\n  </style>\n</head>\n\n<body>\n  <div class=\"header\">\n    <h1>報價單</h1>\n  </div>\n\n  <div class=\"info-section\">\n    <div class=\"info-row\">\n      <span class=\"info-label\">建案名稱：</span>\n      <span class=\"info-value\">{{buildCaseName}}</span>\n    </div>\n    <div class=\"info-row\">\n      <span class=\"info-label\">戶別：</span>\n      <span class=\"info-value\">{{houseHold}}</span>\n    </div>\n    <div class=\"info-row\">\n      <span class=\"info-label\">樓層：</span>\n      <span class=\"info-value\">{{floor}}樓</span>\n    </div>\n    <div class=\"info-row\">\n      <span class=\"info-label\">列印日期：</span>\n      <span class=\"info-value\">{{printDate}}</span>\n    </div>\n  </div>\n\n  <table>\n    <thead>\n      <tr>\n        <th width=\"8%\">序號</th>\n        <th width=\"30%\">項目名稱</th>\n        <th width=\"12%\">單價 (元)</th>\n        <th width=\"8%\">單位</th>\n        <th width=\"8%\">數量</th>\n        <th width=\"15%\">小計 (元)</th>\n        <th width=\"19%\">備註</th>\n      </tr>\n    </thead>\n    <tbody>\n      {{itemsHtml}}\n    </tbody>\n  </table>\n\n  <div class=\"total-section\">\n    <div class=\"subtotal\">\n      小計：{{subtotalAmount}}\n    </div>\n    {{additionalFeeHtml}}\n    <div class=\"total-amount\">\n      總金額：{{totalAmount}}\n    </div>\n  </div>\n\n  <div class=\"signature-section\">\n    <div class=\"signature-box\">\n      <div class=\"signature-label\">客戶簽名：</div>\n      <div class=\"signature-line\"></div>\n      <div class=\"signature-date\">日期：_____年_____月_____日</div>\n    </div>\n\n    <div class=\"signature-notes\">\n      <p><strong>注意事項：</strong></p>\n      <ul>\n        <li>此報價單有效期限為30天，逾期需重新報價</li>\n        <li>報價內容若有異動，請重新確認</li>\n        <li>簽名確認後即視為同意此報價內容</li>\n      </ul>\n    </div>\n  </div>\n\n  <div class=\"footer\">\n    此報價單由系統自動產生，列印時間：{{printDateTime}}\n  </div>\n</body>\n\n</html>`;", "map": {"version": 3, "names": ["QUOTATION_TEMPLATE"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\assets\\template\\quotation-template.ts"], "sourcesContent": ["export const QUOTATION_TEMPLATE = `<!DOCTYPE html>\n<html>\n\n<head>\n  <meta charset=\"utf-8\">\n  <title>報價單列印模板</title>\n  <style>\n    body {\n      font-family: 'Microsoft JhengHei', '微軟正黑體', <PERSON><PERSON>, sans-serif;\n      margin: 20px;\n      font-size: 14px;\n      line-height: 1.6;\n    }\n\n    .header {\n      text-align: center;\n      margin-bottom: 30px;\n    }\n\n    .header h1 {\n      margin: 0;\n      font-size: 24px;\n      color: #333;\n      font-weight: bold;\n    }\n\n    .info-section {\n      margin-bottom: 20px;\n      border-bottom: 1px solid #ddd;\n      padding-bottom: 15px;\n    }\n\n    .info-row {\n      display: flex;\n      margin-bottom: 8px;\n    }\n\n    .info-label {\n      font-weight: bold;\n      width: 100px;\n      flex-shrink: 0;\n    }\n\n    .info-value {\n      flex: 1;\n    }\n\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n    }\n\n    th {\n      background-color: #27ae60;\n      color: white;\n      border: 1px solid #ddd;\n      padding: 10px 8px;\n      text-align: center;\n      font-weight: bold;\n    }\n\n    td {\n      border: 1px solid #ddd;\n      padding: 8px;\n    }\n\n    .text-center {\n      text-align: center;\n    }\n\n    .text-right {\n      text-align: right;\n    }\n\n    .total-section {\n      text-align: right;\n      margin-top: 20px;\n      padding-top: 15px;\n      border-top: 2px solid #27ae60;\n    }\n\n    .subtotal {\n      font-size: 14px;\n      margin-bottom: 5px;\n      color: #666;\n    }\n\n    .additional-fee {\n      font-size: 14px;\n      margin-bottom: 10px;\n      color: #666;\n    }\n\n    .total-amount {\n      font-size: 18px;\n      font-weight: bold;\n      color: #27ae60;\n      border-top: 1px solid #ddd;\n      padding-top: 10px;\n    }\n\n    .footer {\n      margin-top: 40px;\n      text-align: center;\n      font-size: 12px;\n      color: #666;\n    }\n\n    .signature-section {\n      margin-top: 40px;\n      page-break-inside: avoid;\n    }\n\n    .signature-box {\n      width: 300px;\n      margin: 0 auto;\n      text-align: center;\n    }\n\n    .signature-label {\n      font-weight: bold;\n      margin-bottom: 40px;\n      font-size: 16px;\n    }\n\n    .signature-line {\n      border-bottom: 2px solid #000;\n      height: 60px;\n      margin-bottom: 10px;\n      position: relative;\n    }\n\n    .signature-date {\n      font-size: 14px;\n      margin-top: 15px;\n    }\n\n    .signature-notes {\n      margin-top: 30px;\n      padding: 15px;\n      background-color: #f9f9f9;\n      border-left: 4px solid #27ae60;\n    }\n\n    .signature-notes p {\n      margin: 0 0 10px 0;\n      font-weight: bold;\n    }\n\n    .signature-notes ul {\n      margin: 0;\n      padding-left: 20px;\n    }\n\n    .signature-notes li {\n      margin-bottom: 5px;\n      line-height: 1.4;\n    }\n\n    @media print {\n      body {\n        margin: 0;\n      }\n\n      .header {\n        page-break-inside: avoid;\n      }\n\n      .signature-section {\n        page-break-inside: avoid;\n      }\n    }\n  </style>\n</head>\n\n<body>\n  <div class=\"header\">\n    <h1>報價單</h1>\n  </div>\n\n  <div class=\"info-section\">\n    <div class=\"info-row\">\n      <span class=\"info-label\">建案名稱：</span>\n      <span class=\"info-value\">{{buildCaseName}}</span>\n    </div>\n    <div class=\"info-row\">\n      <span class=\"info-label\">戶別：</span>\n      <span class=\"info-value\">{{houseHold}}</span>\n    </div>\n    <div class=\"info-row\">\n      <span class=\"info-label\">樓層：</span>\n      <span class=\"info-value\">{{floor}}樓</span>\n    </div>\n    <div class=\"info-row\">\n      <span class=\"info-label\">列印日期：</span>\n      <span class=\"info-value\">{{printDate}}</span>\n    </div>\n  </div>\n\n  <table>\n    <thead>\n      <tr>\n        <th width=\"8%\">序號</th>\n        <th width=\"30%\">項目名稱</th>\n        <th width=\"12%\">單價 (元)</th>\n        <th width=\"8%\">單位</th>\n        <th width=\"8%\">數量</th>\n        <th width=\"15%\">小計 (元)</th>\n        <th width=\"19%\">備註</th>\n      </tr>\n    </thead>\n    <tbody>\n      {{itemsHtml}}\n    </tbody>\n  </table>\n\n  <div class=\"total-section\">\n    <div class=\"subtotal\">\n      小計：{{subtotalAmount}}\n    </div>\n    {{additionalFeeHtml}}\n    <div class=\"total-amount\">\n      總金額：{{totalAmount}}\n    </div>\n  </div>\n\n  <div class=\"signature-section\">\n    <div class=\"signature-box\">\n      <div class=\"signature-label\">客戶簽名：</div>\n      <div class=\"signature-line\"></div>\n      <div class=\"signature-date\">日期：_____年_____月_____日</div>\n    </div>\n\n    <div class=\"signature-notes\">\n      <p><strong>注意事項：</strong></p>\n      <ul>\n        <li>此報價單有效期限為30天，逾期需重新報價</li>\n        <li>報價內容若有異動，請重新確認</li>\n        <li>簽名確認後即視為同意此報價內容</li>\n      </ul>\n    </div>\n  </div>\n\n  <div class=\"footer\">\n    此報價單由系統自動產生，列印時間：{{printDateTime}}\n  </div>\n</body>\n\n</html>`;\n"], "mappings": "AAAA,OAAO,MAAMA,kBAAkB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAyP1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}