{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { DialogModule } from 'primeng/dialog';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { FormsModule } from '@angular/forms';\nimport { MessageService } from 'primeng/api';\nimport { ToastMessage } from '../../shared/services/message.service';\nimport { LocalStorageService } from '../../shared/services/local-storage.service';\nimport { STORAGE_KEY, LANGUAGE } from '../../shared/constant/constant';\nimport { tap } from 'rxjs';\nimport { BaseFilePipe } from '../../shared/pipes/base-file.pipe';\nimport { LoadingService } from '../../shared/services/loading.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/api/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/dialog\";\nconst _c0 = (a0, a1) => ({\n  \"filter\": a0,\n  \"background-image\": a1\n});\nconst _c1 = a0 => ({\n  \"background-image\": a0\n});\nfunction HomeComponent_div_9_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"a\", 19)(2, \"div\", 20)(3, \"span\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 22);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const menu_r1 = ctx.$implicit;\n    const $index_r2 = ctx.$index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", menu_r1.routeTo)(\"ngStyle\", i0.ɵɵpureFunction2(4, _c0, !menu_r1.isFinish ? \"grayscale(1)\" : \"\", \"url(\" + menu_r1.imageSrcWeb + \")\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate($index_r2 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(menu_r1.name);\n  }\n}\nfunction HomeComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵrepeaterCreate(1, HomeComponent_div_9_For_2_Template, 7, 7, \"div\", 18, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r2.selectedMenuStep);\n  }\n}\nfunction HomeComponent_For_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"a\", 19)(2, \"div\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const menu_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", menu_r4.routeTo)(\"ngStyle\", i0.ɵɵpureFunction1(3, _c1, \"url(\" + menu_r4.imageSrcWeb + \")\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", menu_r4.name, \" \");\n  }\n}\nfunction HomeComponent_For_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"a\", 24);\n    i0.ɵɵelement(2, \"img\", 25);\n    i0.ɵɵelementStart(3, \"div\", 26);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const menu_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", menu_r5.routeTo);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", menu_r5.imageSrcMobile, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(menu_r5.name);\n  }\n}\nfunction HomeComponent_For_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"a\", 24);\n    i0.ɵɵelement(2, \"img\", 25);\n    i0.ɵɵelementStart(3, \"div\", 26);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const menu_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", menu_r6.routeTo);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", menu_r6.imageSrcMobile, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(menu_r6.name);\n  }\n}\nfunction HomeComponent_Conditional_24_Conditional_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \" \\u5BA2\\u8B8A\\u6642\\u9593\\u5DF2\\u7D50\\u675F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_Conditional_24_Conditional_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1, \" \\u63D0\\u9192\\u60A8\\uFF0C\\u4F60\\u7684\\u9078\\u6A23\\u6642\\u6BB5\\u70BA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 33);\n    i0.ɵɵtext(7, \" \\u5982\\u903E\\u671F\\u672A\\u5B8C\\u6210\\u6D41\\u7A0B\\uFF0C\\u65B0\\u5408\\u9054\\u5EFA\\u8A2D\\u5C07\\u4F9D\\u7167\\u6A23\\u54C1\\u5C4B\\u5F62\\u5F0F\\u5B8C\\u6210\\u3002 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(4, 2, ctx_r2.startedDate, \"yyyy/MM/dd\"), \" ~ \", i0.ɵɵpipeBind2(5, 5, ctx_r2.endedDate, \"yyyy/MM/dd\"), \" \");\n  }\n}\nfunction HomeComponent_Conditional_24_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, HomeComponent_Conditional_24_Conditional_1_Conditional_1_Template, 2, 0, \"div\", 29)(2, HomeComponent_Conditional_24_Conditional_1_Conditional_2_Template, 8, 8);\n    i0.ɵɵelementStart(3, \"span\", 30);\n    i0.ɵɵlistener(\"click\", function HomeComponent_Conditional_24_Conditional_1_Template_span_click_3_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.gotoAnnoucement());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r2.currentDate > ctx_r2.endedDate ? 1 : 2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.language.confirm, \"\");\n  }\n}\nfunction HomeComponent_Conditional_24_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵelement(4, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 30);\n    i0.ɵɵlistener(\"click\", function HomeComponent_Conditional_24_Conditional_2_Template_span_click_5_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.acceptChangeTime());\n    });\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.language.systemOperatingInstructions, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.systemInstruction, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.language.iUnderstand, \"\");\n  }\n}\nfunction HomeComponent_Conditional_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dialog\", 27);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function HomeComponent_Conditional_24_Template_p_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.showPopupChangeTime, $event) || (ctx_r2.showPopupChangeTime = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onHide\", function HomeComponent_Conditional_24_Template_p_dialog_onHide_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onHide());\n    });\n    i0.ɵɵtemplate(1, HomeComponent_Conditional_24_Conditional_1_Template, 5, 2, \"div\", 28)(2, HomeComponent_Conditional_24_Conditional_2_Template, 7, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r2.showPopupChangeTime);\n    i0.ɵɵproperty(\"modal\", true)(\"draggable\", false)(\"resizable\", false)(\"closable\", false)(\"showHeader\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, !ctx_r2.goNext ? 1 : 2);\n  }\n}\nexport class HomeComponent {\n  constructor(_buildCaseService, _router, houseService) {\n    this._buildCaseService = _buildCaseService;\n    this._router = _router;\n    this.houseService = houseService;\n    this.selectedMenuStep = [];\n    this.selectedMenu = [];\n    this.language = LANGUAGE.home;\n    //handle show popup change time\n    this.startedDate = new Date();\n    this.endedDate = new Date();\n    this.currentDate = new Date();\n    this.current_progress = 0;\n    this.showPopupChangeTime = false;\n    this.goNext = false;\n    this.checkIsFinishingBuilding = true;\n    this.annoucement = '';\n    this.systemInstruction = '';\n    this.imageFront = \"\";\n    this.cIsChange = false;\n    this.cPayStatus = 0;\n    this.changeTime = true;\n  }\n  ngOnInit() {\n    LoadingService.loading(true);\n    //get startdate, enddate\n    this.startedDate = new Date(LocalStorageService.GetLocalStorage(STORAGE_KEY.DATE_START));\n    this.endedDate = new Date(LocalStorageService.GetLocalStorage(STORAGE_KEY.DATE_END));\n    //check event click back to hide menu\n    let userData = LocalStorageService.GetLocalStorage(STORAGE_KEY.USER);\n    this.checkIsFinishingBuilding = userData.isFinishBuilding;\n    this.selectedMenu = this.getListMenu().filter(menu => !menu.isDisabled);\n    this.showPopupChangeTime = !userData.acceptChangeTime;\n    this.getBuildCasebyId();\n    if (this.currentDate <= this.startedDate || this.currentDate > this.endedDate) {\n      this.changeTime = false;\n      this.selectedMenu = this.getListMenuNotChangeTime();\n    }\n    let is_show = LocalStorageService.GetSessionStorage(STORAGE_KEY.IS_SHOW);\n    if (is_show == \"true\") {\n      this.onHide();\n    }\n  }\n  ngAfterViewChecked() {\n    this.getPayStatus();\n    LoadingService.loading(false);\n  }\n  onHide() {\n    this.showPopupChangeTime = false;\n  }\n  gotoAnnoucement() {\n    if (this.currentDate <= this.startedDate || this.currentDate > this.endedDate) {\n      this.showPopupChangeTime = false;\n      this.changeTime = false;\n      this.selectedMenu = this.getListMenuNotChangeTime();\n      return;\n    }\n    if (this.currentDate >= this.endedDate) {\n      this.acceptChangeTime();\n      this.changeTime = true;\n      return;\n    }\n    this.changeTime = true;\n    let current_progress = LocalStorageService.GetLocalStorage(STORAGE_KEY.CURRENT_PROGRESS);\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.IS_SHOW, 'true');\n    if (current_progress == 0) {\n      this.goNext = true;\n    } else {\n      this.onHide();\n      this.showPopupChangeTime = false;\n      let dataUser = LocalStorageService.GetLocalStorage(STORAGE_KEY.USER);\n      let payload = {\n        acceptChangeTime: true,\n        isFinishBuilding: dataUser.isFinishBuilding,\n        name: dataUser.name\n      };\n      LocalStorageService.AddLocalStorage(STORAGE_KEY.USER, payload);\n    }\n  }\n  getListMenuStep() {\n    this.current_progress = LocalStorageService.GetLocalStorage(STORAGE_KEY.CURRENT_PROGRESS);\n    let listMenu = [{\n      id: 1,\n      name: \"已閱讀操作說明\",\n      imageSrcWeb: 'assets/img1.png',\n      imageSrcMobile: 'assets/Group6.png',\n      routeTo: '/notice',\n      isDisabled: this.currentDate < this.startedDate || this.currentDate > this.endedDate,\n      isFinish: this.current_progress > 0 ? true : false\n    }, {\n      id: 2,\n      name: \"建材設備選定\",\n      routeTo: '/choice',\n      imageSrcWeb: 'assets/img2.png',\n      imageSrcMobile: 'assets/Group1.png',\n      isDisabled: this.currentDate < this.startedDate || this.currentDate > this.endedDate,\n      isFinish: this.current_progress > 1 ? true : false\n    }, {\n      id: 3,\n      name: !this.cIsChange ? \"確認簽署\" : \"簽署及繳款完成\",\n      imageSrcWeb: 'assets/img3.png',\n      imageSrcMobile: 'assets/Group4.png',\n      routeTo: '/sign',\n      isDisabled: this.currentDate < this.startedDate,\n      isFinish: !this.cIsChange ? this.current_progress > 2 ? true : false : this.current_progress > 2 && this.cPayStatus == 1 ? true : false\n    }];\n    return listMenu;\n  }\n  getListMenuNotChangeTime() {\n    let listMenu = [{\n      id: 4,\n      name: \"洽談紀錄\",\n      routeTo: '/history',\n      imageSrcWeb: 'assets/img4.png',\n      imageSrcMobile: 'assets/Group2.png',\n      isDisabled: this.currentDate < this.startedDate || this.currentDate > this.endedDate,\n      isFinish: false\n    }, {\n      id: 6,\n      name: \"建案公佈欄\",\n      imageSrcWeb: 'assets/img6.png',\n      imageSrcMobile: 'assets/Group5.png',\n      routeTo: '/resource',\n      isDisabled: false,\n      isFinish: false\n    }, {\n      id: 7,\n      name: \"聯絡資料修改\",\n      imageSrcWeb: 'assets/img7.png',\n      imageSrcMobile: 'assets/Group7.png',\n      routeTo: '/edit-info',\n      isDisabled: false,\n      isFinish: false\n    }\n    // {\n    //   id: 8,\n    //   name: \"更改密碼\",\n    //   imageSrcWeb: 'assets/img7.png',\n    //   imageSrcMobile: 'assets/Group7.png',\n    //   routeTo: '/change-password',\n    //   isDisabled: false,\n    //   isFinish: false\n    // }\n    ];\n    return listMenu;\n  }\n  getListMenu() {\n    let listMenu = [{\n      id: 4,\n      name: \"洽談紀錄\",\n      routeTo: '/history',\n      imageSrcWeb: 'assets/img4.png',\n      imageSrcMobile: 'assets/Group2.png',\n      isDisabled: this.currentDate < this.startedDate || this.currentDate > this.endedDate,\n      isFinish: false\n    }, {\n      id: 5,\n      name: \"預約客變時段\",\n      imageSrcWeb: 'assets/img5.png',\n      imageSrcMobile: 'assets/Group3.png',\n      routeTo: '/reserve',\n      isDisabled: false,\n      isFinish: false\n    }, {\n      id: 6,\n      name: \"建案公佈欄\",\n      imageSrcWeb: 'assets/img6.png',\n      imageSrcMobile: 'assets/Group5.png',\n      routeTo: '/resource',\n      isDisabled: false,\n      isFinish: false\n    }, {\n      id: 7,\n      name: \"聯絡資料修改\",\n      imageSrcWeb: 'assets/img7.png',\n      imageSrcMobile: 'assets/Group7.png',\n      routeTo: '/edit-info',\n      isDisabled: false,\n      isFinish: false\n    }, {\n      id: 8,\n      name: \"更改密碼\",\n      imageSrcWeb: 'assets/img7.png',\n      imageSrcMobile: 'assets/Group7.png',\n      routeTo: '/change-password',\n      isDisabled: false,\n      isFinish: false\n    }, {\n      id: 9,\n      name: \"報價單\",\n      imageSrcWeb: 'assets/img7.png',\n      imageSrcMobile: 'assets/Group7.png',\n      routeTo: '/quotation',\n      isDisabled: false,\n      isFinish: false\n    }];\n    return listMenu;\n  }\n  getBuildCasebyId() {\n    this._buildCaseService.apiBuildCaseGetBuildCaseByIdPost$Json({\n      body: {\n        CBuildCaseID: LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).buildCaseId\n      }\n    }).pipe(tap(res => {\n      this.imageFront = res.Entries?.CFrontImage;\n      this.systemInstruction = res.Entries?.CSystemInstruction;\n    })).subscribe();\n  }\n  logout() {\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.DATE_END);\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.DATE_START);\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.TOKEN);\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.USER);\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.NUMBER_STEP);\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.CURRENT_PROGRESS);\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.SIGN_INFO);\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.HOUSE_PROGRESS);\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.IS_CHANGE);\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.PAY_STATUS);\n    this._router.navigate(['login']);\n  }\n  acceptChangeTime() {\n    this.updateProgress(1);\n    this.showPopupChangeTime = false;\n    let dataUser = LocalStorageService.GetLocalStorage(STORAGE_KEY.USER);\n    let payload = {\n      acceptChangeTime: true,\n      isFinishBuilding: dataUser.isFinishBuilding,\n      name: dataUser.name\n    };\n    LocalStorageService.AddLocalStorage(STORAGE_KEY.USER, payload);\n  }\n  getPayStatus() {\n    this.cIsChange = LocalStorageService.GetLocalStorage(STORAGE_KEY.IS_CHANGE);\n    this.cPayStatus = LocalStorageService.GetLocalStorage(STORAGE_KEY.PAY_STATUS);\n    this.selectedMenuStep = this.getListMenuStep().filter(menu => !menu.isDisabled);\n  }\n  updateProgress(progress) {\n    LoadingService.loading(true);\n    this.houseService.apiHouseUpdateHouseProgressPost$Json({\n      body: progress\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0 && res.Message == \"Success\") {\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.CURRENT_PROGRESS, progress);\n      }\n    })).subscribe();\n  }\n  static #_ = this.ɵfac = function HomeComponent_Factory(t) {\n    return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.BuildCaseService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i1.HouseService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeComponent,\n    selectors: [[\"app-home\"]],\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([MessageService, ToastMessage]), i0.ɵɵStandaloneFeature],\n    decls: 25,\n    vars: 5,\n    consts: [[1, \"wrapper\", \"web-ver\"], [1, \"content\", \"!w-full\"], [1, \"flex\", \"flex-col\", \"md:pb-20\", \"bg-white\"], [1, \"self-center\", \"mt-10\", \"w-full\", \"max-w-[1216px]\", \"max-md:mt-10\", \"max-md:max-w-full\"], [1, \"flex\", \"!min-h-[60vh]\", \"mx-3\"], [1, \"w-1/3\", \"pr-4\"], [\"loading\", \"lazy\", 1, \"fit-size\", \"imageBuildcase\", \"!w-full\", \"!h-full\", 3, \"src\"], [1, \"w-2/3\"], [\"class\", \"grid grid-cols-3 gap-4 max-lg:w-full max-sm:!grid-cols-2\", 4, \"ngIf\"], [2, \"height\", \"20px\"], [1, \"grid\", \"grid-cols-4\", \"gap-4\", \"max-lg:w-full\", \"max-sm:!grid-cols-2\"], [1, \"w-full\", \"relative\", \"item-home-child\"], [1, \"wrapper\", \"mobile-ver\"], [1, \"self-center\", \"mt-5\", \"w-full\", \"max-w-[1216px]\", \"max-md:mt-5\", \"max-md:max-w-full\"], [1, \"grid\", \"grid-cols-2\", \"gap-4\", \"max-lg:w-full\"], [1, \"w-full\", \"relative\", \"!h-full\"], [3, \"visible\", \"modal\", \"draggable\", \"resizable\", \"closable\", \"showHeader\"], [1, \"grid\", \"grid-cols-3\", \"gap-4\", \"max-lg:w-full\", \"max-sm:!grid-cols-2\"], [1, \"w-full\", \"relative\", \"item-home\"], [3, \"routerLink\", \"ngStyle\"], [1, \"flex\", \"flex-row\", \"text-[#231815]\", \"text-base\", \"item-text\"], [1, \"number-circle\"], [1, \"label-text\"], [1, \"label-text-child\", \"text-[#231815]\", \"text-base\"], [3, \"routerLink\"], [1, \"!h-full\", \"rounded\", \"border-2\", 2, \"border\", \"1px solid #dbe6ea\", 3, \"src\"], [1, \"absolute\", \"top-1/2\", \"left-1/2\", \"text-[#231815]\", \"text-base\", \"text-center\", 2, \"transform\", \"translate(-50%, 200%)\", \"width\", \"150px\"], [3, \"visibleChange\", \"onHide\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"closable\", \"showHeader\"], [1, \"container\", \"max-sm:!w-[340px]\", \"max-md:!w-[450px]\", \"flex\", \"flex-col\", 2, \"height\", \"265px\"], [1, \"zone-date\", 2, \"font-size\", \"30px !important\"], [1, \"button3\", 3, \"click\"], [1, \"text-2xl\", \"font-bold\", \"text-stone-900\", \"text-center\"], [1, \"zone-date\", \"p-[16px]\", \"mt-[16px]\", \"font-bold\", \"text-center\"], [1, \"mt-[16px]\", \"text-center\", \"text-base\"], [1, \"container\", \"max-sm:!w-[340px]\", \"max-md:!w-[450px]\", \"flex\", \"flex-col\", 2, \"height\", \"500px\"], [1, \"text-2xl\", \"self-center\", \"font-bold\", \"text-stone-900\"], [1, \"annoucement\"], [2, \"white-space\", \"pre-wrap\", 3, \"innerHTML\"]],\n    template: function HomeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵelement(6, \"img\", 6);\n        i0.ɵɵpipe(7, \"addBaseFile\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵtemplate(9, HomeComponent_div_9_Template, 3, 0, \"div\", 8);\n        i0.ɵɵelement(10, \"div\", 9);\n        i0.ɵɵelementStart(11, \"div\", 10);\n        i0.ɵɵrepeaterCreate(12, HomeComponent_For_13_Template, 4, 5, \"div\", 11, i0.ɵɵrepeaterTrackByIndex);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(14, \"div\", 12)(15, \"div\", 1)(16, \"div\", 2)(17, \"div\", 13)(18, \"div\", 4)(19, \"div\", 14);\n        i0.ɵɵrepeaterCreate(20, HomeComponent_For_21_Template, 5, 3, \"div\", 15, i0.ɵɵrepeaterTrackByIndex);\n        i0.ɵɵrepeaterCreate(22, HomeComponent_For_23_Template, 5, 3, \"div\", 15, i0.ɵɵrepeaterTrackByIndex);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵtemplate(24, HomeComponent_Conditional_24_Template, 3, 7, \"p-dialog\", 16);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(7, 3, ctx.imageFront), i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.changeTime);\n        i0.ɵɵadvance(3);\n        i0.ɵɵrepeater(ctx.selectedMenu);\n        i0.ɵɵadvance(8);\n        i0.ɵɵrepeater(ctx.selectedMenuStep);\n        i0.ɵɵadvance(2);\n        i0.ɵɵrepeater(ctx.selectedMenu);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(24, ctx.startedDate && ctx.endedDate ? 24 : -1);\n      }\n    },\n    dependencies: [RouterModule, i2.RouterLink, CommonModule, i3.NgIf, i3.NgStyle, i3.DatePipe, DialogModule, i4.Dialog, CheckboxModule, FormsModule, BaseFilePipe],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}html[_ngcontent-%COMP%]{line-height:initial;-webkit-text-size-adjust:100%;width:100%;height:100%}body[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:#23181599;font-family:Noto Sans TC!important;box-sizing:border-box;width:100%;height:auto;overflow:auto;overscroll-behavior:none;letter-spacing:.32px}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{-webkit-text-size-adjust:none;text-size-adjust:none}main[_ngcontent-%COMP%]{display:block}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:400}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible;margin:10px 0;border:none;border-top:1px solid #000}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1rem}a[_ngcontent-%COMP%]{background-color:transparent;text-decoration:none;-webkit-tap-highlight-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:500}strong[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:600}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}p[_ngcontent-%COMP%]{font-size:1rem}img[_ngcontent-%COMP%]{border-style:none;max-width:100%}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:Noto Sans TC,\\\\5fae\\\\8edf\\\\6b63\\\\9ed1\\\\9ad4,Arial,sans-serif;font-size:1rem;line-height:1.15;margin:0;color:#2f2f2f;-webkit-tap-highlight-color:transparent;letter-spacing:2px}select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]{padding:0;background-color:transparent;border:none;cursor:pointer}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}div[role=tab][_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]{border-spacing:0px}ol[_ngcontent-%COMP%], ul[_ngcontent-%COMP%]{padding-left:0;margin-top:0}.form-check-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:none}.container[_ngcontent-%COMP%]{width:100%}@media (min-width: 640px){.container[_ngcontent-%COMP%]{max-width:640px}}@media (min-width: 768px){.container[_ngcontent-%COMP%]{max-width:768px}}@media (min-width: 1024px){.container[_ngcontent-%COMP%]{max-width:1024px}}@media (min-width: 1280px){.container[_ngcontent-%COMP%]{max-width:1280px}}@media (min-width: 1536px){.container[_ngcontent-%COMP%]{max-width:1536px}}.visible[_ngcontent-%COMP%]{visibility:visible}.collapse[_ngcontent-%COMP%]{visibility:collapse}.static[_ngcontent-%COMP%]{position:static}.absolute[_ngcontent-%COMP%]{position:absolute}.relative[_ngcontent-%COMP%]{position:relative}.inset-y-0[_ngcontent-%COMP%]{top:0;bottom:0}.bottom-0[_ngcontent-%COMP%]{bottom:0}.left-0[_ngcontent-%COMP%]{left:0}.left-1\\\\/2[_ngcontent-%COMP%]{left:50%}.left-3[_ngcontent-%COMP%]{left:.75rem}.right-0[_ngcontent-%COMP%]{right:0}.top-1[_ngcontent-%COMP%]{top:.25rem}.top-1\\\\/2[_ngcontent-%COMP%]{top:50%}.top-3[_ngcontent-%COMP%]{top:.75rem}.m-0[_ngcontent-%COMP%]{margin:0}.m-2[_ngcontent-%COMP%]{margin:.5rem}.m-auto[_ngcontent-%COMP%]{margin:auto}.mx-3[_ngcontent-%COMP%]{margin-left:.75rem;margin-right:.75rem}.mx-4[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.mx-5[_ngcontent-%COMP%]{margin-left:1.25rem;margin-right:1.25rem}.mx-\\\\__ph-0__[_ngcontent-%COMP%]{margin-left:20%;margin-right:20%}.my-12[_ngcontent-%COMP%]{margin-top:3rem;margin-bottom:3rem}.my-2[_ngcontent-%COMP%]{margin-top:.5rem;margin-bottom:.5rem}.my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.my-6[_ngcontent-%COMP%]{margin-top:1.5rem;margin-bottom:1.5rem}.\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:.75rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ml-1[_ngcontent-%COMP%]{margin-left:.25rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.ml-4[_ngcontent-%COMP%]{margin-left:1rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-4[_ngcontent-%COMP%]{margin-right:1rem}.mr-5[_ngcontent-%COMP%]{margin-right:1.25rem}.mr-8[_ngcontent-%COMP%]{margin-right:2rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.mt-14[_ngcontent-%COMP%]{margin-top:3.5rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:.75rem}.mt-4[_ngcontent-%COMP%]{margin-top:1rem}.mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.mt-6[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-7[_ngcontent-%COMP%]{margin-top:1.75rem}.mt-8[_ngcontent-%COMP%]{margin-top:2rem}.mt-\\\\__ph-0__[_ngcontent-%COMP%]{margin-top:16px}.block[_ngcontent-%COMP%]{display:block}.flex[_ngcontent-%COMP%]{display:flex}.table[_ngcontent-%COMP%]{display:table}.table-row[_ngcontent-%COMP%]{display:table-row}.grid[_ngcontent-%COMP%]{display:grid}.\\\\!h-\\\\__ph-0__[_ngcontent-%COMP%]{height:107px!important}.\\\\!h-full[_ngcontent-%COMP%]{height:100%!important}.h-28[_ngcontent-%COMP%]{height:7rem}.h-32[_ngcontent-%COMP%]{height:8rem}.h-6[_ngcontent-%COMP%]{height:1.5rem}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:100px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:145px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:280px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:31px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:40vh}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:47px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:480px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:4px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:50px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:55px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:665px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:80px}.h-auto[_ngcontent-%COMP%]{height:auto}.h-fit[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content}.h-full[_ngcontent-%COMP%]{height:100%}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:200px}.\\\\!min-h-\\\\__ph-0__[_ngcontent-%COMP%]{min-height:60vh!important}.\\\\!w-40[_ngcontent-%COMP%]{width:10rem!important}.\\\\!w-48[_ngcontent-%COMP%]{width:12rem!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:107px!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:120px!important}.\\\\!w-full[_ngcontent-%COMP%]{width:100%!important}.w-1\\\\/3[_ngcontent-%COMP%]{width:33.333333%}.w-2\\\\/3[_ngcontent-%COMP%]{width:66.666667%}.w-28[_ngcontent-%COMP%]{width:7rem}.w-6[_ngcontent-%COMP%]{width:1.5rem}.w-7\\\\/12[_ngcontent-%COMP%]{width:58.333333%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:100%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:150px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:160px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:309px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:30rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:400px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:45%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:485px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:50%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:550px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:660px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:760px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:88px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:968px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:97%}.w-full[_ngcontent-%COMP%]{width:100%}.\\\\!max-w-full[_ngcontent-%COMP%]{max-width:100%!important}.max-w-4xl[_ngcontent-%COMP%]{max-width:56rem}.max-w-\\\\__ph-0__[_ngcontent-%COMP%]{max-width:1216px}.flex-shrink[_ngcontent-%COMP%]{flex-shrink:1}.grow[_ngcontent-%COMP%]{flex-grow:1}.border-collapse[_ngcontent-%COMP%]{border-collapse:collapse}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.resize-none[_ngcontent-%COMP%]{resize:none}.grid-cols-10[_ngcontent-%COMP%]{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}.grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}.flex-row[_ngcontent-%COMP%]{flex-direction:row}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.items-start[_ngcontent-%COMP%]{align-items:flex-start}.items-center[_ngcontent-%COMP%]{align-items:center}.justify-start[_ngcontent-%COMP%]{justify-content:flex-start}.justify-end[_ngcontent-%COMP%]{justify-content:flex-end}.justify-center[_ngcontent-%COMP%]{justify-content:center}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.gap-x-1[_ngcontent-%COMP%]{column-gap:.25rem}.self-center[_ngcontent-%COMP%]{align-self:center}.overflow-hidden[_ngcontent-%COMP%]{overflow:hidden}.overflow-y-scroll[_ngcontent-%COMP%]{overflow-y:scroll}.rounded[_ngcontent-%COMP%]{border-radius:.25rem}.rounded-3xl[_ngcontent-%COMP%]{border-radius:1.5rem}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.rounded-lg[_ngcontent-%COMP%]{border-radius:.5rem}.rounded-md[_ngcontent-%COMP%]{border-radius:.375rem}.border[_ngcontent-%COMP%]{border-width:1px}.border-2[_ngcontent-%COMP%]{border-width:2px}.border-solid[_ngcontent-%COMP%]{border-style:solid}.border-blue-400[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(96 165 250 / var(--tw-border-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 143 199 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(184 166 118 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea80}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea99}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(217 217 217 / var(--tw-bg-opacity))}.bg-gray-500[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(107 114 128 / var(--tw-bg-opacity))}.bg-white[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.bg-opacity-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: .04}.object-cover[_ngcontent-%COMP%]{object-fit:cover}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-4[_ngcontent-%COMP%]{padding:1rem}.p-\\\\__ph-0__[_ngcontent-%COMP%]{padding:16px}.px-3[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-6[_ngcontent-%COMP%]{padding-left:1.5rem;padding-right:1.5rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.py-2[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.pb-4[_ngcontent-%COMP%]{padding-bottom:1rem}.pb-5[_ngcontent-%COMP%]{padding-bottom:1.25rem}.pb-8[_ngcontent-%COMP%]{padding-bottom:2rem}.pl-4[_ngcontent-%COMP%]{padding-left:1rem}.pl-6[_ngcontent-%COMP%]{padding-left:1.5rem}.pr-1[_ngcontent-%COMP%]{padding-right:.25rem}.pr-3[_ngcontent-%COMP%]{padding-right:.75rem}.pr-4[_ngcontent-%COMP%]{padding-right:1rem}.text-left[_ngcontent-%COMP%]{text-align:left}.\\\\!text-center[_ngcontent-%COMP%]{text-align:center!important}.text-center[_ngcontent-%COMP%]{text-align:center}.text-right[_ngcontent-%COMP%]{text-align:right}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-3xl[_ngcontent-%COMP%]{font-size:1.875rem;line-height:2.25rem}.text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xl[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-light[_ngcontent-%COMP%]{font-weight:300}.font-medium[_ngcontent-%COMP%]{font-weight:500}.font-normal[_ngcontent-%COMP%]{font-weight:400}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.tracking-wider[_ngcontent-%COMP%]{letter-spacing:.05em}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 143 199 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(35 24 21 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(182 152 103 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(184 166 118 / var(--tw-text-opacity))}.text-black[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-blue-400[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.text-gray-500[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-stone-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(87 83 78 / var(--tw-text-opacity))}.text-stone-900[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(28 25 23 / var(--tw-text-opacity))}.text-white[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.shadow[_ngcontent-%COMP%]{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.grayscale[_ngcontent-%COMP%]{--tw-grayscale: grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter[_ngcontent-%COMP%]{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.content[_ngcontent-%COMP%]{width:100%}@media screen and (max-width: 1024px){.content[_ngcontent-%COMP%]{width:100%;padding:0 16px 50px}}.pc[_ngcontent-%COMP%], .inpc[_ngcontent-%COMP%]{display:initial}@media screen and (max-width: 1024px){.inpc[_ngcontent-%COMP%]{display:none}}.flat[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 1024px){.flat[_ngcontent-%COMP%]{display:initial}}.mb[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.mb[_ngcontent-%COMP%]{display:initial}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{position:relative}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background-image:url(/assets/background/background2.png);width:180px;height:150px;background-size:unset;background-position:left;z-index:-1;right:0;bottom:20px}.button1[_ngcontent-%COMP%]{width:200px;height:47px;border:1px solid #CDCDCD;color:#3a4246;border-radius:28px;background-origin:border-box;background-clip:content-box,border-box;transition:all .3s ease}.button1[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#b8a676;transition:color .3s ease}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#ae9b66}@media screen and (max-width: 912px){.button1[_ngcontent-%COMP%]{width:100%}}.button2[_ngcontent-%COMP%]{width:200px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button2[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button2[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button2[_ngcontent-%COMP%]{width:100%}}.button3[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button3[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button3[_ngcontent-%COMP%]{width:100%}}.button4[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:#979797;border-radius:24px;transition:all .3s ease}.button4[_ngcontent-%COMP%]:hover{background:#7e7e7e}.button4[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button4[_ngcontent-%COMP%]{width:100%}}.checkbox-zone[_ngcontent-%COMP%]{padding:2px;border-radius:2px;position:relative;background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 1px 4px #ae9b664d}.avoid-resize-img[_ngcontent-%COMP%]{max-width:none}.fit-size[_ngcontent-%COMP%]{height:inherit;width:inherit;object-fit:cover;object-position:center}.p-calendar[_ngcontent-%COMP%]{width:100%}.p-calendar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#23181599}@media screen and (max-width: 912px){.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]{border:none;width:100%}.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#231815}@media screen and (max-width: 912px){.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]   .p-dropdown-trigger[_ngcontent-%COMP%]{border-radius:0;border-bottom:1px solid rgba(144,150,157,.4)}.p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{box-shadow:none}.p-radiobutton[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]{width:22px;height:22px;border:2px solid #CDCDCD;background-color:#fff;border-radius:50%;transition:all .3s cubic-bezier(.25,.46,.45,.94);position:relative;box-shadow:0 1px 3px #ae9b661a;cursor:pointer}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:hover{border-color:#b8a676;background:radial-gradient(circle at center,#b8a67614,#b8a67605);transform:scale(1.05);box-shadow:0 2px 8px #ae9b6626}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #b8a67640,0 2px 8px #ae9b6633;border-color:#a39460}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:active{transform:scale(.98)}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-color:#ae9b66;box-shadow:0 2px 12px #ae9b6640,inset 0 1px 2px #fff3}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:8px;height:8px;background:radial-gradient(circle,#fff,#ffffffe6);border-radius:50%;transform:translate(-50%,-50%) scale(0);box-shadow:0 1px 2px #0003;animation:_ngcontent-%COMP%_radioAppear .2s ease forwards}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);border-color:#9b8a5a;transform:scale(1.05);box-shadow:0 3px 15px #ae9b6659,inset 0 1px 2px #ffffff40}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #b8a6764d,0 3px 15px #ae9b6659}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-icon[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.2);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{transform:translate(-50%,-50%) scale(0);opacity:.6}to{transform:translate(-50%,-50%) scale(4);opacity:0}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background-color:#fff;color:#846a52;font-size:16px;border:1px solid #E6F0F3;border-radius:6px;padding:16px 32px}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 32px 8px;border-bottom:none!important;border-bottom-right-radius:0;border-bottom-left-radius:0}@media screen and (max-width: 912px){.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(-180deg)!important;transition:transform .3s linear}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(0)!important;transition:transform .3s linear}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]:focus{box-shadow:none}.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 32px 16px}@media screen and (max-width: 912px){.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 16px 16px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{width:24px;height:24px}.p-dialog-mask[_ngcontent-%COMP%]{width:100%;height:100%;background-color:#0006;top:0;z-index:20;pointer-events:all!important}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]{padding:32px 24px 16px;color:#231815;font-size:24px;text-align:center;display:flex;justify-content:center}.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]{padding:32px 24px 0;background:linear-gradient(180deg,#f3f1ea,#fff)}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]{display:none}.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]{padding:24px 24px 40px;display:flex;justify-content:center}.p-checkbox[_ngcontent-%COMP%]{width:18px;height:18px;display:inline-flex;align-items:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]{width:18px;height:18px;border:2px solid #CDCDCD;border-radius:3px;transition:.3s ease;background:#fff;position:relative;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:hover{border-color:#b8a676}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:focus{outline:none;border-color:#b8a676;box-shadow:0 0 0 2px #b8a67633}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]{background:#b8a676!important;border-color:#b8a676!important}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{color:#fff!important;font-size:12px;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:not(.p-highlight)   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(0);opacity:0}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]:before{content:\\\"\\\\e915\\\";font-family:primeicons;font-weight:400;font-style:normal;font-size:12px;display:inline-block}.p-checkbox-label[_ngcontent-%COMP%]{margin-left:8px;cursor:pointer;-webkit-user-select:none;user-select:none}.input[_ngcontent-%COMP%]{width:100%;border:none;padding:12.5px 0;border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px;height:4px}[_ngcontent-%COMP%]::-webkit-scrollbar-button{background:transparent;border-radius:2px}[_ngcontent-%COMP%]::-webkit-scrollbar-track-piece{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:4px;background-color:#90969d33}[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:transparent}.p-radiobutton-label[_ngcontent-%COMP%]{color:#231815!important;font-weight:400;cursor:pointer;transition:all .3s ease}.p-radiobutton-label[_ngcontent-%COMP%]:hover{color:#ae9b66!important}label[for*=requirement_][_ngcontent-%COMP%]{color:#231815!important;font-weight:400}label[for*=requirement_][_ngcontent-%COMP%]:hover{color:#ae9b66!important}input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%], .p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%] + label[_ngcontent-%COMP%]{color:#ae9b66!important;font-weight:600}@media screen and (max-width: 912px){.content[_ngcontent-%COMP%]{padding-bottom:0}}.container[_ngcontent-%COMP%]{width:650px}.container[_ngcontent-%COMP%]   .zone-date[_ngcontent-%COMP%]{color:#b8a676;font-size:24px;background:#f3f1eae6;border-radius:8px;padding:8px 16px}.container[_ngcontent-%COMP%]   .annoucement[_ngcontent-%COMP%]{margin-top:2rem;height:300px;overflow:auto;scroll-behavior:smooth;color:#000;font-size:14px}.container[_ngcontent-%COMP%]   .button3[_ngcontent-%COMP%]{margin-top:24px;align-self:center;text-align:center;cursor:pointer}.item-home[_ngcontent-%COMP%]{height:160px}.item-home-child[_ngcontent-%COMP%]{height:106px}.item-home[_ngcontent-%COMP%]   a[_ngcontent-%COMP%], .item-home-child[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:block;width:100%;height:100%;background-size:cover;background-position:center;border:1px solid #dbe6ea;border-radius:5px}.item-text[_ngcontent-%COMP%]{position:absolute;top:10px;width:100%;padding:0 10px}.label-text[_ngcontent-%COMP%]{padding-left:10px;line-height:40px;font-weight:700;font-size:24px}.label-text-child[_ngcontent-%COMP%]{position:absolute;bottom:10px;font-weight:700;font-size:20px;width:100%;text-align:center}.number-circle[_ngcontent-%COMP%]{width:40px;height:40px;background:#b8a676;border-radius:50%;display:flex;justify-content:center;align-items:center;font-size:20px;box-shadow:0 2px 8px #b8a6764d;transition:all .3s ease;color:#fff;font-weight:700}.number-circle[_ngcontent-%COMP%]:hover{background:#ae9b66;transform:scale(1.05);box-shadow:0 4px 12px #b8a67666}.web-ver[_ngcontent-%COMP%]{display:block}.mobile-ver[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.web-ver[_ngcontent-%COMP%]{display:none}.mobile-ver[_ngcontent-%COMP%]{display:block}}@media not all and (min-width: 1280px){.max-xl\\\\:grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}}@media not all and (min-width: 1024px){.max-lg\\\\:bottom-12[_ngcontent-%COMP%]{bottom:3rem}.max-lg\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-lg\\\\:grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}}@media not all and (min-width: 768px){.max-md\\\\:bottom-14[_ngcontent-%COMP%]{bottom:3.5rem}.max-md\\\\:my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.max-md\\\\:ml-0[_ngcontent-%COMP%]{margin-left:0}.max-md\\\\:mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.max-md\\\\:mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.max-md\\\\:hidden[_ngcontent-%COMP%]{display:none}.max-md\\\\:h-\\\\__ph-0__[_ngcontent-%COMP%]{height:65%}.max-md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:450px!important}.max-md\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:500px}.max-md\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-md\\\\:max-w-full[_ngcontent-%COMP%]{max-width:100%}.max-md\\\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.max-md\\\\:flex-col[_ngcontent-%COMP%]{flex-direction:column}.max-md\\\\:flex-col-reverse[_ngcontent-%COMP%]{flex-direction:column-reverse}.max-md\\\\:flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.max-md\\\\:items-start[_ngcontent-%COMP%]{align-items:flex-start}}@media not all and (min-width: 640px){.max-sm\\\\:bottom-10[_ngcontent-%COMP%]{bottom:2.5rem}.max-sm\\\\:ml-8[_ngcontent-%COMP%]{margin-left:2rem}.max-sm\\\\:block[_ngcontent-%COMP%]{display:block}.max-sm\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:340px!important}.max-sm\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:380px}.max-sm\\\\:\\\\!grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))!important}.max-sm\\\\:grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.max-sm\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.max-sm\\\\:gap-0[_ngcontent-%COMP%]{gap:0px}.max-sm\\\\:px-0[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.max-sm\\\\:pl-0[_ngcontent-%COMP%]{padding-left:0}.max-sm\\\\:text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}}@media (min-width: 640px){.sm\\\\:mx-0[_ngcontent-%COMP%]{margin-left:0;margin-right:0}.sm\\\\:mb-0[_ngcontent-%COMP%]{margin-bottom:0}.sm\\\\:mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.sm\\\\:flex[_ngcontent-%COMP%]{display:flex}.sm\\\\:items-center[_ngcontent-%COMP%]{align-items:center}.sm\\\\:pl-3[_ngcontent-%COMP%]{padding-left:.75rem}}@media (min-width: 768px){.md\\\\:flex[_ngcontent-%COMP%]{display:flex}.md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:634px!important}.md\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.md\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.md\\\\:pb-20[_ngcontent-%COMP%]{padding-bottom:5rem}}@media (min-width: 1024px){.lg\\\\:\\\\!mx-0[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.lg\\\\:\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.lg\\\\:flex[_ngcontent-%COMP%]{display:flex}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:800px!important}.lg\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.lg\\\\:\\\\!px-4[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.lg\\\\:\\\\!px-\\\\__ph-0__[_ngcontent-%COMP%]{padding-left:22px!important;padding-right:22px!important}.lg\\\\:\\\\!py-\\\\__ph-0__[_ngcontent-%COMP%]{padding-top:18px!important;padding-bottom:18px!important}.lg\\\\:text-center[_ngcontent-%COMP%]{text-align:center}}\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "DialogModule", "CheckboxModule", "FormsModule", "MessageService", "ToastMessage", "LocalStorageService", "STORAGE_KEY", "LANGUAGE", "tap", "BaseFilePipe", "LoadingService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "menu_r1", "routeTo", "ɵɵpureFunction2", "_c0", "is<PERSON><PERSON><PERSON>", "imageSrcWeb", "ɵɵtextInterpolate", "$index_r2", "name", "ɵɵrepeaterCreate", "HomeComponent_div_9_For_2_Template", "ɵɵrepeaterTrackByIndex", "ɵɵrepeater", "ctx_r2", "selectedMenuStep", "menu_r4", "ɵɵpureFunction1", "_c1", "ɵɵtextInterpolate1", "ɵɵelement", "menu_r5", "imageSrcMobile", "ɵɵsanitizeUrl", "menu_r6", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "startedDate", "endedDate", "ɵɵtemplate", "HomeComponent_Conditional_24_Conditional_1_Conditional_1_Template", "HomeComponent_Conditional_24_Conditional_1_Conditional_2_Template", "ɵɵlistener", "HomeComponent_Conditional_24_Conditional_1_Template_span_click_3_listener", "ɵɵrestoreView", "_r8", "ɵɵnextContext", "ɵɵresetView", "gotoAnnoucement", "ɵɵconditional", "currentDate", "language", "confirm", "HomeComponent_Conditional_24_Conditional_2_Template_span_click_5_listener", "_r9", "acceptChangeTime", "systemOperatingInstructions", "systemInstruction", "ɵɵsanitizeHtml", "iUnderstand", "ɵɵtwoWayListener", "HomeComponent_Conditional_24_Template_p_dialog_visibleChange_0_listener", "$event", "_r7", "ɵɵtwoWayBindingSet", "showPopupChangeTime", "HomeComponent_Conditional_24_Template_p_dialog_onHide_0_listener", "onHide", "HomeComponent_Conditional_24_Conditional_1_Template", "HomeComponent_Conditional_24_Conditional_2_Template", "ɵɵtwoWayProperty", "goNext", "HomeComponent", "constructor", "_buildCaseService", "_router", "houseService", "selected<PERSON><PERSON>u", "home", "Date", "current_progress", "checkIsFinishingBuilding", "annoucement", "imageFront", "cIsChange", "cPayStatus", "changeTime", "ngOnInit", "loading", "GetLocalStorage", "DATE_START", "DATE_END", "userData", "USER", "isFinishBuilding", "getListMenu", "filter", "menu", "isDisabled", "getBuildCasebyId", "getListMenuNotChangeTime", "is_show", "GetSessionStorage", "IS_SHOW", "ngAfterViewChecked", "getPayStatus", "CURRENT_PROGRESS", "AddSessionStorage", "dataUser", "payload", "AddLocalStorage", "getListMenuStep", "listMenu", "id", "apiBuildCaseGetBuildCaseByIdPost$Json", "body", "CBuildCaseID", "SAVE_LOGIN", "buildCaseId", "pipe", "res", "Entries", "CFrontImage", "CSystemInstruction", "subscribe", "logout", "RemoveLocalStorage", "TOKEN", "NUMBER_STEP", "SIGN_INFO", "HOUSE_PROGRESS", "IS_CHANGE", "PAY_STATUS", "navigate", "updateProgress", "progress", "apiHouseUpdateHouseProgressPost$Json", "StatusCode", "Message", "_", "ɵɵdirectiveInject", "i1", "BuildCaseService", "i2", "Router", "HouseService", "_2", "selectors", "standalone", "features", "ɵɵProvidersFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_div_9_Template", "HomeComponent_For_13_Template", "HomeComponent_For_21_Template", "HomeComponent_For_23_Template", "HomeComponent_Conditional_24_Template", "ɵɵpipeBind1", "RouterLink", "i3", "NgIf", "NgStyle", "DatePipe", "i4", "Dialog", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { AfterContentChecked, AfterViewChecked, Component, OnInit } from '@angular/core';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport { Menu } from '../../../model/menu.model';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ToastMessage } from '../../shared/services/message.service';\r\nimport { LocalStorageService } from '../../shared/services/local-storage.service';\r\nimport { STORAGE_KEY, UserInfo, LANGUAGE } from '../../shared/constant/constant';\r\nimport { tap } from 'rxjs';\r\nimport { BaseFilePipe } from '../../shared/pipes/base-file.pipe';\r\nimport { BuildCaseService, HouseService } from '../../../services/api/services';\r\nimport { LoadingService } from '../../shared/services/loading.service';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  standalone: true,\r\n  imports: [\r\n    RouterModule,\r\n    CommonModule,\r\n    DialogModule,\r\n    CheckboxModule,\r\n    FormsModule,\r\n    BaseFilePipe\r\n  ],\r\n  providers: [\r\n    MessageService,\r\n    ToastMessage,\r\n  ],\r\n  templateUrl: './home.component.html',\r\n  styleUrl: './home.component.scss'\r\n})\r\nexport class HomeComponent implements OnInit, AfterViewChecked {\r\n  selectedMenuStep: Menu[] = []\r\n  selectedMenu: Menu[] = []\r\n  language = LANGUAGE.home\r\n  //handle show popup change time\r\n  startedDate = new Date()\r\n  endedDate = new Date()\r\n  currentDate = new Date()\r\n  current_progress = 0;\r\n  showPopupChangeTime: boolean = false;\r\n  goNext: boolean = false;\r\n  checkIsFinishingBuilding: boolean = true;\r\n  annoucement: string = ''\r\n  systemInstruction: string | any = ''\r\n  imageFront: string = \"\"\r\n  cIsChange: boolean = false;\r\n  cPayStatus: number = 0;\r\n  changeTime: boolean = true;\r\n\r\n  constructor(\r\n    private _buildCaseService: BuildCaseService,\r\n    private _router: Router,\r\n    private houseService: HouseService\r\n  ) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    LoadingService.loading(true);\r\n    //get startdate, enddate\r\n    this.startedDate = new Date(LocalStorageService.GetLocalStorage(STORAGE_KEY.DATE_START))\r\n    this.endedDate = new Date(LocalStorageService.GetLocalStorage(STORAGE_KEY.DATE_END))\r\n\r\n    //check event click back to hide menu\r\n    let userData = LocalStorageService.GetLocalStorage(STORAGE_KEY.USER) as UserInfo\r\n    this.checkIsFinishingBuilding = userData.isFinishBuilding\r\n    this.selectedMenu = this.getListMenu().filter(menu => !menu.isDisabled)\r\n    this.showPopupChangeTime = !userData.acceptChangeTime;\r\n    this.getBuildCasebyId();\r\n\r\n    if (this.currentDate <= this.startedDate || this.currentDate > this.endedDate) {\r\n      this.changeTime = false;\r\n      this.selectedMenu = this.getListMenuNotChangeTime();\r\n    }\r\n\r\n    let is_show = LocalStorageService.GetSessionStorage(STORAGE_KEY.IS_SHOW);\r\n    if (is_show == \"true\") {\r\n      this.onHide();\r\n    }\r\n  }\r\n\r\n  ngAfterViewChecked(): void {\r\n    this.getPayStatus();\r\n    LoadingService.loading(false);\r\n  }\r\n\r\n  onHide() {\r\n    this.showPopupChangeTime = false;\r\n  }\r\n\r\n  gotoAnnoucement() {\r\n    if (this.currentDate <= this.startedDate || this.currentDate > this.endedDate) {\r\n      this.showPopupChangeTime = false;\r\n      this.changeTime = false;\r\n      this.selectedMenu = this.getListMenuNotChangeTime();\r\n      return;\r\n    }\r\n    if (this.currentDate >= this.endedDate) {\r\n      this.acceptChangeTime()\r\n      this.changeTime = true\r\n      return;\r\n    }\r\n    this.changeTime = true;\r\n    let current_progress = LocalStorageService.GetLocalStorage(STORAGE_KEY.CURRENT_PROGRESS);\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.IS_SHOW, 'true');\r\n    if (current_progress == 0) {\r\n      this.goNext = true;\r\n    }\r\n    else {\r\n      this.onHide();\r\n      this.showPopupChangeTime = false;\r\n      let dataUser = LocalStorageService.GetLocalStorage(STORAGE_KEY.USER) as UserInfo;\r\n      let payload: UserInfo = {\r\n        acceptChangeTime: true,\r\n        isFinishBuilding: dataUser.isFinishBuilding,\r\n        name: dataUser.name\r\n      }\r\n      LocalStorageService.AddLocalStorage(STORAGE_KEY.USER, payload);\r\n    }\r\n  }\r\n\r\n  getListMenuStep() {\r\n    this.current_progress = LocalStorageService.GetLocalStorage(STORAGE_KEY.CURRENT_PROGRESS)\r\n    let listMenu = [\r\n      {\r\n        id: 1,\r\n        name: \"已閱讀操作說明\",\r\n        imageSrcWeb: 'assets/img1.png',\r\n        imageSrcMobile: 'assets/Group6.png',\r\n        routeTo: '/notice',\r\n        isDisabled: this.currentDate < this.startedDate || this.currentDate > this.endedDate,\r\n        isFinish: this.current_progress > 0 ? true : false\r\n      },\r\n      {\r\n        id: 2,\r\n        name: \"建材設備選定\",\r\n        routeTo: '/choice',\r\n        imageSrcWeb: 'assets/img2.png',\r\n        imageSrcMobile: 'assets/Group1.png',\r\n        isDisabled: this.currentDate < this.startedDate || this.currentDate > this.endedDate,\r\n        isFinish: this.current_progress > 1 ? true : false\r\n      },\r\n      {\r\n        id: 3,\r\n        name: !this.cIsChange ? \"確認簽署\" : \"簽署及繳款完成\",\r\n        imageSrcWeb: 'assets/img3.png',\r\n        imageSrcMobile: 'assets/Group4.png',\r\n        routeTo: '/sign',\r\n        isDisabled: this.currentDate < this.startedDate,\r\n        isFinish: !this.cIsChange\r\n          ? (this.current_progress > 2 ? true : false)\r\n          : ((this.current_progress > 2 && this.cPayStatus == 1) ? true : false)\r\n      }\r\n    ];\r\n\r\n    return listMenu;\r\n  }\r\n\r\n  getListMenuNotChangeTime() {\r\n    let listMenu = [\r\n      {\r\n        id: 4,\r\n        name: \"洽談紀錄\",\r\n        routeTo: '/history',\r\n        imageSrcWeb: 'assets/img4.png',\r\n        imageSrcMobile: 'assets/Group2.png',\r\n        isDisabled: this.currentDate < this.startedDate || this.currentDate > this.endedDate,\r\n        isFinish: false\r\n      },\r\n      {\r\n        id: 6,\r\n        name: \"建案公佈欄\",\r\n        imageSrcWeb: 'assets/img6.png',\r\n        imageSrcMobile: 'assets/Group5.png',\r\n        routeTo: '/resource',\r\n        isDisabled: false,\r\n        isFinish: false\r\n      },\r\n      {\r\n        id: 7,\r\n        name: \"聯絡資料修改\",\r\n        imageSrcWeb: 'assets/img7.png',\r\n        imageSrcMobile: 'assets/Group7.png',\r\n        routeTo: '/edit-info',\r\n        isDisabled: false,\r\n        isFinish: false\r\n      },\r\n      // {\r\n      //   id: 8,\r\n      //   name: \"更改密碼\",\r\n      //   imageSrcWeb: 'assets/img7.png',\r\n      //   imageSrcMobile: 'assets/Group7.png',\r\n      //   routeTo: '/change-password',\r\n      //   isDisabled: false,\r\n      //   isFinish: false\r\n      // }\r\n    ];\r\n\r\n    return listMenu;\r\n  }\r\n\r\n  getListMenu() {\r\n    let listMenu = [\r\n      {\r\n        id: 4,\r\n        name: \"洽談紀錄\",\r\n        routeTo: '/history',\r\n        imageSrcWeb: 'assets/img4.png',\r\n        imageSrcMobile: 'assets/Group2.png',\r\n        isDisabled: this.currentDate < this.startedDate || this.currentDate > this.endedDate,\r\n        isFinish: false\r\n      },\r\n      {\r\n        id: 5,\r\n        name: \"預約客變時段\",\r\n        imageSrcWeb: 'assets/img5.png',\r\n        imageSrcMobile: 'assets/Group3.png',\r\n        routeTo: '/reserve',\r\n        isDisabled: false,\r\n        isFinish: false\r\n      },\r\n      {\r\n        id: 6,\r\n        name: \"建案公佈欄\",\r\n        imageSrcWeb: 'assets/img6.png',\r\n        imageSrcMobile: 'assets/Group5.png',\r\n        routeTo: '/resource',\r\n        isDisabled: false,\r\n        isFinish: false\r\n      },\r\n      {\r\n        id: 7,\r\n        name: \"聯絡資料修改\",\r\n        imageSrcWeb: 'assets/img7.png',\r\n        imageSrcMobile: 'assets/Group7.png',\r\n        routeTo: '/edit-info',\r\n        isDisabled: false,\r\n        isFinish: false\r\n      },\r\n      {\r\n        id: 8,\r\n        name: \"更改密碼\",\r\n        imageSrcWeb: 'assets/img7.png',\r\n        imageSrcMobile: 'assets/Group7.png',\r\n        routeTo: '/change-password',\r\n        isDisabled: false,\r\n        isFinish: false\r\n      }, {\r\n        id: 9,\r\n        name: \"報價單\",\r\n        imageSrcWeb: 'assets/img7.png',\r\n        imageSrcMobile: 'assets/Group7.png',\r\n        routeTo: '/quotation',\r\n        isDisabled: false,\r\n        isFinish: false\r\n      }\r\n    ];\r\n\r\n    return listMenu;\r\n  }\r\n\r\n  getBuildCasebyId() {\r\n    this._buildCaseService.apiBuildCaseGetBuildCaseByIdPost$Json({\r\n      body: {\r\n        CBuildCaseID: LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).buildCaseId\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        this.imageFront = res.Entries?.CFrontImage!\r\n        this.systemInstruction = res.Entries?.CSystemInstruction!\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  logout() {\r\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.DATE_END);\r\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.DATE_START);\r\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.TOKEN);\r\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.USER);\r\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.NUMBER_STEP);\r\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.CURRENT_PROGRESS);\r\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.SIGN_INFO);\r\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.HOUSE_PROGRESS);\r\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.IS_CHANGE);\r\n    LocalStorageService.RemoveLocalStorage(STORAGE_KEY.PAY_STATUS);\r\n\r\n    this._router.navigate(['login'])\r\n  }\r\n\r\n  acceptChangeTime() {\r\n    this.updateProgress(1);\r\n    this.showPopupChangeTime = false;\r\n    let dataUser = LocalStorageService.GetLocalStorage(STORAGE_KEY.USER) as UserInfo;\r\n    let payload: UserInfo = {\r\n      acceptChangeTime: true,\r\n      isFinishBuilding: dataUser.isFinishBuilding,\r\n      name: dataUser.name\r\n    }\r\n    LocalStorageService.AddLocalStorage(STORAGE_KEY.USER, payload);\r\n  }\r\n\r\n  getPayStatus() {\r\n    this.cIsChange = LocalStorageService.GetLocalStorage(STORAGE_KEY.IS_CHANGE)\r\n    this.cPayStatus = LocalStorageService.GetLocalStorage(STORAGE_KEY.PAY_STATUS)\r\n    this.selectedMenuStep = this.getListMenuStep().filter(menu => !menu.isDisabled)\r\n  }\r\n\r\n  updateProgress(progress: number) {\r\n    LoadingService.loading(true);\r\n    this.houseService.apiHouseUpdateHouseProgressPost$Json({ body: progress })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0 && res.Message == \"Success\") {\r\n            LocalStorageService.AddLocalStorage(STORAGE_KEY.CURRENT_PROGRESS, progress)\r\n          }\r\n        })\r\n      ).subscribe();\r\n  }\r\n}\r\n\r\n", "<div class=\"wrapper web-ver\">\r\n  <div class=\"content !w-full\">\r\n    <div class=\"flex flex-col md:pb-20 bg-white\">\r\n      <div class=\"self-center mt-10 w-full max-w-[1216px] max-md:mt-10 max-md:max-w-full\">\r\n        <div class=\"flex !min-h-[60vh] mx-3\">\r\n          <div class=\"w-1/3 pr-4\">\r\n            <img [src]=\"imageFront | addBaseFile\" loading=\"lazy\" class=\"fit-size imageBuildcase !w-full !h-full\" />\r\n          </div>\r\n          <div class=\"w-2/3\">\r\n            <div class=\"grid grid-cols-3 gap-4 max-lg:w-full max-sm:!grid-cols-2\" *ngIf=\"changeTime\">\r\n              @for (menu of selectedMenuStep; track $index) {\r\n              <div class=\"w-full relative item-home\">\r\n                <a [routerLink]=\"menu.routeTo\"\r\n                  [ngStyle]=\"{'filter':(!menu.isFinish ? 'grayscale(1)' : ''), 'background-image': 'url(' + menu.imageSrcWeb + ')'}\">\r\n                  <div class=\"flex flex-row text-[#231815] text-base item-text\">\r\n                    <span class=\"number-circle\">{{$index + 1}}</span>\r\n                    <span class=\"label-text\">{{menu.name}}</span>\r\n                  </div>\r\n                </a>\r\n              </div>\r\n              }\r\n            </div>\r\n            <div style=\"height: 20px;\"></div>\r\n            <div class=\"grid grid-cols-4 gap-4 max-lg:w-full max-sm:!grid-cols-2\">\r\n              @for (menu of selectedMenu; track $index) {\r\n              <div class=\"w-full relative item-home-child\">\r\n                <a [routerLink]=\"menu.routeTo\" [ngStyle]=\"{'background-image': 'url(' + menu.imageSrcWeb + ')'}\">\r\n                  <div class=\"label-text-child text-[#231815] text-base\">\r\n                    {{menu.name}}\r\n                  </div>\r\n                </a>\r\n              </div>\r\n              }\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"wrapper mobile-ver\">\r\n  <div class=\"content !w-full\">\r\n    <div class=\"flex flex-col md:pb-20 bg-white\">\r\n      <div class=\"self-center mt-5 w-full max-w-[1216px] max-md:mt-5 max-md:max-w-full\">\r\n        <div class=\"flex !min-h-[60vh] mx-3\">\r\n          <div class=\"grid grid-cols-2 gap-4 max-lg:w-full\">\r\n            @for (menu of selectedMenuStep; track $index) {\r\n            <div class=\"w-full relative !h-full\">\r\n              <a [routerLink]=\"menu.routeTo\">\r\n                <img class=\" !h-full rounded border-2\" style=\"border: 1px solid #dbe6ea;\" [src]=\"menu.imageSrcMobile\">\r\n                <div class=\"absolute top-1/2 left-1/2 text-[#231815] text-base text-center\"\r\n                  style=\"transform: translate(-50%, 200%); width: 150px;\">{{menu.name}}</div>\r\n              </a>\r\n            </div>\r\n            }\r\n            @for (menu of selectedMenu; track $index) {\r\n            <div class=\"w-full relative !h-full\">\r\n              <a [routerLink]=\"menu.routeTo\">\r\n                <img class=\" !h-full rounded border-2\" style=\"border: 1px solid #dbe6ea;\" [src]=\"menu.imageSrcMobile\">\r\n                <div class=\"absolute top-1/2 left-1/2 text-[#231815] text-base text-center\"\r\n                  style=\"transform: translate(-50%, 200%); width: 150px;\">{{menu.name}}</div>\r\n              </a>\r\n            </div>\r\n            }\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n@if (startedDate && endedDate) {\r\n<p-dialog [(visible)]=\"showPopupChangeTime\" [modal]=\"true\" [draggable]=\"false\" [resizable]=\"false\" [closable]=\"false\"\r\n  (onHide)=\"onHide()\" [showHeader]=\"false\">\r\n  @if (!goNext) {\r\n  <div class=\"container max-sm:!w-[340px] max-md:!w-[450px] flex flex-col\" style=\"height: 265px;\">\r\n    @if (this.currentDate > this.endedDate) {\r\n    <div class=\"zone-date\" style=\"font-size: 30px !important;\">\r\n      客變時間已結束\r\n    </div>\r\n    }@else {\r\n    <div class=\"text-2xl font-bold text-stone-900 text-center\">\r\n      提醒您，你的選樣時段為\r\n    </div>\r\n    <div class=\"zone-date p-[16px] mt-[16px] font-bold text-center\">\r\n      {{startedDate | date: 'yyyy/MM/dd'}} ~ {{endedDate | date: 'yyyy/MM/dd'}}\r\n    </div>\r\n    <div class=\"mt-[16px] text-center text-base\">\r\n      如逾期未完成流程，新合達建設將依照樣品屋形式完成。\r\n    </div>\r\n    }\r\n    <span class=\"button3\" (click)=\"gotoAnnoucement()\">\r\n      {{language.confirm}}<!-- 確認  -->\r\n    </span>\r\n  </div>\r\n  }@else {\r\n  <div class=\"container max-sm:!w-[340px] max-md:!w-[450px] flex flex-col\" style=\"height: 500px;\">\r\n    <div class=\"text-2xl self-center font-bold text-stone-900\">\r\n      {{language.systemOperatingInstructions}}<!-- 客變須知 -->\r\n    </div>\r\n    <div class=\"annoucement\">\r\n      <div [innerHTML]=\"systemInstruction\" style=\"white-space: pre-wrap;\"></div>\r\n    </div>\r\n    <span class=\"button3\" (click)=\"acceptChangeTime()\">\r\n      {{language.iUnderstand}}<!-- 確認 -->\r\n    </span>\r\n  </div>\r\n  }\r\n\r\n</p-dialog>\r\n}"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAAiBC,YAAY,QAAQ,iBAAiB;AAEtD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,YAAY,QAAQ,uCAAuC;AACpE,SAASC,mBAAmB,QAAQ,6CAA6C;AACjF,SAASC,WAAW,EAAYC,QAAQ,QAAQ,gCAAgC;AAChF,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,YAAY,QAAQ,mCAAmC;AAEhE,SAASC,cAAc,QAAQ,uCAAuC;;;;;;;;;;;;;;;ICClDC,EAJN,CAAAC,cAAA,cAAuC,YAEgF,cACrD,eAChC;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAG5CF,EAH4C,CAAAG,YAAA,EAAO,EACzC,EACJ,EACA;;;;;IAPDH,EAAA,CAAAI,SAAA,EAA2B;IAC5BJ,EADC,CAAAK,UAAA,eAAAC,OAAA,CAAAC,OAAA,CAA2B,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,GAAAH,OAAA,CAAAI,QAAA,iCAAAJ,OAAA,CAAAK,WAAA,QACsF;IAEpFX,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAY,iBAAA,CAAAC,SAAA,KAAc;IACjBb,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAY,iBAAA,CAAAN,OAAA,CAAAQ,IAAA,CAAa;;;;;IAP9Cd,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAe,gBAAA,IAAAC,kCAAA,mBAAAhB,EAAA,CAAAiB,sBAAA,CAUC;IACHjB,EAAA,CAAAG,YAAA,EAAM;;;;IAXJH,EAAA,CAAAI,SAAA,EAUC;IAVDJ,EAAA,CAAAkB,UAAA,CAAAC,MAAA,CAAAC,gBAAA,CAUC;;;;;IAOGpB,EAFJ,CAAAC,cAAA,cAA6C,YACsD,cACxC;IACrDD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACJ,EACA;;;;IALDH,EAAA,CAAAI,SAAA,EAA2B;IAACJ,EAA5B,CAAAK,UAAA,eAAAgB,OAAA,CAAAd,OAAA,CAA2B,YAAAP,EAAA,CAAAsB,eAAA,IAAAC,GAAA,WAAAF,OAAA,CAAAV,WAAA,QAAkE;IAE5FX,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAH,OAAA,CAAAP,IAAA,MACF;;;;;IAoBJd,EADF,CAAAC,cAAA,cAAqC,YACJ;IAC7BD,EAAA,CAAAyB,SAAA,cAAsG;IACtGzB,EAAA,CAAAC,cAAA,cAC0D;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAE3EF,EAF2E,CAAAG,YAAA,EAAM,EAC3E,EACA;;;;IALDH,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAK,UAAA,eAAAqB,OAAA,CAAAnB,OAAA,CAA2B;IAC8CP,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAK,UAAA,QAAAqB,OAAA,CAAAC,cAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAA2B;IAE3C5B,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAY,iBAAA,CAAAc,OAAA,CAAAZ,IAAA,CAAa;;;;;IAMzEd,EADF,CAAAC,cAAA,cAAqC,YACJ;IAC7BD,EAAA,CAAAyB,SAAA,cAAsG;IACtGzB,EAAA,CAAAC,cAAA,cAC0D;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAE3EF,EAF2E,CAAAG,YAAA,EAAM,EAC3E,EACA;;;;IALDH,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAK,UAAA,eAAAwB,OAAA,CAAAtB,OAAA,CAA2B;IAC8CP,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAK,UAAA,QAAAwB,OAAA,CAAAF,cAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAA2B;IAE3C5B,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAY,iBAAA,CAAAiB,OAAA,CAAAf,IAAA,CAAa;;;;;IAiBnFd,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,MAAA,mDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,MAAA,2EACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,MAAA,+JACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAJJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA8B,kBAAA,MAAA9B,EAAA,CAAA+B,WAAA,OAAAZ,MAAA,CAAAa,WAAA,wBAAAhC,EAAA,CAAA+B,WAAA,OAAAZ,MAAA,CAAAc,SAAA,qBACF;;;;;;IAXFjC,EAAA,CAAAC,cAAA,cAAgG;IAK7FD,EAJD,CAAAkC,UAAA,IAAAC,iEAAA,kBAAyC,IAAAC,iEAAA,OAIjC;IAWRpC,EAAA,CAAAC,cAAA,eAAkD;IAA5BD,EAAA,CAAAqC,UAAA,mBAAAC,0EAAA;MAAAtC,EAAA,CAAAuC,aAAA,CAAAC,GAAA;MAAA,MAAArB,MAAA,GAAAnB,EAAA,CAAAyC,aAAA;MAAA,OAAAzC,EAAA,CAAA0C,WAAA,CAASvB,MAAA,CAAAwB,eAAA,EAAiB;IAAA,EAAC;IAC/C3C,EAAA,CAAAE,MAAA,GAAoB;IAExBF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAlBJH,EAAA,CAAAI,SAAA,EAcC;IAdDJ,EAAA,CAAA4C,aAAA,IAAAzB,MAAA,CAAA0B,WAAA,GAAA1B,MAAA,CAAAc,SAAA,SAcC;IAECjC,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAwB,kBAAA,MAAAL,MAAA,CAAA2B,QAAA,CAAAC,OAAA,KAAoB;;;;;;IAKtB/C,EADF,CAAAC,cAAA,cAAgG,cACnC;IACzDD,EAAA,CAAAE,MAAA,GAAwC;IAC1CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAyB,SAAA,cAA0E;IAC5EzB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAmD;IAA7BD,EAAA,CAAAqC,UAAA,mBAAAW,0EAAA;MAAAhD,EAAA,CAAAuC,aAAA,CAAAU,GAAA;MAAA,MAAA9B,MAAA,GAAAnB,EAAA,CAAAyC,aAAA;MAAA,OAAAzC,EAAA,CAAA0C,WAAA,CAASvB,MAAA,CAAA+B,gBAAA,EAAkB;IAAA,EAAC;IAChDlD,EAAA,CAAAE,MAAA,GAAwB;IAE5BF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IARFH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAwB,kBAAA,MAAAL,MAAA,CAAA2B,QAAA,CAAAK,2BAAA,KAAwC;IAGnCnD,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,UAAA,cAAAc,MAAA,CAAAiC,iBAAA,EAAApD,EAAA,CAAAqD,cAAA,CAA+B;IAGpCrD,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAwB,kBAAA,MAAAL,MAAA,CAAA2B,QAAA,CAAAQ,WAAA,KAAwB;;;;;;IAhC9BtD,EAAA,CAAAC,cAAA,mBAC2C;IADjCD,EAAA,CAAAuD,gBAAA,2BAAAC,wEAAAC,MAAA;MAAAzD,EAAA,CAAAuC,aAAA,CAAAmB,GAAA;MAAA,MAAAvC,MAAA,GAAAnB,EAAA,CAAAyC,aAAA;MAAAzC,EAAA,CAAA2D,kBAAA,CAAAxC,MAAA,CAAAyC,mBAAA,EAAAH,MAAA,MAAAtC,MAAA,CAAAyC,mBAAA,GAAAH,MAAA;MAAA,OAAAzD,EAAA,CAAA0C,WAAA,CAAAe,MAAA;IAAA,EAAiC;IACzCzD,EAAA,CAAAqC,UAAA,oBAAAwB,iEAAA;MAAA7D,EAAA,CAAAuC,aAAA,CAAAmB,GAAA;MAAA,MAAAvC,MAAA,GAAAnB,EAAA,CAAAyC,aAAA;MAAA,OAAAzC,EAAA,CAAA0C,WAAA,CAAUvB,MAAA,CAAA2C,MAAA,EAAQ;IAAA,EAAC;IAsBlB9D,EArBD,CAAAkC,UAAA,IAAA6B,mDAAA,kBAAe,IAAAC,mDAAA,OAqBP;IAcVhE,EAAA,CAAAG,YAAA,EAAW;;;;IArCDH,EAAA,CAAAiE,gBAAA,YAAA9C,MAAA,CAAAyC,mBAAA,CAAiC;IACrB5D,EADsB,CAAAK,UAAA,eAAc,oBAAoB,oBAAoB,mBAAmB,qBAC3E;IACxCL,EAAA,CAAAI,SAAA,EAiCC;IAjCDJ,EAAA,CAAA4C,aAAA,KAAAzB,MAAA,CAAA+C,MAAA,SAiCC;;;AD1EH,OAAM,MAAOC,aAAa;EAmBxBC,YACUC,iBAAmC,EACnCC,OAAe,EACfC,YAA0B;IAF1B,KAAAF,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,YAAY,GAAZA,YAAY;IArBtB,KAAAnD,gBAAgB,GAAW,EAAE;IAC7B,KAAAoD,YAAY,GAAW,EAAE;IACzB,KAAA1B,QAAQ,GAAGlD,QAAQ,CAAC6E,IAAI;IACxB;IACA,KAAAzC,WAAW,GAAG,IAAI0C,IAAI,EAAE;IACxB,KAAAzC,SAAS,GAAG,IAAIyC,IAAI,EAAE;IACtB,KAAA7B,WAAW,GAAG,IAAI6B,IAAI,EAAE;IACxB,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAf,mBAAmB,GAAY,KAAK;IACpC,KAAAM,MAAM,GAAY,KAAK;IACvB,KAAAU,wBAAwB,GAAY,IAAI;IACxC,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAzB,iBAAiB,GAAiB,EAAE;IACpC,KAAA0B,UAAU,GAAW,EAAE;IACvB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,UAAU,GAAW,CAAC;IACtB,KAAAC,UAAU,GAAY,IAAI;EAO1B;EAEAC,QAAQA,CAAA;IACNnF,cAAc,CAACoF,OAAO,CAAC,IAAI,CAAC;IAC5B;IACA,IAAI,CAACnD,WAAW,GAAG,IAAI0C,IAAI,CAAChF,mBAAmB,CAAC0F,eAAe,CAACzF,WAAW,CAAC0F,UAAU,CAAC,CAAC;IACxF,IAAI,CAACpD,SAAS,GAAG,IAAIyC,IAAI,CAAChF,mBAAmB,CAAC0F,eAAe,CAACzF,WAAW,CAAC2F,QAAQ,CAAC,CAAC;IAEpF;IACA,IAAIC,QAAQ,GAAG7F,mBAAmB,CAAC0F,eAAe,CAACzF,WAAW,CAAC6F,IAAI,CAAa;IAChF,IAAI,CAACZ,wBAAwB,GAAGW,QAAQ,CAACE,gBAAgB;IACzD,IAAI,CAACjB,YAAY,GAAG,IAAI,CAACkB,WAAW,EAAE,CAACC,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC;IACvE,IAAI,CAACjC,mBAAmB,GAAG,CAAC2B,QAAQ,CAACrC,gBAAgB;IACrD,IAAI,CAAC4C,gBAAgB,EAAE;IAEvB,IAAI,IAAI,CAACjD,WAAW,IAAI,IAAI,CAACb,WAAW,IAAI,IAAI,CAACa,WAAW,GAAG,IAAI,CAACZ,SAAS,EAAE;MAC7E,IAAI,CAACgD,UAAU,GAAG,KAAK;MACvB,IAAI,CAACT,YAAY,GAAG,IAAI,CAACuB,wBAAwB,EAAE;;IAGrD,IAAIC,OAAO,GAAGtG,mBAAmB,CAACuG,iBAAiB,CAACtG,WAAW,CAACuG,OAAO,CAAC;IACxE,IAAIF,OAAO,IAAI,MAAM,EAAE;MACrB,IAAI,CAAClC,MAAM,EAAE;;EAEjB;EAEAqC,kBAAkBA,CAAA;IAChB,IAAI,CAACC,YAAY,EAAE;IACnBrG,cAAc,CAACoF,OAAO,CAAC,KAAK,CAAC;EAC/B;EAEArB,MAAMA,CAAA;IACJ,IAAI,CAACF,mBAAmB,GAAG,KAAK;EAClC;EAEAjB,eAAeA,CAAA;IACb,IAAI,IAAI,CAACE,WAAW,IAAI,IAAI,CAACb,WAAW,IAAI,IAAI,CAACa,WAAW,GAAG,IAAI,CAACZ,SAAS,EAAE;MAC7E,IAAI,CAAC2B,mBAAmB,GAAG,KAAK;MAChC,IAAI,CAACqB,UAAU,GAAG,KAAK;MACvB,IAAI,CAACT,YAAY,GAAG,IAAI,CAACuB,wBAAwB,EAAE;MACnD;;IAEF,IAAI,IAAI,CAAClD,WAAW,IAAI,IAAI,CAACZ,SAAS,EAAE;MACtC,IAAI,CAACiB,gBAAgB,EAAE;MACvB,IAAI,CAAC+B,UAAU,GAAG,IAAI;MACtB;;IAEF,IAAI,CAACA,UAAU,GAAG,IAAI;IACtB,IAAIN,gBAAgB,GAAGjF,mBAAmB,CAAC0F,eAAe,CAACzF,WAAW,CAAC0G,gBAAgB,CAAC;IACxF3G,mBAAmB,CAAC4G,iBAAiB,CAAC3G,WAAW,CAACuG,OAAO,EAAE,MAAM,CAAC;IAClE,IAAIvB,gBAAgB,IAAI,CAAC,EAAE;MACzB,IAAI,CAACT,MAAM,GAAG,IAAI;KACnB,MACI;MACH,IAAI,CAACJ,MAAM,EAAE;MACb,IAAI,CAACF,mBAAmB,GAAG,KAAK;MAChC,IAAI2C,QAAQ,GAAG7G,mBAAmB,CAAC0F,eAAe,CAACzF,WAAW,CAAC6F,IAAI,CAAa;MAChF,IAAIgB,OAAO,GAAa;QACtBtD,gBAAgB,EAAE,IAAI;QACtBuC,gBAAgB,EAAEc,QAAQ,CAACd,gBAAgB;QAC3C3E,IAAI,EAAEyF,QAAQ,CAACzF;OAChB;MACDpB,mBAAmB,CAAC+G,eAAe,CAAC9G,WAAW,CAAC6F,IAAI,EAAEgB,OAAO,CAAC;;EAElE;EAEAE,eAAeA,CAAA;IACb,IAAI,CAAC/B,gBAAgB,GAAGjF,mBAAmB,CAAC0F,eAAe,CAACzF,WAAW,CAAC0G,gBAAgB,CAAC;IACzF,IAAIM,QAAQ,GAAG,CACb;MACEC,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,SAAS;MACfH,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCpB,OAAO,EAAE,SAAS;MAClBsF,UAAU,EAAE,IAAI,CAAChD,WAAW,GAAG,IAAI,CAACb,WAAW,IAAI,IAAI,CAACa,WAAW,GAAG,IAAI,CAACZ,SAAS;MACpFvB,QAAQ,EAAE,IAAI,CAACiE,gBAAgB,GAAG,CAAC,GAAG,IAAI,GAAG;KAC9C,EACD;MACEiC,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,QAAQ;MACdP,OAAO,EAAE,SAAS;MAClBI,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCkE,UAAU,EAAE,IAAI,CAAChD,WAAW,GAAG,IAAI,CAACb,WAAW,IAAI,IAAI,CAACa,WAAW,GAAG,IAAI,CAACZ,SAAS;MACpFvB,QAAQ,EAAE,IAAI,CAACiE,gBAAgB,GAAG,CAAC,GAAG,IAAI,GAAG;KAC9C,EACD;MACEiC,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,CAAC,IAAI,CAACiE,SAAS,GAAG,MAAM,GAAG,SAAS;MAC1CpE,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCpB,OAAO,EAAE,OAAO;MAChBsF,UAAU,EAAE,IAAI,CAAChD,WAAW,GAAG,IAAI,CAACb,WAAW;MAC/CtB,QAAQ,EAAE,CAAC,IAAI,CAACqE,SAAS,GACpB,IAAI,CAACJ,gBAAgB,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,GACvC,IAAI,CAACA,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAACK,UAAU,IAAI,CAAC,GAAI,IAAI,GAAG;KACnE,CACF;IAED,OAAO2B,QAAQ;EACjB;EAEAZ,wBAAwBA,CAAA;IACtB,IAAIY,QAAQ,GAAG,CACb;MACEC,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,MAAM;MACZP,OAAO,EAAE,UAAU;MACnBI,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCkE,UAAU,EAAE,IAAI,CAAChD,WAAW,GAAG,IAAI,CAACb,WAAW,IAAI,IAAI,CAACa,WAAW,GAAG,IAAI,CAACZ,SAAS;MACpFvB,QAAQ,EAAE;KACX,EACD;MACEkG,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,OAAO;MACbH,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCpB,OAAO,EAAE,WAAW;MACpBsF,UAAU,EAAE,KAAK;MACjBnF,QAAQ,EAAE;KACX,EACD;MACEkG,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,QAAQ;MACdH,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCpB,OAAO,EAAE,YAAY;MACrBsF,UAAU,EAAE,KAAK;MACjBnF,QAAQ,EAAE;;IAEZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAAA,CACD;IAED,OAAOiG,QAAQ;EACjB;EAEAjB,WAAWA,CAAA;IACT,IAAIiB,QAAQ,GAAG,CACb;MACEC,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,MAAM;MACZP,OAAO,EAAE,UAAU;MACnBI,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCkE,UAAU,EAAE,IAAI,CAAChD,WAAW,GAAG,IAAI,CAACb,WAAW,IAAI,IAAI,CAACa,WAAW,GAAG,IAAI,CAACZ,SAAS;MACpFvB,QAAQ,EAAE;KACX,EACD;MACEkG,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,QAAQ;MACdH,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCpB,OAAO,EAAE,UAAU;MACnBsF,UAAU,EAAE,KAAK;MACjBnF,QAAQ,EAAE;KACX,EACD;MACEkG,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,OAAO;MACbH,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCpB,OAAO,EAAE,WAAW;MACpBsF,UAAU,EAAE,KAAK;MACjBnF,QAAQ,EAAE;KACX,EACD;MACEkG,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,QAAQ;MACdH,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCpB,OAAO,EAAE,YAAY;MACrBsF,UAAU,EAAE,KAAK;MACjBnF,QAAQ,EAAE;KACX,EACD;MACEkG,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,MAAM;MACZH,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCpB,OAAO,EAAE,kBAAkB;MAC3BsF,UAAU,EAAE,KAAK;MACjBnF,QAAQ,EAAE;KACX,EAAE;MACDkG,EAAE,EAAE,CAAC;MACL9F,IAAI,EAAE,KAAK;MACXH,WAAW,EAAE,iBAAiB;MAC9BgB,cAAc,EAAE,mBAAmB;MACnCpB,OAAO,EAAE,YAAY;MACrBsF,UAAU,EAAE,KAAK;MACjBnF,QAAQ,EAAE;KACX,CACF;IAED,OAAOiG,QAAQ;EACjB;EAEAb,gBAAgBA,CAAA;IACd,IAAI,CAACzB,iBAAiB,CAACwC,qCAAqC,CAAC;MAC3DC,IAAI,EAAE;QACJC,YAAY,EAAErH,mBAAmB,CAAC0F,eAAe,CAACzF,WAAW,CAACqH,UAAU,CAAC,CAACC;;KAE7E,CAAC,CAACC,IAAI,CACLrH,GAAG,CAACsH,GAAG,IAAG;MACR,IAAI,CAACrC,UAAU,GAAGqC,GAAG,CAACC,OAAO,EAAEC,WAAY;MAC3C,IAAI,CAACjE,iBAAiB,GAAG+D,GAAG,CAACC,OAAO,EAAEE,kBAAmB;IAC3D,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,MAAMA,CAAA;IACJ9H,mBAAmB,CAAC+H,kBAAkB,CAAC9H,WAAW,CAAC2F,QAAQ,CAAC;IAC5D5F,mBAAmB,CAAC+H,kBAAkB,CAAC9H,WAAW,CAAC0F,UAAU,CAAC;IAC9D3F,mBAAmB,CAAC+H,kBAAkB,CAAC9H,WAAW,CAAC+H,KAAK,CAAC;IACzDhI,mBAAmB,CAAC+H,kBAAkB,CAAC9H,WAAW,CAAC6F,IAAI,CAAC;IACxD9F,mBAAmB,CAAC+H,kBAAkB,CAAC9H,WAAW,CAACgI,WAAW,CAAC;IAC/DjI,mBAAmB,CAAC+H,kBAAkB,CAAC9H,WAAW,CAAC0G,gBAAgB,CAAC;IACpE3G,mBAAmB,CAAC+H,kBAAkB,CAAC9H,WAAW,CAACiI,SAAS,CAAC;IAC7DlI,mBAAmB,CAAC+H,kBAAkB,CAAC9H,WAAW,CAACkI,cAAc,CAAC;IAClEnI,mBAAmB,CAAC+H,kBAAkB,CAAC9H,WAAW,CAACmI,SAAS,CAAC;IAC7DpI,mBAAmB,CAAC+H,kBAAkB,CAAC9H,WAAW,CAACoI,UAAU,CAAC;IAE9D,IAAI,CAACzD,OAAO,CAAC0D,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EAClC;EAEA9E,gBAAgBA,CAAA;IACd,IAAI,CAAC+E,cAAc,CAAC,CAAC,CAAC;IACtB,IAAI,CAACrE,mBAAmB,GAAG,KAAK;IAChC,IAAI2C,QAAQ,GAAG7G,mBAAmB,CAAC0F,eAAe,CAACzF,WAAW,CAAC6F,IAAI,CAAa;IAChF,IAAIgB,OAAO,GAAa;MACtBtD,gBAAgB,EAAE,IAAI;MACtBuC,gBAAgB,EAAEc,QAAQ,CAACd,gBAAgB;MAC3C3E,IAAI,EAAEyF,QAAQ,CAACzF;KAChB;IACDpB,mBAAmB,CAAC+G,eAAe,CAAC9G,WAAW,CAAC6F,IAAI,EAAEgB,OAAO,CAAC;EAChE;EAEAJ,YAAYA,CAAA;IACV,IAAI,CAACrB,SAAS,GAAGrF,mBAAmB,CAAC0F,eAAe,CAACzF,WAAW,CAACmI,SAAS,CAAC;IAC3E,IAAI,CAAC9C,UAAU,GAAGtF,mBAAmB,CAAC0F,eAAe,CAACzF,WAAW,CAACoI,UAAU,CAAC;IAC7E,IAAI,CAAC3G,gBAAgB,GAAG,IAAI,CAACsF,eAAe,EAAE,CAACf,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC;EACjF;EAEAoC,cAAcA,CAACC,QAAgB;IAC7BnI,cAAc,CAACoF,OAAO,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACZ,YAAY,CAAC4D,oCAAoC,CAAC;MAAErB,IAAI,EAAEoB;IAAQ,CAAE,CAAC,CACvEhB,IAAI,CACHrH,GAAG,CAACsH,GAAG,IAAG;MACR,IAAIA,GAAG,CAACiB,UAAU,IAAI,CAAC,IAAIjB,GAAG,CAACkB,OAAO,IAAI,SAAS,EAAE;QACnD3I,mBAAmB,CAAC+G,eAAe,CAAC9G,WAAW,CAAC0G,gBAAgB,EAAE6B,QAAQ,CAAC;;IAE/E,CAAC,CAAC,CACH,CAACX,SAAS,EAAE;EACjB;EAAC,QAAAe,CAAA,G;qBA9RUnE,aAAa,EAAAnE,EAAA,CAAAuI,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAzI,EAAA,CAAAuI,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA3I,EAAA,CAAAuI,iBAAA,CAAAC,EAAA,CAAAI,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAb1E,aAAa;IAAA2E,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAhJ,EAAA,CAAAiJ,kBAAA,CAPb,CACTzJ,cAAc,EACdC,YAAY,CACb,GAAAO,EAAA,CAAAkJ,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCzBOxJ,EALV,CAAAC,cAAA,aAA6B,aACE,aACkB,aACyC,aAC7C,aACX;QACtBD,EAAA,CAAAyB,SAAA,aAAuG;;QACzGzB,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAAmB;QACjBD,EAAA,CAAAkC,UAAA,IAAAwH,4BAAA,iBAAyF;QAazF1J,EAAA,CAAAyB,SAAA,cAAiC;QACjCzB,EAAA,CAAAC,cAAA,eAAsE;QACpED,EAAA,CAAAe,gBAAA,KAAA4I,6BAAA,mBAAA3J,EAAA,CAAAiB,sBAAA,CAQC;QAOfjB,EANY,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF,EACF,EACF;QAOIH,EALV,CAAAC,cAAA,eAAgC,cACD,cACkB,eACuC,cAC3C,eACe;QAChDD,EAAA,CAAAe,gBAAA,KAAA6I,6BAAA,mBAAA5J,EAAA,CAAAiB,sBAAA,CAQC;QACDjB,EAAA,CAAAe,gBAAA,KAAA8I,6BAAA,mBAAA7J,EAAA,CAAAiB,sBAAA,CAQC;QAMbjB,EALU,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF,EACF;QAENH,EAAA,CAAAkC,UAAA,KAAA4H,qCAAA,uBAAgC;;;QAlEf9J,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAK,UAAA,QAAAL,EAAA,CAAA+J,WAAA,OAAAN,GAAA,CAAA3E,UAAA,GAAA9E,EAAA,CAAA4B,aAAA,CAAgC;QAGkC5B,EAAA,CAAAI,SAAA,GAAgB;QAAhBJ,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAAxE,UAAA,CAAgB;QAerFjF,EAAA,CAAAI,SAAA,GAQC;QARDJ,EAAA,CAAAkB,UAAA,CAAAuI,GAAA,CAAAjF,YAAA,CAQC;QAeHxE,EAAA,CAAAI,SAAA,GAQC;QARDJ,EAAA,CAAAkB,UAAA,CAAAuI,GAAA,CAAArI,gBAAA,CAQC;QACDpB,EAAA,CAAAI,SAAA,GAQC;QARDJ,EAAA,CAAAkB,UAAA,CAAAuI,GAAA,CAAAjF,YAAA,CAQC;QAQbxE,EAAA,CAAAI,SAAA,GAuCC;QAvCDJ,EAAA,CAAA4C,aAAA,KAAA6G,GAAA,CAAAzH,WAAA,IAAAyH,GAAA,CAAAxH,SAAA,WAuCC;;;mBD3FG7C,YAAY,EAAAsJ,EAAA,CAAAsB,UAAA,EACZ7K,YAAY,EAAA8K,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,QAAA,EACZ/K,YAAY,EAAAgL,EAAA,CAAAC,MAAA,EACZhL,cAAc,EACdC,WAAW,EACXO,YAAY;IAAAyK,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}