{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ButtonModule } from 'primeng/button';\nimport { InputNumberModule } from 'primeng/inputnumber';\nimport { FormsModule } from '@angular/forms';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../services/api/services\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"primeng/inputnumber\";\nimport * as i5 from \"@angular/forms\";\nfunction StepViChoiceComponent_Conditional_1_div_4_div_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"span\", 20);\n    i0.ɵɵtext(2, \"\\u55AE\\u4F4D:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const houseRequirement_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(houseRequirement_r3.CUnit);\n  }\n}\nfunction StepViChoiceComponent_Conditional_1_div_4_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"span\", 23);\n    i0.ɵɵtext(2, \"\\u55AE\\u50F9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const houseRequirement_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind2(5, 1, houseRequirement_r3.CUnitPrice, \"1.0-0\"), \"\");\n  }\n}\nfunction StepViChoiceComponent_Conditional_1_div_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"h3\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 12);\n    i0.ɵɵtemplate(5, StepViChoiceComponent_Conditional_1_div_4_div_3_div_5_Template, 5, 1, \"div\", 13)(6, StepViChoiceComponent_Conditional_1_div_4_div_3_div_6_Template, 6, 4, \"div\", 14);\n    i0.ɵɵelementStart(7, \"div\", 15)(8, \"span\", 16);\n    i0.ɵɵtext(9, \"\\u6578\\u91CF:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 17)(11, \"p-inputNumber\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StepViChoiceComponent_Conditional_1_div_4_div_3_Template_p_inputNumber_ngModelChange_11_listener($event) {\n      const houseRequirement_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      i0.ɵɵtwoWayBindingSet(houseRequirement_r3.CCount, $event) || (houseRequirement_r3.CCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const houseRequirement_r3 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", houseRequirement_r3.CRequirement, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", houseRequirement_r3.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", houseRequirement_r3.CUnitPrice);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", houseRequirement_r3.CCount);\n    i0.ɵɵproperty(\"showButtons\", true)(\"min\", 0)(\"max\", 999)(\"step\", 1);\n  }\n}\nfunction StepViChoiceComponent_Conditional_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, StepViChoiceComponent_Conditional_1_div_4_div_3_Template, 12, 8, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupName_r4 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", groupName_r4, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.groupedRequirements[groupName_r4]);\n  }\n}\nfunction StepViChoiceComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"span\", 2);\n    i0.ɵɵtext(2, \" \\u8ACB\\u78BA\\u8A8D\\u662F\\u5426\\u6709\\u5176\\u4ED6\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 3);\n    i0.ɵɵtemplate(4, StepViChoiceComponent_Conditional_1_div_4_Template, 4, 2, \"div\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function StepViChoiceComponent_Conditional_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.next(3));\n    });\n    i0.ɵɵtext(6, \" \\u9001\\u51FA\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getGroupNames());\n  }\n}\nfunction StepViChoiceComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function StepViChoiceComponent_Conditional_2_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.next(1));\n    });\n    i0.ɵɵtext(3, \" \\u5C1A\\u6709\\u5176\\u4ED6\\u9700\\u6C42\\uFF0C\\u6309\\u6B64\\u9810\\u7D04\\u6D3D\\u8AC7 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function StepViChoiceComponent_Conditional_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.next(2));\n    });\n    i0.ɵɵtext(5, \" \\u7121\\u5176\\u4ED6\\u9700\\u6C42\\uFF0C\\u6309\\u6B64\\u4E0B\\u8F09\\u9078\\u6A23\\u7D50\\u679C\\u7C3D\\u540D \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class StepViChoiceComponent {\n  constructor(_houseService) {\n    this._houseService = _houseService;\n    this.listHouseRequirement = [];\n    this.nextEvent = new EventEmitter();\n    this.isRequirement = false;\n    this.groupedRequirements = {};\n  }\n  ngOnInit() {\n    this.groupRequirements();\n  }\n  ngOnChanges(changes) {\n    if (changes['listHouseRequirement']) {\n      this.groupRequirements();\n    }\n  }\n  groupRequirements() {\n    this.groupedRequirements = this.listHouseRequirement.reduce((groups, requirement) => {\n      const groupName = requirement.CGroupName || '其他需求';\n      if (!groups[groupName]) {\n        groups[groupName] = [];\n      }\n      groups[groupName].push(requirement);\n      return groups;\n    }, {});\n  }\n  getGroupNames() {\n    return Object.keys(this.groupedRequirements);\n  }\n  handleStyleOption(isReview) {\n    if (isReview) {\n      return {\n        'background': 'linear-gradient(to right, #008FC7, #009E9C)',\n        'color': 'white'\n      };\n    }\n    return {\n      'background': 'rgb(170,170,170)'\n    };\n  }\n  hanldeTitleOption(isReview) {\n    return !isReview ? '未選擇' : '已選擇';\n  }\n  next(isReq) {\n    if (isReq == 1) {\n      this.isRequirement = true;\n    } else if (isReq == 2) {\n      let houseRequirement = [];\n      this._houseService.apiHouseUpdateHouseRequirementPost$Json({\n        body: {\n          CHouseRequirement: houseRequirement\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.nextEvent.emit(null);\n        }\n      })).subscribe();\n    } else if (isReq == 3) {\n      let houseRequirement = [];\n      this.listHouseRequirement.forEach(x => {\n        houseRequirement.push({\n          CHouseRequirementID: x.CHouseRequirementID,\n          CCount: x.CCount\n        });\n      });\n      this._houseService.apiHouseUpdateHouseRequirementPost$Json({\n        body: {\n          CHouseRequirement: houseRequirement\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          let dataPayload = this.listHouseRequirement.length > 0; // 如果有需求項目就為 true\n          this.nextEvent.emit(dataPayload);\n        }\n      })).subscribe();\n    }\n  }\n  static #_ = this.ɵfac = function StepViChoiceComponent_Factory(t) {\n    return new (t || StepViChoiceComponent)(i0.ɵɵdirectiveInject(i1.HouseService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StepViChoiceComponent,\n    selectors: [[\"app-step-vi-choice\"]],\n    inputs: {\n      listHouseRequirement: \"listHouseRequirement\"\n    },\n    outputs: {\n      nextEvent: \"nextEvent\"\n    },\n    standalone: true,\n    features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n    decls: 3,\n    vars: 1,\n    consts: [[1, \"flex\", \"flex-col\", \"justify-center\", \"items-center\", \"w-full\"], [1, \"flex\", \"flex-col\", \"text-center\"], [1, \"text-xl\", \"text-black\", \"font-medium\"], [1, \"flex\", \"flex-col\", \"lg:!w-[800px]\", \"w-full\", \"m-auto\", \"my-6\", \"px-4\"], [\"class\", \"group-container\", 4, \"ngFor\", \"ngForOf\"], [\"pButton\", \"\", 1, \"button2\", \"!w-48\", \"butn1\", \"flex\", \"justify-center\", \"items-center\", \"my-6\", 3, \"click\"], [1, \"group-container\"], [1, \"group-title\"], [\"class\", \"requirement-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"requirement-card\"], [1, \"requirement-name\"], [1, \"requirement-title\"], [1, \"requirement-controls\"], [\"class\", \"unit-display\", 4, \"ngIf\"], [\"class\", \"price-display\", 4, \"ngIf\"], [1, \"quantity-control\"], [1, \"quantity-label\"], [1, \"quantity-input-wrapper\"], [\"placeholder\", \"0\", \"inputId\", \"horizontal-buttons\", \"spinnerMode\", \"horizontal\", \"decrementButtonClass\", \"p-button-secondary\", \"incrementButtonClass\", \"p-button-secondary\", \"incrementButtonIcon\", \"pi pi-plus\", \"decrementButtonIcon\", \"pi pi-minus\", 1, \"modern-quantity-input\", 3, \"ngModelChange\", \"ngModel\", \"showButtons\", \"min\", \"max\", \"step\"], [1, \"unit-display\"], [1, \"unit-label\"], [1, \"unit-value\"], [1, \"price-display\"], [1, \"price-label\"], [1, \"price-value\"], [1, \"flex\", \"flex-col\", \"w-full\", \"m-auto\", \"my-6\", \"items-center\"], [1, \"flex\", \"md:!flex-row\", \"flex-col\", \"md:!w-[634px]\", \"w-full\", \"md:!justify-between\", \"items-center\", 2, \"gap\", \"12px\"], [1, \"btn-noReq\", \"w-[309px]\", \"h-[47px]\", 3, \"click\"], [1, \"btn-yesReq\", \"w-[309px]\", \"h-[47px]\", 3, \"click\"]],\n    template: function StepViChoiceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵtemplate(1, StepViChoiceComponent_Conditional_1_Template, 7, 1)(2, StepViChoiceComponent_Conditional_2_Template, 6, 0);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(1, ctx.isRequirement ? 1 : 2);\n      }\n    },\n    dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, ButtonModule, i3.ButtonDirective, InputNumberModule, i4.InputNumber, FormsModule, i5.NgControlStatus, i5.NgModel],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}html[_ngcontent-%COMP%]{line-height:initial;-webkit-text-size-adjust:100%;width:100%;height:100%}body[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:#23181599;font-family:Noto Sans TC!important;box-sizing:border-box;width:100%;height:auto;overflow:auto;overscroll-behavior:none;letter-spacing:.32px}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{-webkit-text-size-adjust:none;text-size-adjust:none}main[_ngcontent-%COMP%]{display:block}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:400}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible;margin:10px 0;border:none;border-top:1px solid #000}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1rem}a[_ngcontent-%COMP%]{background-color:transparent;text-decoration:none;-webkit-tap-highlight-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:500}strong[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:600}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}p[_ngcontent-%COMP%]{font-size:1rem}img[_ngcontent-%COMP%]{border-style:none;max-width:100%}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:Noto Sans TC,\\\\5fae\\\\8edf\\\\6b63\\\\9ed1\\\\9ad4,Arial,sans-serif;font-size:1rem;line-height:1.15;margin:0;color:#2f2f2f;-webkit-tap-highlight-color:transparent;letter-spacing:2px}select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]{padding:0;background-color:transparent;border:none;cursor:pointer}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}div[role=tab][_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]{border-spacing:0px}ol[_ngcontent-%COMP%], ul[_ngcontent-%COMP%]{padding-left:0;margin-top:0}.form-check-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:none}.container[_ngcontent-%COMP%]{width:100%}@media (min-width: 640px){.container[_ngcontent-%COMP%]{max-width:640px}}@media (min-width: 768px){.container[_ngcontent-%COMP%]{max-width:768px}}@media (min-width: 1024px){.container[_ngcontent-%COMP%]{max-width:1024px}}@media (min-width: 1280px){.container[_ngcontent-%COMP%]{max-width:1280px}}@media (min-width: 1536px){.container[_ngcontent-%COMP%]{max-width:1536px}}.visible[_ngcontent-%COMP%]{visibility:visible}.collapse[_ngcontent-%COMP%]{visibility:collapse}.static[_ngcontent-%COMP%]{position:static}.absolute[_ngcontent-%COMP%]{position:absolute}.relative[_ngcontent-%COMP%]{position:relative}.inset-y-0[_ngcontent-%COMP%]{top:0;bottom:0}.bottom-0[_ngcontent-%COMP%]{bottom:0}.left-0[_ngcontent-%COMP%]{left:0}.left-1\\\\/2[_ngcontent-%COMP%]{left:50%}.left-3[_ngcontent-%COMP%]{left:.75rem}.right-0[_ngcontent-%COMP%]{right:0}.top-1[_ngcontent-%COMP%]{top:.25rem}.top-1\\\\/2[_ngcontent-%COMP%]{top:50%}.top-3[_ngcontent-%COMP%]{top:.75rem}.m-0[_ngcontent-%COMP%]{margin:0}.m-2[_ngcontent-%COMP%]{margin:.5rem}.m-auto[_ngcontent-%COMP%]{margin:auto}.mx-3[_ngcontent-%COMP%]{margin-left:.75rem;margin-right:.75rem}.mx-4[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.mx-5[_ngcontent-%COMP%]{margin-left:1.25rem;margin-right:1.25rem}.mx-\\\\__ph-0__[_ngcontent-%COMP%]{margin-left:20%;margin-right:20%}.my-12[_ngcontent-%COMP%]{margin-top:3rem;margin-bottom:3rem}.my-2[_ngcontent-%COMP%]{margin-top:.5rem;margin-bottom:.5rem}.my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.my-6[_ngcontent-%COMP%]{margin-top:1.5rem;margin-bottom:1.5rem}.\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:.75rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ml-1[_ngcontent-%COMP%]{margin-left:.25rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.ml-4[_ngcontent-%COMP%]{margin-left:1rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-4[_ngcontent-%COMP%]{margin-right:1rem}.mr-5[_ngcontent-%COMP%]{margin-right:1.25rem}.mr-8[_ngcontent-%COMP%]{margin-right:2rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.mt-14[_ngcontent-%COMP%]{margin-top:3.5rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:.75rem}.mt-4[_ngcontent-%COMP%]{margin-top:1rem}.mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.mt-6[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-7[_ngcontent-%COMP%]{margin-top:1.75rem}.mt-8[_ngcontent-%COMP%]{margin-top:2rem}.mt-\\\\__ph-0__[_ngcontent-%COMP%]{margin-top:16px}.block[_ngcontent-%COMP%]{display:block}.flex[_ngcontent-%COMP%]{display:flex}.table[_ngcontent-%COMP%]{display:table}.table-row[_ngcontent-%COMP%]{display:table-row}.grid[_ngcontent-%COMP%]{display:grid}.\\\\!h-\\\\__ph-0__[_ngcontent-%COMP%]{height:107px!important}.\\\\!h-full[_ngcontent-%COMP%]{height:100%!important}.h-28[_ngcontent-%COMP%]{height:7rem}.h-32[_ngcontent-%COMP%]{height:8rem}.h-6[_ngcontent-%COMP%]{height:1.5rem}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:100px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:145px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:280px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:31px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:40vh}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:47px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:480px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:4px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:50px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:55px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:665px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:80px}.h-auto[_ngcontent-%COMP%]{height:auto}.h-fit[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content}.h-full[_ngcontent-%COMP%]{height:100%}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:200px}.\\\\!min-h-\\\\__ph-0__[_ngcontent-%COMP%]{min-height:60vh!important}.\\\\!w-40[_ngcontent-%COMP%]{width:10rem!important}.\\\\!w-48[_ngcontent-%COMP%]{width:12rem!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:107px!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:120px!important}.\\\\!w-full[_ngcontent-%COMP%]{width:100%!important}.w-1\\\\/3[_ngcontent-%COMP%]{width:33.333333%}.w-2\\\\/3[_ngcontent-%COMP%]{width:66.666667%}.w-28[_ngcontent-%COMP%]{width:7rem}.w-6[_ngcontent-%COMP%]{width:1.5rem}.w-7\\\\/12[_ngcontent-%COMP%]{width:58.333333%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:100%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:150px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:160px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:309px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:30rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:400px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:45%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:485px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:50%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:550px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:660px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:760px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:88px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:968px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:97%}.w-full[_ngcontent-%COMP%]{width:100%}.\\\\!max-w-full[_ngcontent-%COMP%]{max-width:100%!important}.max-w-4xl[_ngcontent-%COMP%]{max-width:56rem}.max-w-\\\\__ph-0__[_ngcontent-%COMP%]{max-width:1216px}.flex-shrink[_ngcontent-%COMP%]{flex-shrink:1}.grow[_ngcontent-%COMP%]{flex-grow:1}.border-collapse[_ngcontent-%COMP%]{border-collapse:collapse}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.resize-none[_ngcontent-%COMP%]{resize:none}.grid-cols-10[_ngcontent-%COMP%]{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}.grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}.flex-row[_ngcontent-%COMP%]{flex-direction:row}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.items-start[_ngcontent-%COMP%]{align-items:flex-start}.items-center[_ngcontent-%COMP%]{align-items:center}.justify-start[_ngcontent-%COMP%]{justify-content:flex-start}.justify-end[_ngcontent-%COMP%]{justify-content:flex-end}.justify-center[_ngcontent-%COMP%]{justify-content:center}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.gap-x-1[_ngcontent-%COMP%]{column-gap:.25rem}.self-center[_ngcontent-%COMP%]{align-self:center}.overflow-hidden[_ngcontent-%COMP%]{overflow:hidden}.overflow-y-scroll[_ngcontent-%COMP%]{overflow-y:scroll}.rounded[_ngcontent-%COMP%]{border-radius:.25rem}.rounded-3xl[_ngcontent-%COMP%]{border-radius:1.5rem}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.rounded-lg[_ngcontent-%COMP%]{border-radius:.5rem}.rounded-md[_ngcontent-%COMP%]{border-radius:.375rem}.border[_ngcontent-%COMP%]{border-width:1px}.border-2[_ngcontent-%COMP%]{border-width:2px}.border-solid[_ngcontent-%COMP%]{border-style:solid}.border-blue-400[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(96 165 250 / var(--tw-border-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 143 199 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(184 166 118 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea80}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea99}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(217 217 217 / var(--tw-bg-opacity))}.bg-gray-500[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(107 114 128 / var(--tw-bg-opacity))}.bg-white[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.bg-opacity-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: .04}.object-cover[_ngcontent-%COMP%]{object-fit:cover}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-4[_ngcontent-%COMP%]{padding:1rem}.p-\\\\__ph-0__[_ngcontent-%COMP%]{padding:16px}.px-3[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-6[_ngcontent-%COMP%]{padding-left:1.5rem;padding-right:1.5rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.py-2[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.pb-4[_ngcontent-%COMP%]{padding-bottom:1rem}.pb-5[_ngcontent-%COMP%]{padding-bottom:1.25rem}.pb-8[_ngcontent-%COMP%]{padding-bottom:2rem}.pl-4[_ngcontent-%COMP%]{padding-left:1rem}.pl-6[_ngcontent-%COMP%]{padding-left:1.5rem}.pr-1[_ngcontent-%COMP%]{padding-right:.25rem}.pr-3[_ngcontent-%COMP%]{padding-right:.75rem}.pr-4[_ngcontent-%COMP%]{padding-right:1rem}.text-left[_ngcontent-%COMP%]{text-align:left}.\\\\!text-center[_ngcontent-%COMP%]{text-align:center!important}.text-center[_ngcontent-%COMP%]{text-align:center}.text-right[_ngcontent-%COMP%]{text-align:right}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-3xl[_ngcontent-%COMP%]{font-size:1.875rem;line-height:2.25rem}.text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xl[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-light[_ngcontent-%COMP%]{font-weight:300}.font-medium[_ngcontent-%COMP%]{font-weight:500}.font-normal[_ngcontent-%COMP%]{font-weight:400}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.tracking-wider[_ngcontent-%COMP%]{letter-spacing:.05em}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 143 199 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(35 24 21 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(182 152 103 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(184 166 118 / var(--tw-text-opacity))}.text-black[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-blue-400[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.text-gray-500[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-stone-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(87 83 78 / var(--tw-text-opacity))}.text-stone-900[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(28 25 23 / var(--tw-text-opacity))}.text-white[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.shadow[_ngcontent-%COMP%]{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.grayscale[_ngcontent-%COMP%]{--tw-grayscale: grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter[_ngcontent-%COMP%]{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.content[_ngcontent-%COMP%]{width:100%}@media screen and (max-width: 1024px){.content[_ngcontent-%COMP%]{width:100%;padding:0 16px 50px}}.pc[_ngcontent-%COMP%], .inpc[_ngcontent-%COMP%]{display:initial}@media screen and (max-width: 1024px){.inpc[_ngcontent-%COMP%]{display:none}}.flat[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 1024px){.flat[_ngcontent-%COMP%]{display:initial}}.mb[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.mb[_ngcontent-%COMP%]{display:initial}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{position:relative}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background-image:url(/assets/background/background2.png);width:180px;height:150px;background-size:unset;background-position:left;z-index:-1;right:0;bottom:20px}.button1[_ngcontent-%COMP%]{width:200px;height:47px;border:1px solid #CDCDCD;color:#3a4246;border-radius:28px;background-origin:border-box;background-clip:content-box,border-box;transition:all .3s ease}.button1[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#b8a676;transition:color .3s ease}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#ae9b66}@media screen and (max-width: 912px){.button1[_ngcontent-%COMP%]{width:100%}}.button2[_ngcontent-%COMP%]{width:200px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button2[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button2[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button2[_ngcontent-%COMP%]{width:100%}}.button3[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button3[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button3[_ngcontent-%COMP%]{width:100%}}.button4[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:#979797;border-radius:24px;transition:all .3s ease}.button4[_ngcontent-%COMP%]:hover{background:#7e7e7e}.button4[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button4[_ngcontent-%COMP%]{width:100%}}.checkbox-zone[_ngcontent-%COMP%]{padding:2px;border-radius:2px;position:relative;background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 1px 4px #ae9b664d}.avoid-resize-img[_ngcontent-%COMP%]{max-width:none}.fit-size[_ngcontent-%COMP%]{height:inherit;width:inherit;object-fit:cover;object-position:center}.p-calendar[_ngcontent-%COMP%]{width:100%}.p-calendar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#23181599}@media screen and (max-width: 912px){.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]{border:none;width:100%}.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#231815}@media screen and (max-width: 912px){.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]   .p-dropdown-trigger[_ngcontent-%COMP%]{border-radius:0;border-bottom:1px solid rgba(144,150,157,.4)}.p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{box-shadow:none}.p-radiobutton[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]{width:22px;height:22px;border:2px solid #CDCDCD;background-color:#fff;border-radius:50%;transition:all .3s cubic-bezier(.25,.46,.45,.94);position:relative;box-shadow:0 1px 3px #ae9b661a;cursor:pointer}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:hover{border-color:#b8a676;background:radial-gradient(circle at center,#b8a67614,#b8a67605);transform:scale(1.05);box-shadow:0 2px 8px #ae9b6626}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #b8a67640,0 2px 8px #ae9b6633;border-color:#a39460}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:active{transform:scale(.98)}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-color:#ae9b66;box-shadow:0 2px 12px #ae9b6640,inset 0 1px 2px #fff3}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:8px;height:8px;background:radial-gradient(circle,#fff,#ffffffe6);border-radius:50%;transform:translate(-50%,-50%) scale(0);box-shadow:0 1px 2px #0003;animation:_ngcontent-%COMP%_radioAppear .2s ease forwards}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);border-color:#9b8a5a;transform:scale(1.05);box-shadow:0 3px 15px #ae9b6659,inset 0 1px 2px #ffffff40}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #b8a6764d,0 3px 15px #ae9b6659}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-icon[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.2);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{transform:translate(-50%,-50%) scale(0);opacity:.6}to{transform:translate(-50%,-50%) scale(4);opacity:0}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background-color:#fff;color:#846a52;font-size:16px;border:1px solid #E6F0F3;border-radius:6px;padding:16px 32px}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 32px 8px;border-bottom:none!important;border-bottom-right-radius:0;border-bottom-left-radius:0}@media screen and (max-width: 912px){.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(-180deg)!important;transition:transform .3s linear}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(0)!important;transition:transform .3s linear}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]:focus{box-shadow:none}.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 32px 16px}@media screen and (max-width: 912px){.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 16px 16px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{width:24px;height:24px}.p-dialog-mask[_ngcontent-%COMP%]{width:100%;height:100%;background-color:#0006;top:0;z-index:20;pointer-events:all!important}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]{padding:32px 24px 16px;color:#231815;font-size:24px;text-align:center;display:flex;justify-content:center}.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]{padding:32px 24px 0;background:linear-gradient(180deg,#f3f1ea,#fff)}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]{display:none}.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]{padding:24px 24px 40px;display:flex;justify-content:center}.p-checkbox[_ngcontent-%COMP%]{width:18px;height:18px;display:inline-flex;align-items:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]{width:18px;height:18px;border:2px solid #CDCDCD;border-radius:3px;transition:.3s ease;background:#fff;position:relative;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:hover{border-color:#b8a676}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:focus{outline:none;border-color:#b8a676;box-shadow:0 0 0 2px #b8a67633}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]{background:#b8a676!important;border-color:#b8a676!important}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{color:#fff!important;font-size:12px;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:not(.p-highlight)   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(0);opacity:0}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]:before{content:\\\"\\\\e915\\\";font-family:primeicons;font-weight:400;font-style:normal;font-size:12px;display:inline-block}.p-checkbox-label[_ngcontent-%COMP%]{margin-left:8px;cursor:pointer;-webkit-user-select:none;user-select:none}.input[_ngcontent-%COMP%]{width:100%;border:none;padding:12.5px 0;border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px;height:4px}[_ngcontent-%COMP%]::-webkit-scrollbar-button{background:transparent;border-radius:2px}[_ngcontent-%COMP%]::-webkit-scrollbar-track-piece{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:4px;background-color:#90969d33}[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:transparent}.p-radiobutton-label[_ngcontent-%COMP%]{color:#231815!important;font-weight:400;cursor:pointer;transition:all .3s ease}.p-radiobutton-label[_ngcontent-%COMP%]:hover{color:#ae9b66!important}label[for*=requirement_][_ngcontent-%COMP%]{color:#231815!important;font-weight:400}label[for*=requirement_][_ngcontent-%COMP%]:hover{color:#ae9b66!important}input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%], .p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%] + label[_ngcontent-%COMP%]{color:#ae9b66!important;font-weight:600}.btn-noReq[_ngcontent-%COMP%]{border:1px solid #CDCDCD;border-radius:28px;color:#3a4246;font-size:16px;transition:all .3s ease}.btn-noReq[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.btn-yesReq[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:28px;color:#fff;font-size:16px;transition:all .3s ease;box-shadow:0 2px 8px #ae9b6633}.btn-yesReq[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.group-title[_ngcontent-%COMP%]{font-size:20px;font-weight:700;color:#ae9b66;padding:16px 0 12px;margin-bottom:20px;position:relative;display:flex;align-items:center}.group-title[_ngcontent-%COMP%]:before{content:\\\"\\\";width:4px;height:24px;background:linear-gradient(135deg,#ae9b66,#b8a676);border-radius:2px;margin-right:12px}.group-title[_ngcontent-%COMP%]:after{content:\\\"\\\";flex:1;height:1px;background:linear-gradient(90deg,rgba(184,166,118,.3) 0%,transparent 100%);margin-left:16px}.group-container[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fffc,#f8f8f899);border:1px solid rgba(184,166,118,.15);border-radius:20px;padding:24px;margin-bottom:32px;position:relative;-webkit-backdrop-filter:blur(12px);backdrop-filter:blur(12px);box-shadow:0 4px 16px #0000000a,0 2px 8px #b8a67614;transition:all .4s cubic-bezier(.4,0,.2,1);animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.group-container[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#fffffff2,#f8f8f8cc);border-color:#b8a67640;box-shadow:0 8px 32px #0000000f,0 4px 16px #b8a6761f;transform:translateY(-2px)}.group-container[_ngcontent-%COMP%]:nth-child(1){animation-delay:.1s}.group-container[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.group-container[_ngcontent-%COMP%]:nth-child(3){animation-delay:.3s}.group-container[_ngcontent-%COMP%]:nth-child(4){animation-delay:.4s}.group-container[_ngcontent-%COMP%]:nth-child(5){animation-delay:.5s}.requirement-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fffffff2,#f8f8f8e6);border:1px solid rgba(184,166,118,.15);border-radius:16px;padding:24px;margin-bottom:16px;position:relative;overflow:hidden;transition:all .4s cubic-bezier(.4,0,.2,1);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);box-shadow:0 2px 8px #0000000a,0 1px 3px #b8a6761a}.requirement-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 24px #00000014,0 4px 8px #b8a67626;border-color:#b8a67640}.requirement-card[_ngcontent-%COMP%]:last-child{margin-bottom:0}.requirement-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient(90deg,#b8a676,#ae9b66);opacity:0;transition:opacity .3s ease}.requirement-card[_ngcontent-%COMP%]:hover:before{opacity:1}.requirement-name[_ngcontent-%COMP%]{margin-bottom:20px}.requirement-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#2c3e50;margin:0;line-height:1.4;letter-spacing:.5px}.requirement-controls[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}@media (min-width: 640px){.requirement-controls[_ngcontent-%COMP%]{flex-direction:row;align-items:center;justify-content:space-between}}.unit-display[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:8px 12px;background:#b8a67614;border-radius:8px;border:1px solid rgba(184,166,118,.2)}.unit-label[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#666}.unit-value[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#ae9b66;background:#b8a6761a;padding:2px 8px;border-radius:4px}.price-display[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:8px 12px;background:#2e7d3214;border-radius:8px;border:1px solid rgba(46,125,50,.2)}.price-label[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#666}.price-value[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#2e7d32;background:#2e7d321a;padding:2px 8px;border-radius:4px}.quantity-control[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.quantity-label[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#666;min-width:40px}.quantity-input-wrapper[_ngcontent-%COMP%]{position:relative}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-input[_ngcontent-%COMP%]{width:80px;height:40px;text-align:center;border:2px solid rgba(184,166,118,.2);border-radius:10px;font-size:14px;font-weight:600;color:#2c3e50;background:#ffffffe6;transition:all .3s cubic-bezier(.4,0,.2,1)}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-input[_ngcontent-%COMP%]:focus{border-color:#b8a676;box-shadow:0 0 0 3px #b8a67626;outline:none;background:#fff}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-input[_ngcontent-%COMP%]::placeholder{color:#999;font-weight:400}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-button[_ngcontent-%COMP%]{width:32px;height:40px;border:2px solid rgba(184,166,118,.2);background:linear-gradient(135deg,#ffffffe6,#f8f8f8cc);color:#ae9b66;transition:all .3s cubic-bezier(.4,0,.2,1);-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-button[_ngcontent-%COMP%]:hover:not(:disabled){background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;border-color:#ae9b66;transform:scale(1.05);box-shadow:0 2px 8px #b8a6764d}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-button[_ngcontent-%COMP%]:active{transform:scale(.95)}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-button[_ngcontent-%COMP%]:disabled{background:#f1f1f180;color:#ccc;cursor:not-allowed}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-button[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:12px;font-weight:600}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-button-down[_ngcontent-%COMP%]{border-radius:10px 0 0 10px;border-right:none}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-button-up[_ngcontent-%COMP%]{border-radius:0 10px 10px 0;border-left:none}@media (max-width: 768px){.group-container[_ngcontent-%COMP%]{padding:20px 16px;margin-bottom:24px}.requirement-card[_ngcontent-%COMP%]{padding:20px 16px}.requirement-controls[_ngcontent-%COMP%]{gap:12px}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-input[_ngcontent-%COMP%]{width:70px;height:36px}.modern-quantity-input[_ngcontent-%COMP%]   .p-inputnumber[_ngcontent-%COMP%]   .p-inputnumber-button[_ngcontent-%COMP%]{width:28px;height:36px}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(20px)}to{opacity:1;transform:translate(0)}}.requirement-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .5s ease-out}.requirement-card[_ngcontent-%COMP%]:nth-child(1){animation-delay:.1s}.requirement-card[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.requirement-card[_ngcontent-%COMP%]:nth-child(3){animation-delay:.3s}.requirement-card[_ngcontent-%COMP%]:nth-child(4){animation-delay:.4s}.requirement-card[_ngcontent-%COMP%]:nth-child(5){animation-delay:.5s}.unit-label[_ngcontent-%COMP%], .quantity-label[_ngcontent-%COMP%]{font-weight:500;color:#64748b;white-space:nowrap;-webkit-user-select:none;user-select:none}@media not all and (min-width: 1280px){.max-xl\\\\:grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}}@media not all and (min-width: 1024px){.max-lg\\\\:bottom-12[_ngcontent-%COMP%]{bottom:3rem}.max-lg\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-lg\\\\:grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}}@media not all and (min-width: 768px){.max-md\\\\:bottom-14[_ngcontent-%COMP%]{bottom:3.5rem}.max-md\\\\:my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.max-md\\\\:ml-0[_ngcontent-%COMP%]{margin-left:0}.max-md\\\\:mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.max-md\\\\:mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.max-md\\\\:hidden[_ngcontent-%COMP%]{display:none}.max-md\\\\:h-\\\\__ph-0__[_ngcontent-%COMP%]{height:65%}.max-md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:450px!important}.max-md\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:500px}.max-md\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-md\\\\:max-w-full[_ngcontent-%COMP%]{max-width:100%}.max-md\\\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.max-md\\\\:flex-col[_ngcontent-%COMP%]{flex-direction:column}.max-md\\\\:flex-col-reverse[_ngcontent-%COMP%]{flex-direction:column-reverse}.max-md\\\\:flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.max-md\\\\:items-start[_ngcontent-%COMP%]{align-items:flex-start}}@media not all and (min-width: 640px){.max-sm\\\\:bottom-10[_ngcontent-%COMP%]{bottom:2.5rem}.max-sm\\\\:ml-8[_ngcontent-%COMP%]{margin-left:2rem}.max-sm\\\\:block[_ngcontent-%COMP%]{display:block}.max-sm\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:340px!important}.max-sm\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:380px}.max-sm\\\\:\\\\!grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))!important}.max-sm\\\\:grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.max-sm\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.max-sm\\\\:gap-0[_ngcontent-%COMP%]{gap:0px}.max-sm\\\\:px-0[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.max-sm\\\\:pl-0[_ngcontent-%COMP%]{padding-left:0}.max-sm\\\\:text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}}@media (min-width: 640px){.sm\\\\:mx-0[_ngcontent-%COMP%]{margin-left:0;margin-right:0}.sm\\\\:mb-0[_ngcontent-%COMP%]{margin-bottom:0}.sm\\\\:mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.sm\\\\:flex[_ngcontent-%COMP%]{display:flex}.sm\\\\:items-center[_ngcontent-%COMP%]{align-items:center}.sm\\\\:pl-3[_ngcontent-%COMP%]{padding-left:.75rem}}@media (min-width: 768px){.md\\\\:flex[_ngcontent-%COMP%]{display:flex}.md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:634px!important}.md\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.md\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.md\\\\:pb-20[_ngcontent-%COMP%]{padding-bottom:5rem}}@media (min-width: 1024px){.lg\\\\:\\\\!mx-0[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.lg\\\\:\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.lg\\\\:flex[_ngcontent-%COMP%]{display:flex}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:800px!important}.lg\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.lg\\\\:\\\\!px-4[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.lg\\\\:\\\\!px-\\\\__ph-0__[_ngcontent-%COMP%]{padding-left:22px!important;padding-right:22px!important}.lg\\\\:\\\\!py-\\\\__ph-0__[_ngcontent-%COMP%]{padding-top:18px!important;padding-bottom:18px!important}.lg\\\\:text-center[_ngcontent-%COMP%]{text-align:center}}\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "ButtonModule", "InputNumberModule", "FormsModule", "tap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "houseRequirement_r3", "CUnit", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "CUnitPrice", "ɵɵtemplate", "StepViChoiceComponent_Conditional_1_div_4_div_3_div_5_Template", "StepViChoiceComponent_Conditional_1_div_4_div_3_div_6_Template", "ɵɵtwoWayListener", "StepViChoiceComponent_Conditional_1_div_4_div_3_Template_p_inputNumber_ngModelChange_11_listener", "$event", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵtwoWayBindingSet", "CCount", "ɵɵresetView", "CRequirement", "ɵɵproperty", "ɵɵtwoWayProperty", "StepViChoiceComponent_Conditional_1_div_4_div_3_Template", "groupName_r4", "ctx_r4", "groupedRequirements", "StepViChoiceComponent_Conditional_1_div_4_Template", "ɵɵlistener", "StepViChoiceComponent_Conditional_1_Template_button_click_5_listener", "_r1", "ɵɵnextContext", "next", "getGroupNames", "StepViChoiceComponent_Conditional_2_Template_button_click_2_listener", "_r6", "StepViChoiceComponent_Conditional_2_Template_button_click_4_listener", "StepViChoiceComponent", "constructor", "_houseService", "listHouseRequirement", "nextEvent", "isRequirement", "ngOnInit", "groupRequirements", "ngOnChanges", "changes", "reduce", "groups", "requirement", "groupName", "CGroupName", "push", "Object", "keys", "handleStyleOption", "isReview", "hanldeTitleOption", "isReq", "houseRequirement", "apiHouseUpdateHouseRequirementPost$Json", "body", "CHouseRequirement", "pipe", "res", "StatusCode", "emit", "subscribe", "for<PERSON>ach", "x", "CHouseRequirementID", "dataPayload", "length", "_", "ɵɵdirectiveInject", "i1", "HouseService", "_2", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "StepViChoiceComponent_Template", "rf", "ctx", "StepViChoiceComponent_Conditional_1_Template", "StepViChoiceComponent_Conditional_2_Template", "ɵɵconditional", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "ButtonDirective", "i4", "InputNumber", "i5", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\components\\step-vi-choice\\step-vi-choice.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\components\\step-vi-choice\\step-vi-choice.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, OnChanges, SimpleChanges } from '@angular/core';\r\nimport { HouseRequirement, HouseRequirementRes } from '../../../../../services/api/models';\r\nimport { CommonModule, NgFor } from '@angular/common';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputNumberModule } from 'primeng/inputnumber';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { tap } from 'rxjs';\r\nimport { HouseService } from '../../../../../services/api/services';\r\n\r\n@Component({\r\n  selector: 'app-step-vi-choice',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ButtonModule,\r\n    InputNumberModule,\r\n    FormsModule,\r\n    NgFor\r\n  ],\r\n  templateUrl: './step-vi-choice.component.html',\r\n  styleUrl: './step-vi-choice.component.scss'\r\n})\r\nexport class StepViChoiceComponent implements OnInit, OnChanges {\r\n  @Input() listHouseRequirement: HouseRequirementRes[] = []\r\n  @Output() nextEvent = new EventEmitter()\r\n\r\n  isRequirement = false;\r\n  groupedRequirements: { [key: string]: HouseRequirementRes[] } = {};\r\n\r\n  constructor(\r\n    private _houseService: HouseService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.groupRequirements();\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes['listHouseRequirement']) {\r\n      this.groupRequirements();\r\n    }\r\n  }\r\n\r\n  groupRequirements(): void {\r\n    this.groupedRequirements = this.listHouseRequirement.reduce((groups, requirement) => {\r\n      const groupName = requirement.CGroupName || '其他需求';\r\n      if (!groups[groupName]) {\r\n        groups[groupName] = [];\r\n      }\r\n      groups[groupName].push(requirement);\r\n      return groups;\r\n    }, {} as { [key: string]: HouseRequirementRes[] });\r\n  }\r\n\r\n  getGroupNames(): string[] {\r\n    return Object.keys(this.groupedRequirements);\r\n  }\r\n\r\n  handleStyleOption(isReview: boolean) {\r\n    if (isReview) {\r\n      return { 'background': 'linear-gradient(to right, #008FC7, #009E9C)', 'color': 'white' }\r\n    }\r\n    return { 'background': 'rgb(170,170,170)' }\r\n  }\r\n  hanldeTitleOption(isReview: boolean) {\r\n    return !isReview ? '未選擇' : '已選擇'\r\n  }\r\n\r\n  next(isReq: number) {\r\n    if (isReq == 1) {\r\n      this.isRequirement = true;\r\n    }\r\n    else if (isReq == 2) {\r\n      let houseRequirement: HouseRequirement[] = []\r\n      this._houseService.apiHouseUpdateHouseRequirementPost$Json({\r\n        body: {\r\n          CHouseRequirement: houseRequirement\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.nextEvent.emit(null);\r\n          }\r\n        })\r\n      ).subscribe();\r\n    }\r\n    else if (isReq == 3) {\r\n      let houseRequirement: HouseRequirement[] = []\r\n      this.listHouseRequirement.forEach(x => {\r\n        houseRequirement.push({\r\n          CHouseRequirementID: x.CHouseRequirementID!,\r\n          CCount: x.CCount!\r\n        })\r\n      })\r\n      this._houseService.apiHouseUpdateHouseRequirementPost$Json({\r\n        body: {\r\n          CHouseRequirement: houseRequirement\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            let dataPayload = this.listHouseRequirement.length > 0; // 如果有需求項目就為 true\r\n            this.nextEvent.emit(dataPayload);\r\n          }\r\n        })\r\n      ).subscribe();\r\n    }\r\n  }\r\n}\r\n", "<section class=\"flex flex-col justify-center items-center w-full\">\r\n  @if(isRequirement){\r\n  <div class=\"flex flex-col text-center\">\r\n    <span class=\"text-xl text-black font-medium\">\r\n      請確認是否有其他需求\r\n    </span>\r\n    <!-- <span class=\"text-base mt-2\">\r\n              點擊項目可前往選擇\r\n          </span> -->\r\n  </div>\r\n  <div class=\"flex flex-col lg:!w-[800px] w-full m-auto my-6 px-4\">\r\n    <!-- 群組分類顯示 -->\r\n    <div *ngFor=\"let groupName of getGroupNames()\" class=\"group-container\">\r\n      <!-- 群組標題 -->\r\n      <div class=\"group-title\">\r\n        {{ groupName }}\r\n      </div> <!-- 群組內的需求項目 -->\r\n      <div class=\"requirement-card\" *ngFor=\"let houseRequirement of groupedRequirements[groupName]\">\r\n\r\n        <!-- 需求名稱 -->\r\n        <div class=\"requirement-name\">\r\n          <h3 class=\"requirement-title\">\r\n            {{houseRequirement.CRequirement!}}\r\n          </h3>\r\n        </div>\r\n\r\n        <!-- 單位和數量區塊 -->\r\n        <div class=\"requirement-controls\">\r\n          <!-- 單位顯示 -->\r\n          <div class=\"unit-display\" *ngIf=\"houseRequirement.CUnit\">\r\n            <span class=\"unit-label\">單位:</span>\r\n            <span class=\"unit-value\">{{ houseRequirement.CUnit }}</span>\r\n          </div>\r\n\r\n          <!-- 價格顯示 -->\r\n          <div class=\"price-display\" *ngIf=\"houseRequirement.CUnitPrice\">\r\n            <span class=\"price-label\">單價:</span>\r\n            <span class=\"price-value\">NT$ {{ houseRequirement.CUnitPrice | number:'1.0-0' }}</span>\r\n          </div>\r\n\r\n          <!-- 數量輸入 -->\r\n          <div class=\"quantity-control\">\r\n            <span class=\"quantity-label\">數量:</span>\r\n            <div class=\"quantity-input-wrapper\">\r\n              <p-inputNumber [(ngModel)]=\"houseRequirement.CCount\" [showButtons]=\"true\" [min]=\"0\" [max]=\"999\"\r\n                placeholder=\"0\" inputId=\"horizontal-buttons\" spinnerMode=\"horizontal\" [step]=\"1\"\r\n                decrementButtonClass=\"p-button-secondary\" incrementButtonClass=\"p-button-secondary\"\r\n                incrementButtonIcon=\"pi pi-plus\" decrementButtonIcon=\"pi pi-minus\" class=\"modern-quantity-input\">\r\n              </p-inputNumber>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <button class=\"button2 !w-48 butn1 flex justify-center items-center my-6\" pButton (click)=\"next(3)\">\r\n    送出需求\r\n  </button>\r\n  }\r\n  @else {\r\n  <div class=\"flex flex-col w-full m-auto my-6 items-center\">\r\n    <div class=\"flex md:!flex-row flex-col md:!w-[634px] w-full md:!justify-between items-center\" style=\"gap: 12px;\">\r\n      <button class=\"btn-noReq w-[309px] h-[47px]\" (click)=\"next(1)\">\r\n        尚有其他需求，按此預約洽談\r\n      </button>\r\n      <button class=\"btn-yesReq w-[309px] h-[47px]\" (click)=\"next(2)\">\r\n        無其他需求，按此下載選樣結果簽名\r\n      </button>\r\n    </div>\r\n  </div>\r\n  }\r\n\r\n</section>"], "mappings": "AAAA,SAAoBA,YAAY,QAAyD,eAAe;AAExG,SAASC,YAAY,QAAe,iBAAiB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,GAAG,QAAQ,MAAM;;;;;;;;;ICwBdC,EADF,CAAAC,cAAA,cAAyD,eAC9B;IAAAD,EAAA,CAAAE,MAAA,oBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACxD;;;;IADqBH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,iBAAA,CAAAC,mBAAA,CAAAC,KAAA,CAA4B;;;;;IAKrDP,EADF,CAAAC,cAAA,cAA+D,eACnC;IAAAD,EAAA,CAAAE,MAAA,oBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAsD;;IAClFF,EADkF,CAAAG,YAAA,EAAO,EACnF;;;;IADsBH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAQ,kBAAA,SAAAR,EAAA,CAAAS,WAAA,OAAAH,mBAAA,CAAAI,UAAA,eAAsD;;;;;;IAhBlFV,EAJJ,CAAAC,cAAA,aAA8F,cAG9D,aACE;IAC5BD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAK,EACD;IAGNH,EAAA,CAAAC,cAAA,cAAkC;IAQhCD,EANA,CAAAW,UAAA,IAAAC,8DAAA,kBAAyD,IAAAC,8DAAA,kBAMM;IAO7Db,EADF,CAAAC,cAAA,cAA8B,eACC;IAAAD,EAAA,CAAAE,MAAA,oBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAErCH,EADF,CAAAC,cAAA,eAAoC,yBAIiE;IAHpFD,EAAA,CAAAc,gBAAA,2BAAAC,iGAAAC,MAAA;MAAA,MAAAV,mBAAA,GAAAN,EAAA,CAAAiB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAnB,EAAA,CAAAoB,kBAAA,CAAAd,mBAAA,CAAAe,MAAA,EAAAL,MAAA,MAAAV,mBAAA,CAAAe,MAAA,GAAAL,MAAA;MAAA,OAAAhB,EAAA,CAAAsB,WAAA,CAAAN,MAAA;IAAA,EAAqC;IAQ5DhB,EAJQ,CAAAG,YAAA,EAAgB,EACZ,EACF,EACF,EACF;;;;IA9BAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAQ,kBAAA,MAAAF,mBAAA,CAAAiB,YAAA,MACF;IAM2BvB,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAwB,UAAA,SAAAlB,mBAAA,CAAAC,KAAA,CAA4B;IAM3BP,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAwB,UAAA,SAAAlB,mBAAA,CAAAI,UAAA,CAAiC;IAS1CV,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAyB,gBAAA,YAAAnB,mBAAA,CAAAe,MAAA,CAAqC;IACoBrB,EADnB,CAAAwB,UAAA,qBAAoB,UAAU,YAAY,WACb;;;;;IA/B1FxB,EAFF,CAAAC,cAAA,aAAuE,aAE5C;IACvBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAW,UAAA,IAAAe,wDAAA,kBAA8F;IAoChG1B,EAAA,CAAAG,YAAA,EAAM;;;;;IAtCFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAQ,kBAAA,MAAAmB,YAAA,MACF;IAC2D3B,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAwB,UAAA,YAAAI,MAAA,CAAAC,mBAAA,CAAAF,YAAA,EAAiC;;;;;;IAd9F3B,EADF,CAAAC,cAAA,aAAuC,cACQ;IAC3CD,EAAA,CAAAE,MAAA,qEACF;IAIFF,EAJE,CAAAG,YAAA,EAAO,EAIH;IACNH,EAAA,CAAAC,cAAA,aAAiE;IAE/DD,EAAA,CAAAW,UAAA,IAAAmB,kDAAA,iBAAuE;IA0CzE9B,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAAoG;IAAlBD,EAAA,CAAA+B,UAAA,mBAAAC,qEAAA;MAAAhC,EAAA,CAAAiB,aAAA,CAAAgB,GAAA;MAAA,MAAAL,MAAA,GAAA5B,EAAA,CAAAkC,aAAA;MAAA,OAAAlC,EAAA,CAAAsB,WAAA,CAASM,MAAA,CAAAO,IAAA,CAAK,CAAC,CAAC;IAAA,EAAC;IACjGnC,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IA9CoBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAwB,UAAA,YAAAI,MAAA,CAAAQ,aAAA,GAAkB;;;;;;IAmD3CpC,EAFJ,CAAAC,cAAA,cAA2D,cACwD,iBAChD;IAAlBD,EAAA,CAAA+B,UAAA,mBAAAM,qEAAA;MAAArC,EAAA,CAAAiB,aAAA,CAAAqB,GAAA;MAAA,MAAAV,MAAA,GAAA5B,EAAA,CAAAkC,aAAA;MAAA,OAAAlC,EAAA,CAAAsB,WAAA,CAASM,MAAA,CAAAO,IAAA,CAAK,CAAC,CAAC;IAAA,EAAC;IAC5DnC,EAAA,CAAAE,MAAA,uFACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAgE;IAAlBD,EAAA,CAAA+B,UAAA,mBAAAQ,qEAAA;MAAAvC,EAAA,CAAAiB,aAAA,CAAAqB,GAAA;MAAA,MAAAV,MAAA,GAAA5B,EAAA,CAAAkC,aAAA;MAAA,OAAAlC,EAAA,CAAAsB,WAAA,CAASM,MAAA,CAAAO,IAAA,CAAK,CAAC,CAAC;IAAA,EAAC;IAC7DnC,EAAA,CAAAE,MAAA,yGACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;ADhDR,OAAM,MAAOqC,qBAAqB;EAOhCC,YACUC,aAA2B;IAA3B,KAAAA,aAAa,GAAbA,aAAa;IAPd,KAAAC,oBAAoB,GAA0B,EAAE;IAC/C,KAAAC,SAAS,GAAG,IAAIlD,YAAY,EAAE;IAExC,KAAAmD,aAAa,GAAG,KAAK;IACrB,KAAAhB,mBAAmB,GAA6C,EAAE;EAI9D;EAEJiB,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,sBAAsB,CAAC,EAAE;MACnC,IAAI,CAACF,iBAAiB,EAAE;;EAE5B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,CAAClB,mBAAmB,GAAG,IAAI,CAACc,oBAAoB,CAACO,MAAM,CAAC,CAACC,MAAM,EAAEC,WAAW,KAAI;MAClF,MAAMC,SAAS,GAAGD,WAAW,CAACE,UAAU,IAAI,MAAM;MAClD,IAAI,CAACH,MAAM,CAACE,SAAS,CAAC,EAAE;QACtBF,MAAM,CAACE,SAAS,CAAC,GAAG,EAAE;;MAExBF,MAAM,CAACE,SAAS,CAAC,CAACE,IAAI,CAACH,WAAW,CAAC;MACnC,OAAOD,MAAM;IACf,CAAC,EAAE,EAA8C,CAAC;EACpD;EAEAf,aAAaA,CAAA;IACX,OAAOoB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5B,mBAAmB,CAAC;EAC9C;EAEA6B,iBAAiBA,CAACC,QAAiB;IACjC,IAAIA,QAAQ,EAAE;MACZ,OAAO;QAAE,YAAY,EAAE,6CAA6C;QAAE,OAAO,EAAE;MAAO,CAAE;;IAE1F,OAAO;MAAE,YAAY,EAAE;IAAkB,CAAE;EAC7C;EACAC,iBAAiBA,CAACD,QAAiB;IACjC,OAAO,CAACA,QAAQ,GAAG,KAAK,GAAG,KAAK;EAClC;EAEAxB,IAAIA,CAAC0B,KAAa;IAChB,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,IAAI,CAAChB,aAAa,GAAG,IAAI;KAC1B,MACI,IAAIgB,KAAK,IAAI,CAAC,EAAE;MACnB,IAAIC,gBAAgB,GAAuB,EAAE;MAC7C,IAAI,CAACpB,aAAa,CAACqB,uCAAuC,CAAC;QACzDC,IAAI,EAAE;UACJC,iBAAiB,EAAEH;;OAEtB,CAAC,CAACI,IAAI,CACLnE,GAAG,CAACoE,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACxB,SAAS,CAACyB,IAAI,CAAC,IAAI,CAAC;;MAE7B,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;KACd,MACI,IAAIT,KAAK,IAAI,CAAC,EAAE;MACnB,IAAIC,gBAAgB,GAAuB,EAAE;MAC7C,IAAI,CAACnB,oBAAoB,CAAC4B,OAAO,CAACC,CAAC,IAAG;QACpCV,gBAAgB,CAACP,IAAI,CAAC;UACpBkB,mBAAmB,EAAED,CAAC,CAACC,mBAAoB;UAC3CpD,MAAM,EAAEmD,CAAC,CAACnD;SACX,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACqB,aAAa,CAACqB,uCAAuC,CAAC;QACzDC,IAAI,EAAE;UACJC,iBAAiB,EAAEH;;OAEtB,CAAC,CAACI,IAAI,CACLnE,GAAG,CAACoE,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAIM,WAAW,GAAG,IAAI,CAAC/B,oBAAoB,CAACgC,MAAM,GAAG,CAAC,CAAC,CAAC;UACxD,IAAI,CAAC/B,SAAS,CAACyB,IAAI,CAACK,WAAW,CAAC;;MAEpC,CAAC,CAAC,CACH,CAACJ,SAAS,EAAE;;EAEjB;EAAC,QAAAM,CAAA,G;qBArFUpC,qBAAqB,EAAAxC,EAAA,CAAA6E,iBAAA,CAAAC,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBxC,qBAAqB;IAAAyC,SAAA;IAAAC,MAAA;MAAAvC,oBAAA;IAAA;IAAAwC,OAAA;MAAAvC,SAAA;IAAA;IAAAwC,UAAA;IAAAC,QAAA,GAAArF,EAAA,CAAAsF,oBAAA,EAAAtF,EAAA,CAAAuF,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCtBlC7F,EAAA,CAAAC,cAAA,iBAAkE;QA4DhED,EA3DA,CAAAW,UAAA,IAAAoF,4CAAA,OAAmB,IAAAC,4CAAA,OA2DZ;QAaThG,EAAA,CAAAG,YAAA,EAAU;;;QAxERH,EAAA,CAAAI,SAAA,EAsEC;QAtEDJ,EAAA,CAAAiG,aAAA,IAAAH,GAAA,CAAAjD,aAAA,SAsEC;;;mBD1DClD,YAAY,EAAAuG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EACZzG,YAAY,EAAA0G,EAAA,CAAAC,eAAA,EACZ1G,iBAAiB,EAAA2G,EAAA,CAAAC,WAAA,EACjB3G,WAAW,EAAA4G,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}