import { ChangeDetectionStrategy, Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { QuotationService } from '../../../services/api/services/quotation.service';
import { GetQuotationVersionsListResponseBase } from '../../../services/api/models/get-quotation-versions-list-response-base';
import { GetQuotationVersions } from '../../../services/api/models/get-quotation-versions';
import { TblQuotationItem } from '../../../services/api/models/tbl-quotation-item';
import { DialogPopupComponent } from '../../components/dialog-popup/dialog-popup.component';
import { QUOTATION_TEMPLATE } from '../../../assets/template/quotation-template';

@Component({
  selector: 'app-quotation',
  standalone: true,
  imports: [CommonModule, DialogPopupComponent],
  templateUrl: './quotation.component.html',
  styleUrl: './quotation.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QuotationComponent implements OnInit {
  quotationVersions = signal<GetQuotationVersions[]>([]);
  selectedVersion = signal<GetQuotationVersions | null>(null);
  showVersionHistory = signal(false);
  isLoading = signal(false);
  error = signal<string | null>(null);

  constructor(private quotationService: QuotationService) { }

  ngOnInit() {
    this.loadQuotationHistory();
  }

  loadQuotationHistory() {
    this.isLoading.set(true);
    this.error.set(null);

    this.quotationService.apiQuotationGetQuotationHistoryPost$Json()
      .subscribe({
        next: (response: GetQuotationVersionsListResponseBase) => {
          if (response.Entries) {
            this.quotationVersions.set(response.Entries);
            if (response.Entries.length > 0) {
              this.selectedVersion.set(response.Entries[0]); // 選擇第一個版本
            }
          } else {
            this.quotationVersions.set([]); // 確保設置為空陣列
          }
          this.isLoading.set(false);
        },
        error: (error) => {
          console.error('載入報價歷程失敗:', error);
          this.error.set('載入報價歷程失敗，請稍後再試。');
          this.quotationVersions.set([]); // 確保錯誤時清空資料
          this.selectedVersion.set(null);
          this.isLoading.set(false);
        }
      });
  }
  selectVersion(version: GetQuotationVersions) {
    this.selectedVersion.set(version);
    this.showVersionHistory.set(false); // 選擇後關閉 dialog
  }

  toggleVersionHistory() {
    this.showVersionHistory.set(!this.showVersionHistory());
  }

  onVersionHistoryClose() {
    this.showVersionHistory.set(false);
  }

  // 輔助方法來獲取狀態樣式類別
  getStatusClass(version: GetQuotationVersions): string {
    const status = this.convertStatusFromAPI(version.CQuotationStatus);
    switch (status) {
      case 'pending': return 'status-pending';
      case 'quoted': return 'status-quoted';
      case 'confirmed': return 'status-confirmed';
      case 'sent': return 'status-sent';
      case 'approved': return 'status-approved';
      case 'rejected': return 'status-rejected';
      case 'expired': return 'status-expired';
      default: return 'status-pending';
    }
  }

  // 輔助方法來獲取狀態圖示
  getStatusIcon(version: GetQuotationVersions): string {
    const status = this.convertStatusFromAPI(version.CQuotationStatus);
    switch (status) {
      case 'pending': return 'icon-clock';
      case 'quoted': return 'icon-file-text';
      case 'confirmed': return 'icon-check-circle';
      case 'sent': return 'icon-send';
      case 'approved': return 'icon-check-circle-2';
      case 'rejected': return 'icon-x-circle';
      case 'expired': return 'icon-alert-triangle';
      default: return 'icon-clock';
    }
  }

  // 輔助方法來獲取狀態文字
  getStatusText(version: GetQuotationVersions): string {
    const status = this.convertStatusFromAPI(version.CQuotationStatus);
    switch (status) {
      case 'pending': return '待報價';
      case 'quoted': return '已報價';
      case 'confirmed': return '已簽回';
      case 'sent': return '已發送';
      case 'approved': return '已核准';
      case 'rejected': return '已拒絕';
      case 'expired': return '已過期';
      default: return '待報價';
    }
  }

  printPreview() {
    // 創建新視窗顯示套印模板
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow && this.selectedVersion()) {
      const templateContent = this.generatePrintTemplate();
      printWindow.document.write(templateContent);
      printWindow.document.close();

      // 等待內容載入後執行列印
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    }
  }

  private generatePrintTemplate(): string {
    const version = this.selectedVersion();
    if (!version) return '';

    // 使用新的模板
    let template = QUOTATION_TEMPLATE;

    // 準備模板變數
    const items = version.tblQuotationItems || [];
    const subtotal = this.calculateSubtotal(items);
    const otherFee = version.CShowOther ? this.calculateOtherFee(items, version.COtherPercent) : 0;
    const totalAmount = this.calculateTotalWithOther(version);

    // 生成項目HTML
    const itemsHtml = items.map((item, index) => `
      <tr>
        <td class="text-center">${index + 1}</td>
        <td>${item.CItemName || ''}</td>
        <td class="text-right">NT$ ${(item.CUnitPrice || 0).toLocaleString()}</td>
        <td class="text-center">${item.CUnit || ''}</td>
        <td class="text-center">${item.CCount || 0}</td>
        <td class="text-right">NT$ ${(item.CSubtotal || 0).toLocaleString()}</td>
        <td>${item.CRemark || ''}</td>
      </tr>
    `).join('');

    // 生成額外費用HTML
    const additionalFeeHtml = version.CShowOther && version.COtherPercent ?
      `<div class="additional-fee">${version.COtherName || '額外費用'} (${version.COtherPercent}%)：NT$ ${otherFee.toLocaleString()}</div>` : '';

    // 替換模板變數
    template = template
      .replace(/{{buildCaseName}}/g, '建案名稱') // 這裡可以根據實際需求調整
      .replace(/{{houseHold}}/g, '戶別資訊') // 這裡可以根據實際需求調整
      .replace(/{{floor}}/g, '樓層') // 這裡可以根據實際需求調整
      .replace(/{{printDate}}/g, new Date().toLocaleDateString('zh-TW'))
      .replace(/{{itemsHtml}}/g, itemsHtml)
      .replace(/{{subtotalAmount}}/g, `NT$ ${subtotal.toLocaleString()}`)
      .replace(/{{additionalFeeHtml}}/g, additionalFeeHtml)
      .replace(/{{totalAmount}}/g, `NT$ ${totalAmount.toLocaleString()}`)
      .replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));

    return template;
  }





  viewVersionDetails(version: GetQuotationVersions) {
    this.selectedVersion.set(version);
    // 可以添加額外的檢視邏輯，如滾動到詳情區域
    const detailsElement = document.querySelector('.quotation-details');
    if (detailsElement) {
      detailsElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  compareVersion(version: GetQuotationVersions) {
    // TODO: 實作版本比較功能
    alert(`比較版本 ${version.CversionNo} 與當前版本 ${this.selectedVersion()?.CversionNo} 的功能開發中...`);
  }

  reviseQuotation() {
    // TODO: 實作修改報價功能
    alert('修改報價功能開發中...');
  }

  viewContract() {
    // TODO: 實作查看合約功能
    alert('查看合約功能開發中...');
  }

  convertStatusFromAPI(statusCode?: number): 'pending' | 'quoted' | 'confirmed' | 'sent' | 'approved' | 'rejected' | 'expired' {
    switch (statusCode) {
      case 1: return 'pending';  // 待報價
      case 2: return 'quoted';   // 已報價
      case 3: return 'confirmed'; // 已簽回
      default: return 'pending'; // 預設為待報價
    }
  }

  // 輔助方法來獲取報價編號
  getQuotationNumber(version: GetQuotationVersions): string {
    return `Q${version.CQuotationVersionID}`;
  }

  // 輔助方法來獲取版本號
  getVersionNumber(version: GetQuotationVersions): string {
    return version.CversionNo || 'v1.0';
  }

  // 輔助方法來獲取建立日期
  getCreateDate(version: GetQuotationVersions): Date {
    return version.CCreateDT ? new Date(version.CCreateDT) : new Date();
  }

  // 輔助方法來計算小計
  calculateSubtotal(items?: TblQuotationItem[] | null): number {
    if (!items) return 0;
    return items.reduce((sum, item) => sum + (item.CSubtotal || 0), 0);
  }

  // 輔助方法來計算稅額
  calculateTax(items?: TblQuotationItem[] | null): number {
    const subtotal = this.calculateSubtotal(items);
    return Math.round(subtotal * 0.1); // 10% 稅率
  }

  // 輔助方法來計算額外費用
  calculateOtherFee(items?: TblQuotationItem[] | null, otherPercent?: number): number {
    if (!otherPercent || otherPercent <= 0) return 0;
    const subtotal = this.calculateSubtotal(items);
    return Math.round(subtotal * (otherPercent / 100));
  }

  // 輔助方法來計算含額外費用的總計
  calculateTotalWithOther(version: GetQuotationVersions): number {
    const subtotal = this.calculateSubtotal(version.tblQuotationItems);
    const otherFee = version.CShowOther ? this.calculateOtherFee(version.tblQuotationItems, version.COtherPercent) : 0;
    return subtotal + otherFee;
  }

}
