﻿/* 引入色彩系統 */
@import 'colors';

/* 引入動畫系統 */
@import 'animations';

/* 響應式斷點 */
$main-webM: 1200px;
$main-webS: 1024px;
/* header切換成手機版 */
$main-mobileL: 912px;
$main-mobileM: 640px;
$main-mobileS: 450px;

/* 保持向後兼容性 */
$mainColorB: $info;
$mainColorG: #009E9C;
$mainColorGray: $bg-tertiary;
$textColor: $text-secondary;
$mainDeepG: #264D00;
$mainColorY: $warning;
$color-drop: $success;

$color-text: #3e454d;

@mixin backgroundStyle {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

@mixin submitBtnStyle {
  width: 100%;
  height: 44px;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: $text-light;
  border-radius: 8px;
  background: $gradient-primary;
  margin: 0px auto;
  border: 1px solid $primary-gold-light;
  letter-spacing: 0.32px;
  box-shadow: $shadow-md;
  transition: $transition-normal;

  &:hover {
    background: $gradient-primary-hover;
    transform: translateY(-1px);
    box-shadow: $shadow-lg;
  }
}

@mixin fundBtnStyle {
  width: fit-content;
  height: 32px;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: $text-secondary;
  border-radius: 16px;
  background-color: $bg-primary;
  border: 1px solid $border-light;
  letter-spacing: 0.32px;
  padding: 4px 16px;
  transition: $transition-normal;

  &:hover {
    border-color: $primary-gold-light;
    color: $primary-gold-dark;
    background-color: rgba(184, 166, 118, 0.05);
  }
}

@mixin fundselectedBtnStyle {
  width: fit-content;
  height: 32px;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: $text-primary;
  border-radius: 16px;
  background: $gradient-primary;
  border: 1px solid $primary-gold-light;
  letter-spacing: 0.32px;
  padding: 4px 16px;
  box-shadow: $shadow-sm;
  transition: $transition-normal;
}

@mixin submitBtnHoverStyle {
  background-color: $mainDeepRed;
  color: #fff;
  border-color: $mainDeepRed;
}

@mixin disabledBtnStyle {
  width: 100%;
  height: 44px;
  line-height: 44px;
  font-size: 1rem;
  color: $text-light;
  border-radius: 8px;
  background-color: $text-disabled;
  margin: 0px auto;
  border: 1px solid $text-disabled;
  transition: $transition-normal;
}

@mixin media-lg {
  @media screen and (min-width: 992px) {
    @content;
  }
}