﻿/* 色彩系統設計指南 */
/* ==================================== */

/* 品牌主色 - 金色漸變系統 */
/* ------------------------------------ */
$primary-gold-light: #B8A676;
/* 主要金色 - 淺色 */
$primary-gold-dark: #AE9B66;
/* 主要金色 - 深色 */
$primary-gold-darker: #9B8A5A;
/* 主要金色 - 更深 */
$primary-gold-hover: #C4B382;
/* 懸停狀態 */
$primary-gold-active: #A39460;
/* 按下狀態 */

/* 輔助色彩 */
/* ------------------------------------ */
$secondary-cream: #F3F1EA;
/* 奶油色背景 */
$secondary-cream-dark: #E8E5DC;
/* 深色奶油背景 */
$secondary-cream-light: #F8F7F4;
/* 淺色奶油背景 */

/* 文字色彩系統 */
/* ------------------------------------ */
$text-primary: #231815;
/* 主要文字色彩 */
$text-secondary: #23181599;
/* 次要文字色彩 (60% opacity) */
$text-tertiary: #3A4246;
/* 第三級文字色彩 */
$text-disabled: #979797;
/* 禁用狀態文字 */
$text-light: #FFFFFF;
/* 白色文字 */

/* 背景色彩 */
/* ------------------------------------ */
$bg-primary: #FFFFFF;
/* 主要背景 */
$bg-secondary: #F8F7F4;
/* 次要背景 */
$bg-tertiary: #E6F0F3;
/* 第三級背景 */
$bg-overlay: rgba(0, 0, 0, 0.4);
/* 遮罩背景 */

/* 邊框色彩 */
/* ------------------------------------ */
$border-light: #E5E3E1;
/* 淺色邊框 */
$border-medium: #CDCDCD;
/* 中等邊框 */
$border-accent: rgba(184, 166, 118, 0.2);
/* 金色邊框 */
$border-focus: rgba(184, 166, 118, 0.4);
/* 焦點邊框 */

/* 語義色彩 */
/* ------------------------------------ */
$success: #23A415;
/* 成功狀態 */
$success-light: rgba(35, 164, 21, 0.1);
/* 淺色成功背景 */
$warning: #FFDD64;
/* 警告狀態 */
$warning-light: rgba(255, 221, 100, 0.1);
/* 淺色警告背景 */
$error: #F1502F;
/* 錯誤狀態 */
$error-light: rgba(241, 80, 47, 0.08);
/* 淺色錯誤背景 */
$info: #008FC7;
/* 資訊狀態 */
$info-light: rgba(0, 143, 199, 0.1);
/* 淺色資訊背景 */

/* 陰影系統 */
/* ------------------------------------ */
$shadow-sm: 0 1px 3px rgba(174, 155, 102, 0.1);
$shadow-md: 0 2px 8px rgba(174, 155, 102, 0.2);
$shadow-lg: 0 4px 12px rgba(174, 155, 102, 0.3);
$shadow-xl: 0 8px 24px rgba(174, 155, 102, 0.4);

/* 漸變系統 */
/* ------------------------------------ */
$gradient-primary: linear-gradient(90deg, $primary-gold-dark 0%, $primary-gold-light 100%);
$gradient-primary-hover: linear-gradient(90deg, $primary-gold-darker 0%, $primary-gold-dark 100%);
$gradient-background: linear-gradient(180deg, $secondary-cream 0%, $bg-primary 100%);

/* 動畫時間 */
/* ------------------------------------ */
$transition-fast: 0.15s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

/* Radio Button 專屬色彩 */
/* ------------------------------------ */
$radio-bg-hover: radial-gradient(circle at center, rgba(184, 166, 118, 0.1) 0%, transparent 70%);
$radio-bg-selected: linear-gradient(135deg, $primary-gold-light 0%, $primary-gold-dark 100%);
$radio-bg-selected-hover: linear-gradient(135deg, $primary-gold-hover 0%, $primary-gold-active 100%);
$radio-inner-dot: radial-gradient(circle, $text-light 0%, rgba(255, 255, 255, 0.9) 100%);
$radio-shadow-default: 0 1px 3px rgba(174, 155, 102, 0.1);
$radio-shadow-hover: 0 2px 8px rgba(174, 155, 102, 0.15);
$radio-shadow-selected: 0 2px 8px rgba(174, 155, 102, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3), inset 0 -1px 0 rgba(0, 0, 0, 0.1);
$radio-shadow-selected-hover: 0 3px 12px rgba(174, 155, 102, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.4), inset 0 -1px 0 rgba(0, 0, 0, 0.15);

/* 容器背景色彩 */
/* ------------------------------------ */
$container-bg-subtle: rgba(184, 166, 118, 0.03);
$container-bg-hover: rgba(184, 166, 118, 0.05);
$container-bg-selected: linear-gradient(135deg, rgba(184, 166, 118, 0.08) 0%, rgba(184, 166, 118, 0.03) 100%);
$container-border-hover: rgba(184, 166, 118, 0.2);
$container-border-selected: rgba(184, 166, 118, 0.3);