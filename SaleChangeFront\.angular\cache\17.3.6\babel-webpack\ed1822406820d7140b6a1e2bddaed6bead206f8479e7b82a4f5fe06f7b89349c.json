{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { DialogModule } from 'primeng/dialog';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';\nimport { LoadingService } from '../../../../shared/services/loading.service';\nimport { ButtonModule } from 'primeng/button';\nimport { tap } from 'rxjs';\nimport html2canvas from 'html2canvas';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../services/api/services\";\nimport * as i2 from \"../../../../shared/services/message.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/radiobutton\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/dialog\";\nimport * as i10 from \"primeng/checkbox\";\nconst _c0 = () => ({\n  \"width\": \"1000px\",\n  \"min-height\": \"auto\"\n});\nconst _c1 = () => ({\n  \"600px\": \"100vw\",\n  \"auto\": \"90vw\"\n});\nconst _c2 = (a0, a1) => ({\n  \"btn-enable\": a0,\n  \"btn-disable\": a1\n});\nconst _c3 = a0 => ({\n  \"width\": a0\n});\nfunction StepIiChoiceComponent_Case_1_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17)(3, \"span\", 18);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 19);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_1_div_5_div_1_Template_button_click_7_listener() {\n      const regularChangeItem_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.gotoPickItem(regularChangeItem_r2));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const regularChangeItem_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", regularChangeItem_r2.CTotalAnswer + \"\\u9078\" + regularChangeItem_r2.CRequireAnswer, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", regularChangeItem_r2.CItemName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isDisabledOptionStep2)(\"ngStyle\", ctx_r2.handleStyleOption(regularChangeItem_r2.CIsSelect));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.hanldeTitleOption(regularChangeItem_r2.CIsSelect), \" \");\n  }\n}\nfunction StepIiChoiceComponent_Case_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, StepIiChoiceComponent_Case_1_div_5_div_1_Template, 9, 5, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.listRegularChangeItem);\n  }\n}\nfunction StepIiChoiceComponent_Case_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵtext(2, \" \\u8868\\u55AE\\u5C1A\\u672A\\u9396\\u5B9A \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StepIiChoiceComponent_Case_1_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_1_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.next());\n    });\n    i0.ɵɵtext(1, \" \\u4E0B\\u4E00\\u6B65 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.checkAllSelect())(\"ngClass\", i0.ɵɵpureFunction2(2, _c2, !ctx_r2.checkAllSelect(), ctx_r2.checkAllSelect()));\n  }\n}\nfunction StepIiChoiceComponent_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"span\", 8);\n    i0.ɵɵtext(2, \" \\u9078\\u64C7\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 9);\n    i0.ɵɵtext(4, \" \\u9EDE\\u64CA\\u9805\\u76EE\\u53EF\\u524D\\u5F80\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, StepIiChoiceComponent_Case_1_div_5_Template, 2, 1, \"div\", 10)(6, StepIiChoiceComponent_Case_1_div_6_Template, 3, 0, \"div\", 11)(7, StepIiChoiceComponent_Case_1_button_7_Template, 2, 5, \"button\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.listRegularChangeItem.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.listRegularChangeItem.length <= 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.listRegularChangeItem.length > 0);\n  }\n}\nfunction StepIiChoiceComponent_Case_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.regularChangeDetail.CItemName, \" \");\n  }\n}\nfunction StepIiChoiceComponent_Case_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u8ACB\\u9078\\u64C7 \", ctx_r2.regularChangeDetail.CRequireAnswer, \" \\u500B\\u9078\\u9805\\uFF0C\\u9EDE\\u64CA\\u53EF\\u653E\\u5927\\u6AA2\\u8996\\u5716\\u7247\\u8A73\\u7D30. \");\n  }\n}\nfunction StepIiChoiceComponent_Case_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_2_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.enlargeimg(ctx_r2.regularChangeDetail.CDesignFileUrl));\n    });\n    i0.ɵɵelementStart(1, \"div\", 34);\n    i0.ɵɵelement(2, \"img\", 35);\n    i0.ɵɵelementStart(3, \"span\", 36);\n    i0.ɵɵtext(4, \" \\u6AA2\\u8996\\u8A2D\\u8A08\\u6982\\u5FF5 \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-radioButton\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_5_Template_p_radioButton_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedRegularChangeDetail, $event) || (ctx_r2.selectedRegularChangeDetail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const regularChangeDetailData_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"inputId\", regularChangeDetailData_r8.CRegularChangeDetailId == null ? null : regularChangeDetailData_r8.CRegularChangeDetailId.toString())(\"value\", regularChangeDetailData_r8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedRegularChangeDetail);\n  }\n}\nfunction StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-checkbox\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_6_Template_p_checkbox_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedRegularChangeDetail, $event) || (ctx_r2.selectedRegularChangeDetail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_6_Template_p_checkbox_onChange_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.validateTotalAnswer());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const regularChangeDetailData_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"inputId\", regularChangeDetailData_r8.CRegularChangeDetailId == null ? null : regularChangeDetailData_r8.CRegularChangeDetailId.toString())(\"value\", regularChangeDetailData_r8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedRegularChangeDetail);\n  }\n}\nfunction StepIiChoiceComponent_Case_2_div_4_For_2_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"span\", 49);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const regularChangeDetailData_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" NT$ \", ctx_r2.formatPrice(regularChangeDetailData_r8.CPrice), \" \");\n  }\n}\nfunction StepIiChoiceComponent_Case_2_div_4_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"img\", 40);\n    i0.ɵɵpipe(3, \"addBaseFile\");\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_2_div_4_For_2_Template_img_click_2_listener() {\n      const regularChangeDetailData_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.enlargeimg(regularChangeDetailData_r8.CInfoPicture == null ? null : regularChangeDetailData_r8.CInfoPicture.CFile, regularChangeDetailData_r8.CSelectName));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 41);\n    i0.ɵɵtemplate(5, StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_5_Template, 1, 3, \"p-radioButton\", 42)(6, StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_6_Template, 1, 3);\n    i0.ɵɵelementStart(7, \"label\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 44)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, StepIiChoiceComponent_Case_2_div_4_For_2_div_12_Template, 3, 1, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const regularChangeDetailData_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(9, _c3, 100 / ctx_r2.regularChangeDetail.regularChangeDetails.length + \"%\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(3, 7, regularChangeDetailData_r8.CPicture == null ? null : regularChangeDetailData_r8.CPicture.CFile), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(5, ctx_r2.regularChangeDetail.CRequireAnswer <= 1 ? 5 : 6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", regularChangeDetailData_r8.CRegularChangeDetailId == null ? null : regularChangeDetailData_r8.CRegularChangeDetailId.toString());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", regularChangeDetailData_r8.CSelectName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", regularChangeDetailData_r8.CDescription, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", regularChangeDetailData_r8.CPrice);\n  }\n}\nfunction StepIiChoiceComponent_Case_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵrepeaterCreate(1, StepIiChoiceComponent_Case_2_div_4_For_2_Template, 13, 11, \"div\", 38, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r2.regularChangeDetail.regularChangeDetails);\n  }\n}\nfunction StepIiChoiceComponent_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, StepIiChoiceComponent_Case_2_span_1_Template, 2, 1, \"span\", 25)(2, StepIiChoiceComponent_Case_2_span_2_Template, 2, 1, \"span\", 26)(3, StepIiChoiceComponent_Case_2_div_3_Template, 5, 0, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, StepIiChoiceComponent_Case_2_div_4_Template, 3, 0, \"div\", 28);\n    i0.ɵɵelementStart(5, \"div\", 29)(6, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_2_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.reset());\n    });\n    i0.ɵɵtext(7, \" \\u8FD4\\u56DE\\u5217\\u8868 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save());\n    });\n    i0.ɵɵtext(9, \" \\u5132\\u5B58 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.regularChangeDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.regularChangeDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.regularChangeDetail == null ? null : ctx_r2.regularChangeDetail.CDesignFileUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r2.regularChangeDetail);\n  }\n}\nfunction StepIiChoiceComponent_Case_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.regularChangeDetail.CItemName, \" \");\n  }\n}\nfunction StepIiChoiceComponent_Case_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u8ACB\\u9078\\u64C7 \", ctx_r2.regularChangeDetail.CRequireAnswer, \" \\u500B\\u9078\\u9805\\uFF0C\\u9EDE\\u64CA\\u53EF\\u653E\\u5927\\u6AA2\\u8996\\u5716\\u7247\\u8A73\\u7D30\\u3002 \");\n  }\n}\nfunction StepIiChoiceComponent_Case_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_3_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.enlargeimg(ctx_r2.regularChangeDetail.CDesignFileUrl));\n    });\n    i0.ɵɵelementStart(1, \"div\", 34);\n    i0.ɵɵelement(2, \"img\", 54);\n    i0.ɵɵelementStart(3, \"span\", 36);\n    i0.ɵɵtext(4, \" \\u6AA2\\u8996\\u8A2D\\u8A08\\u6982\\u5FF5 \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-radioButton\", 62);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_5_Template_p_radioButton_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedRegularChangeDetail, $event) || (ctx_r2.selectedRegularChangeDetail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const regularChangeDetailData_r14 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"inputId\", regularChangeDetailData_r14.CRegularChangeDetailId == null ? null : regularChangeDetailData_r14.CRegularChangeDetailId.toString())(\"value\", regularChangeDetailData_r14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedRegularChangeDetail);\n  }\n}\nfunction StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-checkbox\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_6_Template_p_checkbox_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedRegularChangeDetail, $event) || (ctx_r2.selectedRegularChangeDetail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_6_Template_p_checkbox_onChange_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.validateTotalAnswer());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const regularChangeDetailData_r14 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"inputId\", regularChangeDetailData_r14.CRegularChangeDetailId == null ? null : regularChangeDetailData_r14.CRegularChangeDetailId.toString())(\"value\", regularChangeDetailData_r14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedRegularChangeDetail);\n  }\n}\nfunction StepIiChoiceComponent_Case_3_div_4_For_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"span\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const regularChangeDetailData_r14 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" NT$ \", ctx_r2.formatPrice(regularChangeDetailData_r14.CPrice), \" \");\n  }\n}\nfunction StepIiChoiceComponent_Case_3_div_4_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"label\", 58)(3, \"img\", 59);\n    i0.ɵɵpipe(4, \"addBaseFile\");\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_3_div_4_For_2_Template_img_click_3_listener() {\n      const regularChangeDetailData_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.enlargeimg(regularChangeDetailData_r14.CInfoPicture == null ? null : regularChangeDetailData_r14.CInfoPicture.CFile, regularChangeDetailData_r14.CSelectName));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_5_Template, 1, 3, \"p-radioButton\", 60)(6, StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_6_Template, 1, 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, StepIiChoiceComponent_Case_3_div_4_For_2_div_7_Template, 3, 1, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const regularChangeDetailData_r14 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", regularChangeDetailData_r14.CRegularChangeDetailId == null ? null : regularChangeDetailData_r14.CRegularChangeDetailId.toString());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(4, 4, regularChangeDetailData_r14.CPicture == null ? null : regularChangeDetailData_r14.CPicture.CFile), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(5, ctx_r2.regularChangeDetail.CRequireAnswer <= 1 ? 5 : 6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", regularChangeDetailData_r14.CPrice);\n  }\n}\nfunction StepIiChoiceComponent_Case_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵrepeaterCreate(1, StepIiChoiceComponent_Case_3_div_4_For_2_Template, 8, 6, \"div\", 56, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r2.regularChangeDetail.regularChangeDetails);\n  }\n}\nfunction StepIiChoiceComponent_Case_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, StepIiChoiceComponent_Case_3_span_1_Template, 2, 1, \"span\", 25)(2, StepIiChoiceComponent_Case_3_span_2_Template, 2, 1, \"span\", 26)(3, StepIiChoiceComponent_Case_3_div_3_Template, 5, 0, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, StepIiChoiceComponent_Case_3_div_4_Template, 3, 0, \"div\", 50);\n    i0.ɵɵelementStart(5, \"div\", 51)(6, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.reset());\n    });\n    i0.ɵɵtext(7, \" \\u8FD4\\u56DE\\u5217\\u8868 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_3_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save());\n    });\n    i0.ɵɵtext(9, \" \\u5132\\u5B58 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.regularChangeDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.regularChangeDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.regularChangeDetail == null ? null : ctx_r2.regularChangeDetail.CDesignFileUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r2.regularChangeDetail);\n  }\n}\nfunction StepIiChoiceComponent_Case_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.buildingSampleSelection.CItemName, \" \");\n  }\n}\nfunction StepIiChoiceComponent_Case_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_4_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.enlargeimg(ctx_r2.buildingSampleSelection.CDesignFileUrl));\n    });\n    i0.ɵɵelementStart(1, \"div\", 34);\n    i0.ɵɵelement(2, \"img\", 35);\n    i0.ɵɵelementStart(3, \"span\", 36);\n    i0.ɵɵtext(4, \" \\u6AA2\\u8996\\u8A2D\\u8A08\\u6982\\u5FF5 \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction StepIiChoiceComponent_Case_4_div_5_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"span\", 86);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" NT$ \", ctx_r2.formatPrice(ctx_r2.selectedBuildingSample.CPrice), \" \");\n  }\n}\nfunction StepIiChoiceComponent_Case_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68)(2, \"div\", 69)(3, \"div\", 70)(4, \"p-dropdown\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StepIiChoiceComponent_Case_4_div_5_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedBuildingSample, $event) || (ctx_r2.selectedBuildingSample = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function StepIiChoiceComponent_Case_4_div_5_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.changeBuilding($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 72);\n    i0.ɵɵtext(6, \"\\u5099\\u8A3B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 73)(8, \"p-dropdown\", 74);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StepIiChoiceComponent_Case_4_div_5_Template_p_dropdown_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedRemark, $event) || (ctx_r2.selectedRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function StepIiChoiceComponent_Case_4_div_5_Template_p_dropdown_onChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.changeRemark($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StepIiChoiceComponent_Case_4_div_5_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedBuildingSample.CRemark, $event) || (ctx_r2.selectedBuildingSample.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 76)(11, \"img\", 77);\n    i0.ɵɵpipe(12, \"addBaseFile\");\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_4_div_5_Template_img_click_11_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.enlargeimg(ctx_r2.selectedBuildingSample.CInfoPictureFile[0].CFile ? ctx_r2.selectedBuildingSample.CInfoPictureFile[0].CFile : \"\", ctx_r2.selectedBuildingSample.CSelectName));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 78)(14, \"div\", 79)(15, \"div\", 80)(16, \"img\", 81);\n    i0.ɵɵpipe(17, \"addBaseFile\");\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_4_div_5_Template_img_click_16_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.enlargeimg((ctx_r2.selectedBuildingSample.CPictureFile == null ? null : ctx_r2.selectedBuildingSample.CPictureFile.length) && ctx_r2.selectedBuildingSample.CPictureFile[0].CFile ? ctx_r2.selectedBuildingSample.CPictureFile[0].CFile : \"\", ctx_r2.selectedBuildingSample.CSelectName));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 82)(19, \"div\", 83);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, StepIiChoiceComponent_Case_4_div_5_div_21_Template, 3, 1, \"div\", 84);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r2.buildingSampleSelection.buildingSamples);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedBuildingSample);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r2.selectedBuildingSample.RegularRemark);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedRemark);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedBuildingSample.CRemark);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedRemark != null ? ctx_r2.selectedRemark.CRegularChangeItemRemarkTypeID != null ? true : false : false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (ctx_r2.selectedBuildingSample.CInfoPictureFile == null ? null : ctx_r2.selectedBuildingSample.CInfoPictureFile.length) && ctx_r2.selectedBuildingSample.CInfoPictureFile[0].CFile ? i0.ɵɵpipeBind1(12, 10, ctx_r2.selectedBuildingSample.CInfoPictureFile[0].CFile) : \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", (ctx_r2.selectedBuildingSample.CPictureFile == null ? null : ctx_r2.selectedBuildingSample.CPictureFile.length) && ctx_r2.selectedBuildingSample.CPictureFile[0].CFile ? i0.ɵɵpipeBind1(17, 12, ctx_r2.selectedBuildingSample.CPictureFile[0].CFile) : \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedBuildingSample.CDescription ? ctx_r2.selectedBuildingSample.CDescription : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuildingSample.CPrice);\n  }\n}\nfunction StepIiChoiceComponent_Case_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, StepIiChoiceComponent_Case_4_span_1_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementStart(2, \"span\", 9);\n    i0.ɵɵtext(3, \" \\u8ACB\\u9078\\u64C7 1 \\u500B\\u9078\\u9805\\uFF0C\\u9EDE\\u64CA\\u53EF\\u653E\\u5927\\u6AA2\\u8996\\u5716\\u7247\\u8A73\\u7D30\\u3002 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, StepIiChoiceComponent_Case_4_div_4_Template, 5, 0, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, StepIiChoiceComponent_Case_4_div_5_Template, 22, 14, \"div\", 66);\n    i0.ɵɵelementStart(6, \"div\", 51)(7, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_4_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.reset());\n    });\n    i0.ɵɵtext(8, \" \\u8FD4\\u56DE\\u5217\\u8868 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_Case_4_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save());\n    });\n    i0.ɵɵtext(10, \" \\u5132\\u5B58 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.buildingSampleSelection);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.regularChangeDetail == null ? null : ctx_r2.regularChangeDetail.CDesignFileUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r2.buildingSampleSelection && !!ctx_r2.selectedBuildingSample);\n  }\n}\nfunction StepIiChoiceComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 87)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.conentenlargeimg, \"\");\n  }\n}\nfunction StepIiChoiceComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function StepIiChoiceComponent_ng_template_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeimg());\n    });\n    i0.ɵɵtext(1, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class StepIiChoiceComponent {\n  constructor(_regularChangeService, _toastService) {\n    this._regularChangeService = _regularChangeService;\n    this._toastService = _toastService;\n    this.listRegularChangeItem = [];\n    this.isDisabledOptionStep2 = false;\n    this.nextEvent = new EventEmitter();\n    this.currentTypeUIChange = new EventEmitter();\n    this.refreshListRegular = new EventEmitter();\n    this.listRegularChangeItemChange = new EventEmitter();\n    this.visibleimg = false;\n  }\n  ngOnInit() {}\n  enlargeimg(img, CDescription) {\n    this.conentenlargeimg = CDescription ? CDescription : '';\n    this.visibleimg = true;\n    this.choiceenlargeimg = img;\n  }\n  closeimg() {\n    this.visibleimg = false;\n  }\n  hanldeTitleOption(isSelect) {\n    return !isSelect ? '未選擇' : '已選擇';\n  }\n  handleStyleOption(isSelect) {\n    if (isSelect) {\n      return {\n        'background': 'linear-gradient(90deg, #ae9b66, #b8a676)',\n        'color': 'white'\n      };\n    }\n    return {\n      'backgroundColor': '#E5E3E1',\n      'color': '#3A4246B2'\n    };\n  }\n  gotoPickItem(regularChange) {\n    this.currentTypeUI = regularChange.CUiType;\n    this.currentregularChangeItem = regularChange;\n    this.getRegularChangeById(regularChange).subscribe();\n  }\n  next() {\n    this.nextEvent.emit();\n  }\n  save() {\n    LoadingService.loading(true);\n    // 針對不同的 UI 類型進行不同的檢核\n    if (this.currentTypeUI === 3) {\n      // Case 3: 建材選樣 - 檢查 selectedBuildingSample\n      if (!this.selectedBuildingSample) {\n        this._toastService.showErrorMSG(\"請選擇一個選項\");\n        LoadingService.loading(false);\n        return;\n      }\n    } else {\n      // Case 1 & 2: 一般選項 - 檢查 selectedRegularChangeDetail\n      if (this.regularChangeDetail?.CRequireAnswer == 1) {\n        if (this.selectedRegularChangeDetail == null) {\n          this._toastService.showErrorMSG(\"必填數量 \" + this.regularChangeDetail?.CRequireAnswer);\n          LoadingService.loading(false);\n          return;\n        }\n      } else {\n        if (this.regularChangeDetail?.CRequireAnswer != this.selectedRegularChangeDetail?.length) {\n          this._toastService.showErrorMSG(\"必填數量 \" + this.regularChangeDetail?.CRequireAnswer);\n          LoadingService.loading(false);\n          return;\n        }\n      }\n    }\n    let screenshot = document.getElementById(\"choice-selecting\");\n    html2canvas(screenshot, {\n      allowTaint: true,\n      useCORS: true,\n      ignoreElements: element => {\n        if (element.id === \"action\") {\n          return true;\n        }\n        return false;\n      }\n    }).then(canvas => {\n      const base64image = canvas.toDataURL(\"image/png\");\n      let payload = [];\n      if (this.currentTypeUI === 3) {\n        payload.push({\n          CRegularChangeDetailId: this.handleReturnID().CRegularChangeDetailId,\n          CRegularChangeItemlId: this.handleReturnID().CRegularChangeItemId,\n          CRemark: this.currentTypeUI === 3 ? this.selectedBuildingSample.CRemark : null,\n          CResultHtml: base64image,\n          RegularRemark: {\n            CRegularChangeItemRemarkTypeID: this.currentTypeUI === 3 ? this.selectedRemark.CRegularChangeItemRemarkTypeID : null\n          }\n        });\n      } else {\n        if (this.regularChangeDetail?.CRequireAnswer == 1) {\n          if (this.selectedRegularChangeDetail) {\n            payload.push({\n              CRegularChangeDetailId: this.selectedRegularChangeDetail.CRegularChangeDetailId,\n              CRegularChangeItemlId: this.regularChangeDetail.CRegularChangeItemId,\n              CRemark: null,\n              CResultHtml: base64image,\n              RegularRemark: {\n                CRegularChangeItemRemarkTypeID: null\n              }\n            });\n          }\n        } else {\n          this.selectedRegularChangeDetail.forEach(x => {\n            payload.push({\n              CRegularChangeDetailId: x.CRegularChangeDetailId,\n              CRegularChangeItemlId: this.regularChangeDetail.CRegularChangeItemId,\n              CRemark: null,\n              CResultHtml: base64image,\n              RegularRemark: {\n                CRegularChangeItemRemarkTypeID: null\n              }\n            });\n          });\n        }\n      }\n      this._regularChangeService.apiRegularChangeItemSaveRegularChangeDetailPost$Json({\n        body: payload\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          LoadingService.loading(false);\n          this.reset();\n          this.refreshListRegular.emit();\n        }\n      })).subscribe();\n    });\n  }\n  reset() {\n    this.currentTypeUI = 0;\n    this.regularChangeDetail = {};\n    this.selectedRegularChangeDetail = [];\n    this.selectedBuildingSample = {};\n    this.buildingSampleSelection = {};\n    this.selectedRemark = {};\n  }\n  handleReturnID() {\n    let CRegularChangeItemId = undefined;\n    let CRegularChangeDetailId = undefined;\n    if (this.currentTypeUI == 3) {\n      CRegularChangeItemId = this.buildingSampleSelection.CRegularChangeItemId;\n      CRegularChangeDetailId = this.selectedBuildingSample.CRegularChangeDetailId;\n    }\n    return {\n      CRegularChangeDetailId,\n      CRegularChangeItemId\n    };\n  }\n  getRegularChangeById(regularChange) {\n    return this._regularChangeService.apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Json({\n      body: regularChange.CRegularChangeItemId\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.regularChangeDetail = res.Entries;\n        if (this.currentTypeUI === 3) {\n          // Handle building sample case - convert regularChangeDetails to building samples format\n          this.buildingSampleSelection = {\n            CDesignFileUrl: res.Entries.CDesignFileUrl,\n            CItemName: res.Entries.CItemName,\n            CRegularChangeItemId: res.Entries.CRegularChangeItemId\n            // buildingSamples: res.Entries!.regularChangeDetails?.map(detail => ({\n            //   CDescription: detail.CDescription,\n            //   CInfoPictureFile: detail.CInfoPicture ? [detail.CInfoPicture] : null,\n            //   CIsSelect: detail.CIsSelect,\n            //   CPictureFile: detail.CPicture ? [detail.CPicture] : null,\n            //   CRegularChangeDetailId: detail.CRegularChangeDetailId,\n            //   CRemark: null,\n            //   CSelectName: detail.CSelectName,\n            //   RegularRemark: detail.RegularRemark,\n            //   CPrice: detail.CPrice,\n            // }))\n          };\n          // Add \"其他（自填）\" option to each building sample\n          this.buildingSampleSelection.buildingSamples?.forEach(x => {\n            x.RegularRemark?.push({\n              CIsSelect: null,\n              CRegularChangeItemRemarkTypeID: null,\n              CTypeName: \"其他（自填）\"\n            });\n          });\n          // 確保有選中的建材樣本，優先選擇已選擇的，否則選擇第一個\n          const preSelectedSample = this.buildingSampleSelection.buildingSamples.find(x => x.CIsSelect);\n          this.selectedBuildingSample = preSelectedSample || this.buildingSampleSelection.buildingSamples[0];\n          // 確保 selectedBuildingSample 存在時才設置 selectedRemark\n          if (this.selectedBuildingSample) {\n            this.selectedRemark = this.selectedBuildingSample.RegularRemark?.find(x => x.CTypeName == this.selectedBuildingSample.CRemark) || this.selectedBuildingSample.RegularRemark?.[2] || this.selectedBuildingSample.RegularRemark?.[0] || {};\n            this.selectedBuildingSample.CRemark = this.selectedBuildingSample.CRemark || this.selectedRemark.CTypeName || '';\n          }\n        } else {\n          // Handle regular change details case\n          let tempData = res.Entries?.regularChangeDetails?.filter(x => x.CIsSelect);\n          let tempSelected;\n          tempSelected = regularChange?.CRequireAnswer == 1 ? tempData[0] : tempData;\n          this.selectedRegularChangeDetail = tempSelected;\n        }\n      }\n    }));\n  }\n  changeBuilding(event) {\n    if (this.selectedRemark.CRegularChangeItemRemarkTypeID != null) {\n      this.selectedBuildingSample.CRemark = this.selectedRemark.CTypeName;\n    }\n  }\n  changeRemark(event) {\n    if (event.value.CRegularChangeItemRemarkTypeID !== null) {\n      this.selectedBuildingSample.CRemark = event.value.CTypeName;\n    } else {\n      this.selectedBuildingSample.CRemark = \"\";\n    }\n  }\n  checkAllSelect() {\n    // return !this.listRegularChangeItem.every(x => x.CIsSelect)\n    return false;\n  }\n  validateTotalAnswer() {\n    if (this.regularChangeDetail?.CRequireAnswer) {\n      if (this.selectedRegularChangeDetail.length > this.regularChangeDetail?.CRequireAnswer) {\n        this.selectedRegularChangeDetail.pop();\n        this._toastService.showErrorMSG(\"必填數量 \" + this.regularChangeDetail?.CRequireAnswer);\n        LoadingService.loading(false);\n      }\n    }\n  }\n  formatPrice(price) {\n    if (!price) return '';\n    return price.toLocaleString('zh-TW');\n  }\n  static #_ = this.ɵfac = function StepIiChoiceComponent_Factory(t) {\n    return new (t || StepIiChoiceComponent)(i0.ɵɵdirectiveInject(i1.RegularChangeItemService), i0.ɵɵdirectiveInject(i2.ToastMessage));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StepIiChoiceComponent,\n    selectors: [[\"app-step-ii-choice\"]],\n    inputs: {\n      listRegularChangeItem: \"listRegularChangeItem\",\n      currentTypeUI: \"currentTypeUI\",\n      isDisabledOptionStep2: \"isDisabledOptionStep2\"\n    },\n    outputs: {\n      nextEvent: \"nextEvent\",\n      currentTypeUIChange: \"currentTypeUIChange\",\n      refreshListRegular: \"refreshListRegular\",\n      listRegularChangeItemChange: \"listRegularChangeItemChange\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 14,\n    vars: 13,\n    consts: [[\"id\", \"choice-selecting\", 1, \"flex\", \"flex-col\", \"justify-center\", \"items-center\", \"w-full\", \"pb-4\"], [3, \"visibleChange\", \"visible\", \"draggable\", \"closable\", \"resizable\", \"breakpoints\"], [\"pTemplate\", \"header\"], [1, \"w-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"w-[80%]\", \"h-auto\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fit-size\", 3, \"src\"], [\"pTemplate\", \"footer\"], [1, \"flex\", \"flex-col\", \"text-center\", 2, \"padding-bottom\", \"24px\"], [1, \"text-black\", \"text-xl\", \"font-bold\"], [1, \"text-base\", \"mt-2\"], [\"class\", \"flex flex-col lg:!w-[800px] w-full m-auto pb-5 px-4\", 4, \"ngIf\"], [\"class\", \"flex flex-col w-full m-auto mt-4\", 4, \"ngIf\"], [\"class\", \"text-base btn-next w-[180px] h-[47px] butn1 flex justify-center items-center my-4\", \"pButton\", \"\", 3, \"disabled\", \"ngClass\", \"click\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"lg:!w-[800px]\", \"w-full\", \"m-auto\", \"pb-5\", \"px-4\"], [\"class\", \"lg:flex items-center justify-between cursor-pointer bg-[#F3F1EA99] rounded-lg mb-2 p-4 lg:!px-4 lg:!py-[18px]\", 4, \"ngFor\", \"ngForOf\"], [1, \"lg:flex\", \"items-center\", \"justify-between\", \"cursor-pointer\", \"bg-[#F3F1EA99]\", \"rounded-lg\", \"mb-2\", \"p-4\", \"lg:!px-4\", \"lg:!py-[18px]\"], [1, \"lg:flex\", \"items-center\"], [1, \"bg-[#B8A676]\", \"px-4\", \"py-1\", \"h-[31px]\", \"w-[88px]\", \"mr-3\", \"flex\", \"justify-center\", \"items-center\", \"rounded\"], [1, \"text-base\", \"text-white\"], [1, \"text-xl\", \"text-black\", \"block\", \"lg:text-center\", \"w-full\", \"text-left\"], [\"pButton\", \"\", 1, \"flex\", \"justify-center\", \"text-white\", \"items-center\", \"font-medium\", \"rounded-3xl\", \"bg-gray-500\", \"p-2\", \"w-full\", \"lg:!w-[180px]\", \"h-[47px]\", \"py-3\", \"text-base\", 3, \"click\", \"disabled\", \"ngStyle\"], [1, \"flex\", \"flex-col\", \"w-full\", \"m-auto\", \"mt-4\"], [1, \"no-data\"], [\"pButton\", \"\", 1, \"text-base\", \"btn-next\", \"w-[180px]\", \"h-[47px]\", \"butn1\", \"flex\", \"justify-center\", \"items-center\", \"my-4\", 3, \"click\", \"disabled\", \"ngClass\"], [1, \"flex\", \"flex-col\", \"text-center\", \"justify-center\", \"items-center\"], [\"class\", \"text-2xl text-black\", 4, \"ngIf\"], [\"class\", \"text-base mt-2\", 4, \"ngIf\"], [\"class\", \"bg-[#008FC7] bg-opacity-[0.04] px-3 py-2 mt-2 cursor-pointer w-[50%] rounded-md\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"flex max-sm:block justify-center my-6\", 4, \"ngIf\"], [\"id\", \"action\", 1, \"flex\", \"items-center\", \"justify-center\", \"my-6\"], [\"pButton\", \"\", 1, \"button1\", \"butn1\", \"flex\", \"justify-center\", \"items-center\", \"mr-4\", \"!w-40\", 3, \"click\"], [\"pButton\", \"\", 1, \"button2\", \"butn1\", \"flex\", \"justify-center\", \"items-center\", \"ml-4\", \"!w-40\", 3, \"click\"], [1, \"text-2xl\", \"text-black\"], [1, \"bg-[#008FC7]\", \"bg-opacity-[0.04]\", \"px-3\", \"py-2\", \"mt-2\", \"cursor-pointer\", \"w-[50%]\", \"rounded-md\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-center\"], [\"src\", \"/assets/designFile.svg\", 1, \"mr-2\"], [1, \"text-[#008FC7]\"], [1, \"flex\", \"max-sm:block\", \"justify-center\", \"my-6\"], [1, \"flex\", \"items-center\", \"justify-center\", \"flex-col\", \"mr-5\", 3, \"ngStyle\"], [1, \"box\"], [1, \"fit-size\", 3, \"click\", \"src\"], [1, \"radiobox\", \"max-sm:bottom-10\", \"max-md:bottom-14\", \"max-lg:bottom-12\"], [\"name\", \"plan\", 1, \"mr-2\", 3, \"inputId\", \"value\", \"ngModel\"], [1, \"cursor-pointer\", \"text-xl\", \"text-center\", \"w-full\", 3, \"for\"], [1, \"flex\", \"justify-center\", \"text-base\"], [\"class\", \"flex justify-center text-sm mt-1\", 4, \"ngIf\"], [\"name\", \"plan\", 1, \"mr-2\", 3, \"ngModelChange\", \"inputId\", \"value\", \"ngModel\"], [\"name\", \"plan\", 1, \"mr-2\", 3, \"ngModelChange\", \"onChange\", \"inputId\", \"value\", \"ngModel\"], [1, \"flex\", \"justify-center\", \"text-sm\", \"mt-1\"], [1, \"text-[#B8A676]\", \"font-medium\"], [\"class\", \"grid grid-cols-10 max-xl:grid-cols-6 max-lg:grid-cols-4 my-6 w-full\", 4, \"ngIf\"], [\"id\", \"action\", 1, \"flex\", \"items-center\", \"justify-center\", \"my-6\", \"max-lg:w-full\"], [\"pButton\", \"\", 1, \"button1\", \"butn1\", \"flex\", \"justify-center\", \"items-center\", \"m-2\", 3, \"click\"], [\"pButton\", \"\", 1, \"button2\", \"butn1\", \"flex\", \"justify-center\", \"items-center\", \"m-2\", 3, \"click\"], [\"src\", \"/assets/designFile.svg\", 1, \"mr-2\", \"fit-size\"], [1, \"grid\", \"grid-cols-10\", \"max-xl:grid-cols-6\", \"max-lg:grid-cols-4\", \"my-6\", \"w-full\"], [1, \"relative\", \"!w-[107px]\", \"m-auto\", \"mb-4\"], [1, \"relative\", \"!w-[107px]\", \"!h-[107px]\"], [3, \"for\"], [1, \"fit-img-size\", 3, \"click\", \"src\"], [\"name\", \"itemColor\", 1, \"absolute\", \"left-3\", \"top-3\", 3, \"inputId\", \"value\", \"ngModel\"], [\"class\", \"text-center mt-1\", 4, \"ngIf\"], [\"name\", \"itemColor\", 1, \"absolute\", \"left-3\", \"top-3\", 3, \"ngModelChange\", \"inputId\", \"value\", \"ngModel\"], [\"name\", \"itemColor\", 1, \"absolute\", \"left-0\", \"top-1\", 3, \"ngModelChange\", \"onChange\", \"inputId\", \"value\", \"ngModel\"], [1, \"text-center\", \"mt-1\"], [1, \"text-[#B8A676]\", \"font-medium\", \"text-xs\"], [\"class\", \"flex flex-row max-md:flex-col-reverse my-6 flex-wrap w-full justify-between\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"max-md:flex-col-reverse\", \"my-6\", \"flex-wrap\", \"w-full\", \"justify-between\"], [1, \"w-[760px]\"], [1, \"flex\", \"flex-row\", \"justify-between\"], [1, \"w-[660px]\", \"h-[145px]\"], [\"appendTo\", \"body\", \"optionLabel\", \"CSelectName\", 1, \"drop100\", \"h-[47px]\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [1, \"text-base\", \"mt-3\"], [1, \"flex\"], [\"appendTo\", \"body\", \"optionLabel\", \"CTypeName\", 1, \"drop50\", \"mr-4\", \"h-[47px]\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\"], [\"pInputText\", \"\", \"placeholder\", \"\\u5728\\u6B64\\u8F38\\u5165\\u60A8\\u7684\\u6587\\u5B57\", 1, \"input\", \"input-inline\", \"ml-2\", \"fs-12\", \"!mt-0\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"h-[80px]\", \"w-[80px]\"], [1, \"object-cover\", \"m-auto\", \"w-full\", \"h-full\", \"rounded\", 3, \"click\", \"src\"], [1, \"w-[400px]\"], [1, \"flex\", \"flex-col\", \"max-md:my-4\"], [1, \"overflow-hidden\"], [1, \"object-cover\", \"w-[400px]\", \"h-[280px]\", \"rounded-md\", 3, \"click\", \"src\"], [1, \"text-left\", \"mt-3\"], [1, \"text\", \"text-base\"], [\"class\", \"text-left mt-2\", 4, \"ngIf\"], [1, \"text-left\", \"mt-2\"], [1, \"text-[#B8A676]\", \"font-medium\", \"text-base\"], [1, \"title\"], [1, \"button2\", \"m-0\", 3, \"click\"]],\n    template: function StepIiChoiceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵtemplate(1, StepIiChoiceComponent_Case_1_Template, 8, 3)(2, StepIiChoiceComponent_Case_2_Template, 10, 4)(3, StepIiChoiceComponent_Case_3_Template, 10, 4)(4, StepIiChoiceComponent_Case_4_Template, 11, 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerStart(5);\n        i0.ɵɵelementStart(6, \"p-dialog\", 1);\n        i0.ɵɵtwoWayListener(\"visibleChange\", function StepIiChoiceComponent_Template_p_dialog_visibleChange_6_listener($event) {\n          i0.ɵɵtwoWayBindingSet(ctx.visibleimg, $event) || (ctx.visibleimg = $event);\n          return $event;\n        });\n        i0.ɵɵtemplate(7, StepIiChoiceComponent_ng_template_7_Template, 3, 1, \"ng-template\", 2);\n        i0.ɵɵelementStart(8, \"div\", 3)(9, \"div\", 4);\n        i0.ɵɵelement(10, \"img\", 5);\n        i0.ɵɵpipe(11, \"addBaseFile\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(12, StepIiChoiceComponent_ng_template_12_Template, 2, 0, \"ng-template\", 6);\n        i0.ɵɵelement(13, \"div\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n      }\n      if (rf & 2) {\n        let tmp_0_0;\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(1, (tmp_0_0 = ctx.currentTypeUI) === 0 ? 1 : tmp_0_0 === 1 ? 2 : tmp_0_0 === 2 ? 3 : tmp_0_0 === 3 ? 4 : -1);\n        i0.ɵɵadvance(5);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(11, _c0));\n        i0.ɵɵtwoWayProperty(\"visible\", ctx.visibleimg);\n        i0.ɵɵproperty(\"draggable\", false)(\"closable\", false)(\"resizable\", false)(\"breakpoints\", i0.ɵɵpureFunction0(12, _c1));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(11, 9, ctx.choiceenlargeimg), i0.ɵɵsanitizeUrl);\n      }\n    },\n    dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgStyle, ButtonModule, i4.ButtonDirective, i5.PrimeTemplate, RadioButtonModule, i6.RadioButton, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, DropdownModule, i8.Dropdown, DialogModule, i9.Dialog, CheckboxModule, i10.Checkbox, BaseFilePipe],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}html[_ngcontent-%COMP%]{line-height:initial;-webkit-text-size-adjust:100%;width:100%;height:100%}body[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:#23181599;font-family:Noto Sans TC!important;box-sizing:border-box;width:100%;height:auto;overflow:auto;overscroll-behavior:none;letter-spacing:.32px}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{-webkit-text-size-adjust:none;text-size-adjust:none}main[_ngcontent-%COMP%]{display:block}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:400}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible;margin:10px 0;border:none;border-top:1px solid #000}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1rem}a[_ngcontent-%COMP%]{background-color:transparent;text-decoration:none;-webkit-tap-highlight-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:500}strong[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:600}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}p[_ngcontent-%COMP%]{font-size:1rem}img[_ngcontent-%COMP%]{border-style:none;max-width:100%}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:Noto Sans TC,\\\\5fae\\\\8edf\\\\6b63\\\\9ed1\\\\9ad4,Arial,sans-serif;font-size:1rem;line-height:1.15;margin:0;color:#2f2f2f;-webkit-tap-highlight-color:transparent;letter-spacing:2px}select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]{padding:0;background-color:transparent;border:none;cursor:pointer}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}div[role=tab][_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]{border-spacing:0px}ol[_ngcontent-%COMP%], ul[_ngcontent-%COMP%]{padding-left:0;margin-top:0}.form-check-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:none}.container[_ngcontent-%COMP%]{width:100%}@media (min-width: 640px){.container[_ngcontent-%COMP%]{max-width:640px}}@media (min-width: 768px){.container[_ngcontent-%COMP%]{max-width:768px}}@media (min-width: 1024px){.container[_ngcontent-%COMP%]{max-width:1024px}}@media (min-width: 1280px){.container[_ngcontent-%COMP%]{max-width:1280px}}@media (min-width: 1536px){.container[_ngcontent-%COMP%]{max-width:1536px}}.visible[_ngcontent-%COMP%]{visibility:visible}.collapse[_ngcontent-%COMP%]{visibility:collapse}.static[_ngcontent-%COMP%]{position:static}.absolute[_ngcontent-%COMP%]{position:absolute}.relative[_ngcontent-%COMP%]{position:relative}.inset-y-0[_ngcontent-%COMP%]{top:0;bottom:0}.bottom-0[_ngcontent-%COMP%]{bottom:0}.left-0[_ngcontent-%COMP%]{left:0}.left-1\\\\/2[_ngcontent-%COMP%]{left:50%}.left-3[_ngcontent-%COMP%]{left:.75rem}.right-0[_ngcontent-%COMP%]{right:0}.top-1[_ngcontent-%COMP%]{top:.25rem}.top-1\\\\/2[_ngcontent-%COMP%]{top:50%}.top-3[_ngcontent-%COMP%]{top:.75rem}.m-0[_ngcontent-%COMP%]{margin:0}.m-2[_ngcontent-%COMP%]{margin:.5rem}.m-auto[_ngcontent-%COMP%]{margin:auto}.mx-3[_ngcontent-%COMP%]{margin-left:.75rem;margin-right:.75rem}.mx-4[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.mx-5[_ngcontent-%COMP%]{margin-left:1.25rem;margin-right:1.25rem}.mx-\\\\__ph-0__[_ngcontent-%COMP%]{margin-left:20%;margin-right:20%}.my-12[_ngcontent-%COMP%]{margin-top:3rem;margin-bottom:3rem}.my-2[_ngcontent-%COMP%]{margin-top:.5rem;margin-bottom:.5rem}.my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.my-6[_ngcontent-%COMP%]{margin-top:1.5rem;margin-bottom:1.5rem}.\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:.75rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ml-1[_ngcontent-%COMP%]{margin-left:.25rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.ml-4[_ngcontent-%COMP%]{margin-left:1rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-4[_ngcontent-%COMP%]{margin-right:1rem}.mr-5[_ngcontent-%COMP%]{margin-right:1.25rem}.mr-8[_ngcontent-%COMP%]{margin-right:2rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.mt-14[_ngcontent-%COMP%]{margin-top:3.5rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:.75rem}.mt-4[_ngcontent-%COMP%]{margin-top:1rem}.mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.mt-6[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-7[_ngcontent-%COMP%]{margin-top:1.75rem}.mt-8[_ngcontent-%COMP%]{margin-top:2rem}.mt-\\\\__ph-0__[_ngcontent-%COMP%]{margin-top:16px}.block[_ngcontent-%COMP%]{display:block}.flex[_ngcontent-%COMP%]{display:flex}.table[_ngcontent-%COMP%]{display:table}.table-row[_ngcontent-%COMP%]{display:table-row}.grid[_ngcontent-%COMP%]{display:grid}.\\\\!h-\\\\__ph-0__[_ngcontent-%COMP%]{height:107px!important}.\\\\!h-full[_ngcontent-%COMP%]{height:100%!important}.h-28[_ngcontent-%COMP%]{height:7rem}.h-32[_ngcontent-%COMP%]{height:8rem}.h-6[_ngcontent-%COMP%]{height:1.5rem}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:100px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:145px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:280px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:31px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:40vh}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:47px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:480px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:4px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:50px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:55px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:665px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:80px}.h-auto[_ngcontent-%COMP%]{height:auto}.h-fit[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content}.h-full[_ngcontent-%COMP%]{height:100%}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:200px}.\\\\!min-h-\\\\__ph-0__[_ngcontent-%COMP%]{min-height:60vh!important}.\\\\!w-40[_ngcontent-%COMP%]{width:10rem!important}.\\\\!w-48[_ngcontent-%COMP%]{width:12rem!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:107px!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:120px!important}.\\\\!w-full[_ngcontent-%COMP%]{width:100%!important}.w-1\\\\/3[_ngcontent-%COMP%]{width:33.333333%}.w-2\\\\/3[_ngcontent-%COMP%]{width:66.666667%}.w-28[_ngcontent-%COMP%]{width:7rem}.w-6[_ngcontent-%COMP%]{width:1.5rem}.w-7\\\\/12[_ngcontent-%COMP%]{width:58.333333%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:100%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:150px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:160px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:309px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:30rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:400px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:45%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:485px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:50%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:550px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:660px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:760px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:88px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:968px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:97%}.w-full[_ngcontent-%COMP%]{width:100%}.\\\\!max-w-full[_ngcontent-%COMP%]{max-width:100%!important}.max-w-4xl[_ngcontent-%COMP%]{max-width:56rem}.max-w-\\\\__ph-0__[_ngcontent-%COMP%]{max-width:1216px}.flex-shrink[_ngcontent-%COMP%]{flex-shrink:1}.grow[_ngcontent-%COMP%]{flex-grow:1}.border-collapse[_ngcontent-%COMP%]{border-collapse:collapse}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.resize-none[_ngcontent-%COMP%]{resize:none}.grid-cols-10[_ngcontent-%COMP%]{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}.grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}.flex-row[_ngcontent-%COMP%]{flex-direction:row}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.items-start[_ngcontent-%COMP%]{align-items:flex-start}.items-center[_ngcontent-%COMP%]{align-items:center}.justify-start[_ngcontent-%COMP%]{justify-content:flex-start}.justify-end[_ngcontent-%COMP%]{justify-content:flex-end}.justify-center[_ngcontent-%COMP%]{justify-content:center}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.gap-x-1[_ngcontent-%COMP%]{column-gap:.25rem}.self-center[_ngcontent-%COMP%]{align-self:center}.overflow-hidden[_ngcontent-%COMP%]{overflow:hidden}.overflow-y-scroll[_ngcontent-%COMP%]{overflow-y:scroll}.rounded[_ngcontent-%COMP%]{border-radius:.25rem}.rounded-3xl[_ngcontent-%COMP%]{border-radius:1.5rem}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.rounded-lg[_ngcontent-%COMP%]{border-radius:.5rem}.rounded-md[_ngcontent-%COMP%]{border-radius:.375rem}.border[_ngcontent-%COMP%]{border-width:1px}.border-2[_ngcontent-%COMP%]{border-width:2px}.border-solid[_ngcontent-%COMP%]{border-style:solid}.border-blue-400[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(96 165 250 / var(--tw-border-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 143 199 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(184 166 118 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea80}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea99}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(217 217 217 / var(--tw-bg-opacity))}.bg-gray-500[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(107 114 128 / var(--tw-bg-opacity))}.bg-white[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.bg-opacity-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: .04}.object-cover[_ngcontent-%COMP%]{object-fit:cover}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-4[_ngcontent-%COMP%]{padding:1rem}.p-\\\\__ph-0__[_ngcontent-%COMP%]{padding:16px}.px-3[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-6[_ngcontent-%COMP%]{padding-left:1.5rem;padding-right:1.5rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.py-2[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.pb-4[_ngcontent-%COMP%]{padding-bottom:1rem}.pb-5[_ngcontent-%COMP%]{padding-bottom:1.25rem}.pb-8[_ngcontent-%COMP%]{padding-bottom:2rem}.pl-4[_ngcontent-%COMP%]{padding-left:1rem}.pl-6[_ngcontent-%COMP%]{padding-left:1.5rem}.pr-1[_ngcontent-%COMP%]{padding-right:.25rem}.pr-3[_ngcontent-%COMP%]{padding-right:.75rem}.pr-4[_ngcontent-%COMP%]{padding-right:1rem}.text-left[_ngcontent-%COMP%]{text-align:left}.\\\\!text-center[_ngcontent-%COMP%]{text-align:center!important}.text-center[_ngcontent-%COMP%]{text-align:center}.text-right[_ngcontent-%COMP%]{text-align:right}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-3xl[_ngcontent-%COMP%]{font-size:1.875rem;line-height:2.25rem}.text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xl[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-light[_ngcontent-%COMP%]{font-weight:300}.font-medium[_ngcontent-%COMP%]{font-weight:500}.font-normal[_ngcontent-%COMP%]{font-weight:400}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.tracking-wider[_ngcontent-%COMP%]{letter-spacing:.05em}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 143 199 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(35 24 21 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(182 152 103 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(184 166 118 / var(--tw-text-opacity))}.text-black[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-blue-400[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.text-gray-500[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-stone-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(87 83 78 / var(--tw-text-opacity))}.text-stone-900[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(28 25 23 / var(--tw-text-opacity))}.text-white[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.shadow[_ngcontent-%COMP%]{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.grayscale[_ngcontent-%COMP%]{--tw-grayscale: grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter[_ngcontent-%COMP%]{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.content[_ngcontent-%COMP%]{width:100%}@media screen and (max-width: 1024px){.content[_ngcontent-%COMP%]{width:100%;padding:0 16px 50px}}.pc[_ngcontent-%COMP%], .inpc[_ngcontent-%COMP%]{display:initial}@media screen and (max-width: 1024px){.inpc[_ngcontent-%COMP%]{display:none}}.flat[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 1024px){.flat[_ngcontent-%COMP%]{display:initial}}.mb[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.mb[_ngcontent-%COMP%]{display:initial}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{position:relative}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background-image:url(/assets/background/background2.png);width:180px;height:150px;background-size:unset;background-position:left;z-index:-1;right:0;bottom:20px}.button1[_ngcontent-%COMP%]{width:200px;height:47px;border:1px solid #CDCDCD;color:#3a4246;border-radius:28px;background-origin:border-box;background-clip:content-box,border-box;transition:all .3s ease}.button1[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#b8a676;transition:color .3s ease}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#ae9b66}@media screen and (max-width: 912px){.button1[_ngcontent-%COMP%]{width:100%}}.button2[_ngcontent-%COMP%]{width:200px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button2[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button2[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button2[_ngcontent-%COMP%]{width:100%}}.button3[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button3[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button3[_ngcontent-%COMP%]{width:100%}}.button4[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:#979797;border-radius:24px;transition:all .3s ease}.button4[_ngcontent-%COMP%]:hover{background:#7e7e7e}.button4[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button4[_ngcontent-%COMP%]{width:100%}}.checkbox-zone[_ngcontent-%COMP%]{padding:2px;border-radius:2px;position:relative;background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 1px 4px #ae9b664d}.avoid-resize-img[_ngcontent-%COMP%]{max-width:none}.fit-size[_ngcontent-%COMP%]{height:inherit;width:inherit;object-fit:cover;object-position:center}.p-calendar[_ngcontent-%COMP%]{width:100%}.p-calendar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#23181599}@media screen and (max-width: 912px){.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]{border:none;width:100%}.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#231815}@media screen and (max-width: 912px){.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]   .p-dropdown-trigger[_ngcontent-%COMP%]{border-radius:0;border-bottom:1px solid rgba(144,150,157,.4)}.p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{box-shadow:none}.p-radiobutton[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]{width:22px;height:22px;border:2px solid #CDCDCD;background-color:#fff;border-radius:50%;transition:all .3s cubic-bezier(.25,.46,.45,.94);position:relative;box-shadow:0 1px 3px #ae9b661a;cursor:pointer}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:hover{border-color:#b8a676;background:radial-gradient(circle at center,#b8a67614,#b8a67605);transform:scale(1.05);box-shadow:0 2px 8px #ae9b6626}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #b8a67640,0 2px 8px #ae9b6633;border-color:#a39460}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:active{transform:scale(.98)}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-color:#ae9b66;box-shadow:0 2px 12px #ae9b6640,inset 0 1px 2px #fff3}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:8px;height:8px;background:radial-gradient(circle,#fff,#ffffffe6);border-radius:50%;transform:translate(-50%,-50%) scale(0);box-shadow:0 1px 2px #0003;animation:_ngcontent-%COMP%_radioAppear .2s ease forwards}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);border-color:#9b8a5a;transform:scale(1.05);box-shadow:0 3px 15px #ae9b6659,inset 0 1px 2px #ffffff40}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #b8a6764d,0 3px 15px #ae9b6659}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-icon[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.2);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{transform:translate(-50%,-50%) scale(0);opacity:.6}to{transform:translate(-50%,-50%) scale(4);opacity:0}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background-color:#fff;color:#846a52;font-size:16px;border:1px solid #E6F0F3;border-radius:6px;padding:16px 32px}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 32px 8px;border-bottom:none!important;border-bottom-right-radius:0;border-bottom-left-radius:0}@media screen and (max-width: 912px){.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(-180deg)!important;transition:transform .3s linear}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(0)!important;transition:transform .3s linear}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]:focus{box-shadow:none}.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 32px 16px}@media screen and (max-width: 912px){.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 16px 16px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{width:24px;height:24px}.p-dialog-mask[_ngcontent-%COMP%]{width:100%;height:100%;background-color:#0006;top:0;z-index:20;pointer-events:all!important}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]{padding:32px 24px 16px;color:#231815;font-size:24px;text-align:center;display:flex;justify-content:center}.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]{padding:32px 24px 0;background:linear-gradient(180deg,#f3f1ea,#fff)}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]{display:none}.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]{padding:24px 24px 40px;display:flex;justify-content:center}.p-checkbox[_ngcontent-%COMP%]{width:18px;height:18px;display:inline-flex;align-items:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]{width:18px;height:18px;border:2px solid #CDCDCD;border-radius:3px;transition:.3s ease;background:#fff;position:relative;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:hover{border-color:#b8a676}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:focus{outline:none;border-color:#b8a676;box-shadow:0 0 0 2px #b8a67633}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]{background:#b8a676!important;border-color:#b8a676!important}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{color:#fff!important;font-size:12px;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:not(.p-highlight)   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(0);opacity:0}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]:before{content:\\\"\\\\e915\\\";font-family:primeicons;font-weight:400;font-style:normal;font-size:12px;display:inline-block}.p-checkbox-label[_ngcontent-%COMP%]{margin-left:8px;cursor:pointer;-webkit-user-select:none;user-select:none}.input[_ngcontent-%COMP%]{width:100%;border:none;padding:12.5px 0;border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px;height:4px}[_ngcontent-%COMP%]::-webkit-scrollbar-button{background:transparent;border-radius:2px}[_ngcontent-%COMP%]::-webkit-scrollbar-track-piece{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:4px;background-color:#90969d33}[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:transparent}.p-radiobutton-label[_ngcontent-%COMP%]{color:#231815!important;font-weight:400;cursor:pointer;transition:all .3s ease}.p-radiobutton-label[_ngcontent-%COMP%]:hover{color:#ae9b66!important}label[for*=requirement_][_ngcontent-%COMP%]{color:#231815!important;font-weight:400}label[for*=requirement_][_ngcontent-%COMP%]:hover{color:#ae9b66!important}input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%], .p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%] + label[_ngcontent-%COMP%]{color:#ae9b66!important;font-weight:600}[_nghost-%COMP%]   .disabled[_ngcontent-%COMP%]{background-color:#aaa}  .radiobox{width:175px;height:45px;box-shadow:0 1px 8px #454a4f1f;padding:8px 24px;background-color:#fff;border-radius:31px;display:flex;align-items:center;position:relative;top:-20px}@media screen and (max-width: 912px){  .radiobox{min-width:125px}}  .radiobox label{width:max-content}.no-data[_ngcontent-%COMP%]{padding:10px 20px;background-color:#ff0;font-size:20px;text-align:center;border-radius:5px}.btn-enable[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.btn-enable[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.btn-disable[_ngcontent-%COMP%]{background-color:#979797!important;color:#231815!important;transition:all .3s ease}.btn-next[_ngcontent-%COMP%]{padding:12px 24px;color:#fff;border-radius:24px}.fit-img-size[_ngcontent-%COMP%]{width:107px;height:107px;object-fit:cover;object-position:center}@media not all and (min-width: 1280px){.max-xl\\\\:grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}}@media not all and (min-width: 1024px){.max-lg\\\\:bottom-12[_ngcontent-%COMP%]{bottom:3rem}.max-lg\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-lg\\\\:grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}}@media not all and (min-width: 768px){.max-md\\\\:bottom-14[_ngcontent-%COMP%]{bottom:3.5rem}.max-md\\\\:my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.max-md\\\\:ml-0[_ngcontent-%COMP%]{margin-left:0}.max-md\\\\:mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.max-md\\\\:mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.max-md\\\\:hidden[_ngcontent-%COMP%]{display:none}.max-md\\\\:h-\\\\__ph-0__[_ngcontent-%COMP%]{height:65%}.max-md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:450px!important}.max-md\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:500px}.max-md\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-md\\\\:max-w-full[_ngcontent-%COMP%]{max-width:100%}.max-md\\\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.max-md\\\\:flex-col[_ngcontent-%COMP%]{flex-direction:column}.max-md\\\\:flex-col-reverse[_ngcontent-%COMP%]{flex-direction:column-reverse}.max-md\\\\:flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.max-md\\\\:items-start[_ngcontent-%COMP%]{align-items:flex-start}}@media not all and (min-width: 640px){.max-sm\\\\:bottom-10[_ngcontent-%COMP%]{bottom:2.5rem}.max-sm\\\\:ml-8[_ngcontent-%COMP%]{margin-left:2rem}.max-sm\\\\:block[_ngcontent-%COMP%]{display:block}.max-sm\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:340px!important}.max-sm\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:380px}.max-sm\\\\:\\\\!grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))!important}.max-sm\\\\:grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.max-sm\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.max-sm\\\\:gap-0[_ngcontent-%COMP%]{gap:0px}.max-sm\\\\:px-0[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.max-sm\\\\:pl-0[_ngcontent-%COMP%]{padding-left:0}.max-sm\\\\:text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}}@media (min-width: 640px){.sm\\\\:mx-0[_ngcontent-%COMP%]{margin-left:0;margin-right:0}.sm\\\\:mb-0[_ngcontent-%COMP%]{margin-bottom:0}.sm\\\\:mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.sm\\\\:flex[_ngcontent-%COMP%]{display:flex}.sm\\\\:items-center[_ngcontent-%COMP%]{align-items:center}.sm\\\\:pl-3[_ngcontent-%COMP%]{padding-left:.75rem}}@media (min-width: 768px){.md\\\\:flex[_ngcontent-%COMP%]{display:flex}.md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:634px!important}.md\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.md\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.md\\\\:pb-20[_ngcontent-%COMP%]{padding-bottom:5rem}}@media (min-width: 1024px){.lg\\\\:\\\\!mx-0[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.lg\\\\:\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.lg\\\\:flex[_ngcontent-%COMP%]{display:flex}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:800px!important}.lg\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.lg\\\\:\\\\!px-4[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.lg\\\\:\\\\!px-\\\\__ph-0__[_ngcontent-%COMP%]{padding-left:22px!important;padding-right:22px!important}.lg\\\\:\\\\!py-\\\\__ph-0__[_ngcontent-%COMP%]{padding-top:18px!important;padding-bottom:18px!important}.lg\\\\:text-center[_ngcontent-%COMP%]{text-align:center}}\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "EventEmitter", "FormsModule", "DialogModule", "DropdownModule", "RadioButtonModule", "BaseFilePipe", "LoadingService", "ButtonModule", "tap", "html2canvas", "CheckboxModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "StepIiChoiceComponent_Case_1_div_5_div_1_Template_button_click_7_listener", "regularChangeItem_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "gotoPickItem", "ɵɵadvance", "ɵɵtextInterpolate1", "CTotalAnswer", "CRequireAnswer", "CItemName", "ɵɵproperty", "isDisabledOptionStep2", "handleStyleOption", "CIsSelect", "hanldeTitleOption", "ɵɵtemplate", "StepIiChoiceComponent_Case_1_div_5_div_1_Template", "listRegularChangeItem", "StepIiChoiceComponent_Case_1_button_7_Template_button_click_0_listener", "_r4", "next", "checkAllSelect", "ɵɵpureFunction2", "_c2", "StepIiChoiceComponent_Case_1_div_5_Template", "StepIiChoiceComponent_Case_1_div_6_Template", "StepIiChoiceComponent_Case_1_button_7_Template", "length", "regularChangeDetail", "StepIiChoiceComponent_Case_2_div_3_Template_div_click_0_listener", "_r6", "enlargeimg", "CDesignFileUrl", "ɵɵelement", "ɵɵtwoWayListener", "StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_5_Template_p_radioButton_ngModelChange_0_listener", "$event", "_r9", "ɵɵtwoWayBindingSet", "selectedRegularChangeDetail", "regularChangeDetailData_r8", "CRegularChangeDetailId", "toString", "ɵɵtwoWayProperty", "StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_6_Template_p_checkbox_ngModelChange_0_listener", "_r10", "StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_6_Template_p_checkbox_onChange_0_listener", "validateTotalAnswer", "formatPrice", "CPrice", "StepIiChoiceComponent_Case_2_div_4_For_2_Template_img_click_2_listener", "_r7", "CInfoPicture", "CFile", "CSelectName", "StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_5_Template", "StepIiChoiceComponent_Case_2_div_4_For_2_Conditional_6_Template", "StepIiChoiceComponent_Case_2_div_4_For_2_div_12_Template", "ɵɵpureFunction1", "_c3", "regularChangeDetails", "ɵɵpipeBind1", "CPicture", "ɵɵsanitizeUrl", "ɵɵconditional", "CDescription", "ɵɵrepeaterCreate", "StepIiChoiceComponent_Case_2_div_4_For_2_Template", "ɵɵrepeaterTrackByIndex", "ɵɵrepeater", "StepIiChoiceComponent_Case_2_span_1_Template", "StepIiChoiceComponent_Case_2_span_2_Template", "StepIiChoiceComponent_Case_2_div_3_Template", "StepIiChoiceComponent_Case_2_div_4_Template", "StepIiChoiceComponent_Case_2_Template_button_click_6_listener", "_r5", "reset", "StepIiChoiceComponent_Case_2_Template_button_click_8_listener", "save", "StepIiChoiceComponent_Case_3_div_3_Template_div_click_0_listener", "_r12", "StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_5_Template_p_radioButton_ngModelChange_0_listener", "_r15", "regularChangeDetailData_r14", "StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_6_Template_p_checkbox_ngModelChange_0_listener", "_r16", "StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_6_Template_p_checkbox_onChange_0_listener", "StepIiChoiceComponent_Case_3_div_4_For_2_Template_img_click_3_listener", "_r13", "StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_5_Template", "StepIiChoiceComponent_Case_3_div_4_For_2_Conditional_6_Template", "StepIiChoiceComponent_Case_3_div_4_For_2_div_7_Template", "StepIiChoiceComponent_Case_3_div_4_For_2_Template", "StepIiChoiceComponent_Case_3_span_1_Template", "StepIiChoiceComponent_Case_3_span_2_Template", "StepIiChoiceComponent_Case_3_div_3_Template", "StepIiChoiceComponent_Case_3_div_4_Template", "StepIiChoiceComponent_Case_3_Template_button_click_6_listener", "_r11", "StepIiChoiceComponent_Case_3_Template_button_click_8_listener", "buildingSampleSelection", "StepIiChoiceComponent_Case_4_div_4_Template_div_click_0_listener", "_r18", "selectedBuildingSample", "StepIiChoiceComponent_Case_4_div_5_Template_p_dropdown_ngModelChange_4_listener", "_r19", "changeBuilding", "StepIiChoiceComponent_Case_4_div_5_Template_p_dropdown_ngModelChange_8_listener", "selectedRemark", "StepIiChoiceComponent_Case_4_div_5_Template_p_dropdown_onChange_8_listener", "changeRemark", "StepIiChoiceComponent_Case_4_div_5_Template_input_ngModelChange_9_listener", "CRemark", "StepIiChoiceComponent_Case_4_div_5_Template_img_click_11_listener", "CInfoPictureFile", "StepIiChoiceComponent_Case_4_div_5_Template_img_click_16_listener", "CPictureFile", "StepIiChoiceComponent_Case_4_div_5_div_21_Template", "buildingSamples", "RegularRemark", "CRegularChangeItemRemarkTypeID", "ɵɵtextInterpolate", "StepIiChoiceComponent_Case_4_span_1_Template", "StepIiChoiceComponent_Case_4_div_4_Template", "StepIiChoiceComponent_Case_4_div_5_Template", "StepIiChoiceComponent_Case_4_Template_button_click_7_listener", "_r17", "StepIiChoiceComponent_Case_4_Template_button_click_9_listener", "conentenlargeimg", "StepIiChoiceComponent_ng_template_12_Template_button_click_0_listener", "_r20", "closeimg", "StepIiChoiceComponent", "constructor", "_regularChangeService", "_toastService", "nextEvent", "currentTypeUIChange", "refreshListRegular", "listRegularChangeItemChange", "visibleimg", "ngOnInit", "img", "choiceenlargeimg", "isSelect", "regularChange", "currentTypeUI", "CUiType", "currentregularChangeItem", "getRegularChangeById", "subscribe", "emit", "loading", "showErrorMSG", "screenshot", "document", "getElementById", "<PERSON><PERSON><PERSON><PERSON>", "useCORS", "ignoreElements", "element", "id", "then", "canvas", "base64image", "toDataURL", "payload", "push", "handleReturnID", "CRegularChangeItemlId", "CRegularChangeItemId", "CResultHtml", "for<PERSON>ach", "x", "apiRegularChangeItemSaveRegularChangeDetailPost$Json", "body", "pipe", "res", "StatusCode", "undefined", "apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Json", "Entries", "CTypeName", "preSelectedSample", "find", "tempData", "filter", "tempSelected", "event", "value", "pop", "price", "toLocaleString", "_", "ɵɵdirectiveInject", "i1", "RegularChangeItemService", "i2", "ToastMessage", "_2", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "StepIiChoiceComponent_Template", "rf", "ctx", "StepIiChoiceComponent_Case_1_Template", "StepIiChoiceComponent_Case_2_Template", "StepIiChoiceComponent_Case_3_Template", "StepIiChoiceComponent_Case_4_Template", "ɵɵelementContainerStart", "StepIiChoiceComponent_Template_p_dialog_visibleChange_6_listener", "StepIiChoiceComponent_ng_template_7_Template", "StepIiChoiceComponent_ng_template_12_Template", "tmp_0_0", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "_c1", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgStyle", "i4", "ButtonDirective", "i5", "PrimeTemplate", "i6", "RadioButton", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i8", "Dropdown", "i9", "Dialog", "i10", "Checkbox", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\components\\step-ii-choice\\step-ii-choice.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\components\\step-ii-choice\\step-ii-choice.component.html"], "sourcesContent": ["import { CommonModule, NgFor } from '@angular/common';\r\nimport { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport {\r\n  BuildingSample,\r\n  GetBuildingSampleSelectionRes,\r\n  GetRegularChangeDetailByItemIdRes, RegularChangeDetail,\r\n  RegularRemark,\r\n  SaveRegularChangeDetailRequest,\r\n  GetListRegularChangeItemRes\r\n} from '../../../../../services/api/models';\r\nimport { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';\r\nimport { LoadingService } from '../../../../shared/services/loading.service';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { Observable, finalize, forkJoin, tap } from 'rxjs';\r\nimport html2canvas from 'html2canvas';\r\nimport { RegularChangeItemService } from '../../../../../services/api/services';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { ToastMessage } from '../../../../shared/services/message.service';\r\n@Component({\r\n  selector: 'app-step-ii-choice',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ButtonModule,\r\n    RadioButtonModule,\r\n    FormsModule,\r\n    DropdownModule,\r\n    DialogModule,\r\n    CheckboxModule,\r\n\r\n    NgFor,\r\n\r\n    BaseFilePipe,\r\n  ],\r\n  templateUrl: './step-ii-choice.component.html',\r\n  styleUrl: './step-ii-choice.component.scss'\r\n})\r\n\r\nexport class StepIiChoiceComponent implements OnInit {\r\n  @Input() listRegularChangeItem: GetListRegularChangeItemRes[] = []\r\n  @Input() currentTypeUI!: number\r\n  @Input() isDisabledOptionStep2: boolean = false;\r\n\r\n  @Output() nextEvent = new EventEmitter()\r\n  @Output() currentTypeUIChange = new EventEmitter();\r\n  @Output() refreshListRegular = new EventEmitter()\r\n  @Output() listRegularChangeItemChange = new EventEmitter()\r\n\r\n  buildingSampleSelection!: GetBuildingSampleSelectionRes\r\n  choiceenlargeimg: string | undefined;\r\n  conentenlargeimg: string | undefined;\r\n\r\n  currentregularChangeItem!: GetListRegularChangeItemRes\r\n  regularChangeDetail!: GetRegularChangeDetailByItemIdRes\r\n  selectedRegularChangeDetail!: RegularChangeDetail[] //For UIType = 1 && 2\r\n  selectedBuildingSample!: BuildingSample //For UIType = 3\r\n  selectedRemark!: RegularRemark\r\n  visibleimg: boolean = false;\r\n\r\n  constructor(\r\n    private _regularChangeService: RegularChangeItemService,\r\n    private _toastService: ToastMessage,\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n  enlargeimg(img?: string | any, CDescription?: | any) {\r\n    this.conentenlargeimg = CDescription ? CDescription : ''\r\n    this.visibleimg = true;\r\n    this.choiceenlargeimg = img;\r\n  }\r\n  closeimg() {\r\n    this.visibleimg = false;\r\n  }\r\n\r\n  hanldeTitleOption(isSelect: boolean) {\r\n    return !isSelect ? '未選擇' : '已選擇'\r\n  }\r\n\r\n  handleStyleOption(isSelect: boolean) {\r\n    if (isSelect) {\r\n      return { 'background': 'linear-gradient(90deg, #ae9b66, #b8a676)', 'color': 'white' }\r\n    }\r\n    return { 'backgroundColor': '#E5E3E1', 'color': '#3A4246B2' }\r\n  }\r\n\r\n  gotoPickItem(regularChange: GetListRegularChangeItemRes) {\r\n    this.currentTypeUI = regularChange.CUiType!\r\n    this.currentregularChangeItem = regularChange\r\n    this.getRegularChangeById(regularChange).subscribe()\r\n  }\r\n\r\n  next() {\r\n    this.nextEvent.emit()\r\n  }\r\n\r\n  save() {\r\n    LoadingService.loading(true);\r\n\r\n    // 針對不同的 UI 類型進行不同的檢核\r\n    if (this.currentTypeUI === 3) {\r\n      // Case 3: 建材選樣 - 檢查 selectedBuildingSample\r\n      if (!this.selectedBuildingSample) {\r\n        this._toastService.showErrorMSG(\"請選擇一個選項\")\r\n        LoadingService.loading(false);\r\n        return;\r\n      }\r\n    } else {\r\n      // Case 1 & 2: 一般選項 - 檢查 selectedRegularChangeDetail\r\n      if (this.regularChangeDetail?.CRequireAnswer! == 1) {\r\n        if (this.selectedRegularChangeDetail == null) {\r\n          this._toastService.showErrorMSG(\"必填數量 \" + this.regularChangeDetail?.CRequireAnswer!)\r\n          LoadingService.loading(false);\r\n          return;\r\n        }\r\n      }\r\n      else {\r\n        if (this.regularChangeDetail?.CRequireAnswer! != this.selectedRegularChangeDetail?.length) {\r\n          this._toastService.showErrorMSG(\"必填數量 \" + this.regularChangeDetail?.CRequireAnswer!)\r\n          LoadingService.loading(false);\r\n          return;\r\n        }\r\n      }\r\n    }\r\n\r\n    let screenshot = document.getElementById(\"choice-selecting\")\r\n    html2canvas(screenshot!, {\r\n      allowTaint: true, useCORS: true,\r\n      ignoreElements: (element) => {\r\n        if (element.id === \"action\") {\r\n          return true;\r\n        }\r\n        return false;\r\n      }\r\n    }).then((canvas) => {\r\n      const base64image = canvas.toDataURL(\"image/png\");\r\n      let payload: SaveRegularChangeDetailRequest[] = []\r\n      if (this.currentTypeUI === 3) {\r\n        payload.push({\r\n          CRegularChangeDetailId: this.handleReturnID().CRegularChangeDetailId,\r\n          CRegularChangeItemlId: this.handleReturnID().CRegularChangeItemId,\r\n          CRemark: this.currentTypeUI === 3 ? this.selectedBuildingSample.CRemark : null,\r\n          CResultHtml: base64image,\r\n          RegularRemark: {\r\n            CRegularChangeItemRemarkTypeID: this.currentTypeUI === 3 ? this.selectedRemark.CRegularChangeItemRemarkTypeID! : null,\r\n          }\r\n        })\r\n      } else {\r\n        if (this.regularChangeDetail?.CRequireAnswer == 1) {\r\n          if (this.selectedRegularChangeDetail) {\r\n            payload.push({\r\n              CRegularChangeDetailId: (this.selectedRegularChangeDetail as RegularChangeDetail).CRegularChangeDetailId,\r\n              CRegularChangeItemlId: this.regularChangeDetail.CRegularChangeItemId!,\r\n              CRemark: null,\r\n              CResultHtml: base64image,\r\n              RegularRemark: {\r\n                CRegularChangeItemRemarkTypeID: null,\r\n              }\r\n            })\r\n          }\r\n        }\r\n        else {\r\n          this.selectedRegularChangeDetail.forEach(x => {\r\n            payload.push({\r\n              CRegularChangeDetailId: x.CRegularChangeDetailId,\r\n              CRegularChangeItemlId: this.regularChangeDetail.CRegularChangeItemId!,\r\n              CRemark: null,\r\n              CResultHtml: base64image,\r\n              RegularRemark: {\r\n                CRegularChangeItemRemarkTypeID: null,\r\n              }\r\n            })\r\n          })\r\n        }\r\n      }\r\n      this._regularChangeService.apiRegularChangeItemSaveRegularChangeDetailPost$Json({\r\n        body: payload\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            LoadingService.loading(false)\r\n            this.reset();\r\n            this.refreshListRegular.emit()\r\n          }\r\n        }),\r\n      ).subscribe();\r\n    });\r\n\r\n  }\r\n\r\n  reset() {\r\n    this.currentTypeUI = 0;\r\n    this.regularChangeDetail = {}\r\n    this.selectedRegularChangeDetail = []\r\n    this.selectedBuildingSample = {} as BuildingSample\r\n    this.buildingSampleSelection = {} as GetBuildingSampleSelectionRes\r\n    this.selectedRemark = {} as RegularRemark\r\n  }\r\n\r\n  handleReturnID() {\r\n    let CRegularChangeItemId = undefined;\r\n    let CRegularChangeDetailId = undefined;\r\n\r\n    if (this.currentTypeUI == 3) {\r\n      CRegularChangeItemId = this.buildingSampleSelection.CRegularChangeItemId!\r\n      CRegularChangeDetailId = this.selectedBuildingSample.CRegularChangeDetailId!\r\n    }\r\n    return { CRegularChangeDetailId, CRegularChangeItemId };\r\n  }\r\n\r\n  getRegularChangeById(regularChange: GetListRegularChangeItemRes) {\r\n    return this._regularChangeService.apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Json({\r\n      body: regularChange.CRegularChangeItemId!\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.regularChangeDetail = res.Entries!\r\n\r\n          if (this.currentTypeUI === 3) {\r\n            // Handle building sample case - convert regularChangeDetails to building samples format\r\n            this.buildingSampleSelection = {\r\n              CDesignFileUrl: res.Entries!.CDesignFileUrl,\r\n              CItemName: res.Entries!.CItemName,\r\n              CRegularChangeItemId: res.Entries!.CRegularChangeItemId,\r\n              // buildingSamples: res.Entries!.regularChangeDetails?.map(detail => ({\r\n              //   CDescription: detail.CDescription,\r\n              //   CInfoPictureFile: detail.CInfoPicture ? [detail.CInfoPicture] : null,\r\n              //   CIsSelect: detail.CIsSelect,\r\n              //   CPictureFile: detail.CPicture ? [detail.CPicture] : null,\r\n              //   CRegularChangeDetailId: detail.CRegularChangeDetailId,\r\n              //   CRemark: null,\r\n              //   CSelectName: detail.CSelectName,\r\n              //   RegularRemark: detail.RegularRemark,\r\n              //   CPrice: detail.CPrice,\r\n              // }))\r\n            }\r\n\r\n            // Add \"其他（自填）\" option to each building sample\r\n            this.buildingSampleSelection.buildingSamples?.forEach(x => {\r\n              x.RegularRemark?.push({\r\n                CIsSelect: null,\r\n                CRegularChangeItemRemarkTypeID: null,\r\n                CTypeName: \"其他（自填）\"\r\n              })\r\n            });\r\n\r\n            // 確保有選中的建材樣本，優先選擇已選擇的，否則選擇第一個\r\n            const preSelectedSample = this.buildingSampleSelection.buildingSamples!.find(x => x.CIsSelect);\r\n            this.selectedBuildingSample = preSelectedSample || this.buildingSampleSelection.buildingSamples![0];\r\n\r\n            // 確保 selectedBuildingSample 存在時才設置 selectedRemark\r\n            if (this.selectedBuildingSample) {\r\n              this.selectedRemark = this.selectedBuildingSample.RegularRemark?.find(x => x.CTypeName == this.selectedBuildingSample.CRemark)\r\n                || this.selectedBuildingSample.RegularRemark?.[2]\r\n                || this.selectedBuildingSample.RegularRemark?.[0]\r\n                || {} as RegularRemark;\r\n\r\n              this.selectedBuildingSample.CRemark = this.selectedBuildingSample.CRemark || this.selectedRemark.CTypeName || '';\r\n            }\r\n          } else {\r\n            // Handle regular change details case\r\n            let tempData = res.Entries?.regularChangeDetails?.filter(x => x.CIsSelect)\r\n            let tempSelected: any\r\n            tempSelected = regularChange?.CRequireAnswer == 1 ? tempData![0] : tempData\r\n            this.selectedRegularChangeDetail = tempSelected\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  changeBuilding(event: any) {\r\n    if (this.selectedRemark.CRegularChangeItemRemarkTypeID != null) {\r\n      this.selectedBuildingSample.CRemark = this.selectedRemark.CTypeName\r\n    }\r\n  }\r\n\r\n  changeRemark(event: any) {\r\n    if (event.value.CRegularChangeItemRemarkTypeID !== null) {\r\n      this.selectedBuildingSample.CRemark = event.value.CTypeName\r\n    } else {\r\n      this.selectedBuildingSample.CRemark = \"\"\r\n    }\r\n  }\r\n\r\n  checkAllSelect() {\r\n    // return !this.listRegularChangeItem.every(x => x.CIsSelect)\r\n    return false\r\n  }\r\n\r\n  validateTotalAnswer() {\r\n    if (this.regularChangeDetail?.CRequireAnswer) {\r\n      if (this.selectedRegularChangeDetail.length > this.regularChangeDetail?.CRequireAnswer) {\r\n        this.selectedRegularChangeDetail.pop();\r\n        this._toastService.showErrorMSG(\"必填數量 \" + this.regularChangeDetail?.CRequireAnswer!)\r\n        LoadingService.loading(false);\r\n      }\r\n    }\r\n  }\r\n\r\n  formatPrice(price: number | undefined): string {\r\n    if (!price) return '';\r\n    return price.toLocaleString('zh-TW');\r\n  }\r\n}\r\n\r\n", "<section class=\"flex flex-col justify-center items-center w-full pb-4\" id=\"choice-selecting\">\r\n  @switch (currentTypeUI) {\r\n  @case (0) {\r\n  <div class=\"flex flex-col text-center\" style=\"padding-bottom: 24px;\">\r\n    <span class=\"text-black text-xl font-bold\">\r\n      選擇項目\r\n    </span>\r\n    <span class=\"text-base mt-2\">\r\n      點擊項目可前往選擇\r\n    </span>\r\n  </div>\r\n  <div class=\"flex flex-col lg:!w-[800px] w-full m-auto pb-5 px-4\" *ngIf=\"listRegularChangeItem.length > 0\">\r\n    <div\r\n      class=\"lg:flex items-center justify-between cursor-pointer bg-[#F3F1EA99] rounded-lg mb-2 p-4 lg:!px-4 lg:!py-[18px]\"\r\n      *ngFor=\"let regularChangeItem of listRegularChangeItem\">\r\n      <div class=\"lg:flex items-center\">\r\n        <div class=\"bg-[#B8A676] px-4 py-1 h-[31px] w-[88px] mr-3 flex justify-center items-center rounded\">\r\n          <span class=\"text-base text-white\">\r\n            {{regularChangeItem.CTotalAnswer + '選' + regularChangeItem.CRequireAnswer}}\r\n          </span>\r\n        </div>\r\n        <span class=\"text-xl text-black block lg:text-center w-full text-left\">\r\n          {{regularChangeItem.CItemName}}\r\n        </span>\r\n      </div>\r\n      <button class=\"flex justify-center text-white\r\n          items-center font-medium rounded-3xl\r\n          bg-gray-500 p-2\r\n          w-full lg:!w-[180px] h-[47px] py-3 text-base\" [disabled]=\"isDisabledOptionStep2\" pButton\r\n        (click)=\"gotoPickItem(regularChangeItem)\" [ngStyle]=\"handleStyleOption(regularChangeItem.CIsSelect!)\">\r\n        {{hanldeTitleOption(regularChangeItem.CIsSelect!)}}\r\n      </button>\r\n    </div>\r\n  </div>\r\n  <div class=\"flex flex-col w-full m-auto mt-4\" *ngIf=\"listRegularChangeItem.length <= 0\">\r\n    <div class=\"no-data\">\r\n      表單尚未鎖定\r\n    </div>\r\n  </div>\r\n\r\n  <button class=\"text-base btn-next w-[180px] h-[47px] butn1 flex justify-center items-center my-4\" pButton\r\n    [disabled]=\"checkAllSelect()\" [ngClass]=\"{'btn-enable': !checkAllSelect(), 'btn-disable': checkAllSelect()}\"\r\n    (click)=\"next()\" *ngIf=\"listRegularChangeItem.length > 0\">\r\n    下一步\r\n  </button>\r\n  }\r\n  @case (1) {\r\n  <div class=\"flex flex-col text-center justify-center items-center\">\r\n    <span class=\"text-2xl text-black\" *ngIf=\"regularChangeDetail\">\r\n      {{regularChangeDetail.CItemName!}}\r\n    </span>\r\n    <span class=\"text-base mt-2\" *ngIf=\"regularChangeDetail\">\r\n      請選擇 {{regularChangeDetail.CRequireAnswer!}} 個選項，點擊可放大檢視圖片詳細.\r\n    </span>\r\n    <div *ngIf=\"regularChangeDetail?.CDesignFileUrl\"\r\n      class=\"bg-[#008FC7] bg-opacity-[0.04] px-3 py-2 mt-2 cursor-pointer w-[50%] rounded-md\"\r\n      (click)=\"enlargeimg(regularChangeDetail.CDesignFileUrl!)\">\r\n      <div class=\"flex items-center justify-center\">\r\n        <img class=\"mr-2\" src=\"/assets/designFile.svg\">\r\n        <span class=\"text-[#008FC7]\">\r\n          檢視設計概念\r\n        </span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"flex max-sm:block justify-center my-6\" *ngIf=\"!!regularChangeDetail\">\r\n    @for (regularChangeDetailData of regularChangeDetail.regularChangeDetails!; track $index) {\r\n    <div class=\"flex items-center justify-center flex-col mr-5\"\r\n      [ngStyle]=\"{'width': 100 / regularChangeDetail.regularChangeDetails!.length! + '%'}\">\r\n      <div class=\"box\">\r\n        <img class=\"fit-size\" [src]=\"regularChangeDetailData.CPicture?.CFile! | addBaseFile\"\r\n          (click)=\"enlargeimg(regularChangeDetailData.CInfoPicture?.CFile!, regularChangeDetailData.CSelectName)\">\r\n      </div>\r\n      <div class=\"radiobox max-sm:bottom-10 max-md:bottom-14 max-lg:bottom-12\">\r\n        @if (regularChangeDetail.CRequireAnswer!\r\n        <= 1) { <p-radioButton class=\"mr-2\" name=\"plan\"\r\n          [inputId]=\"regularChangeDetailData.CRegularChangeDetailId?.toString()\" [value]=\"regularChangeDetailData\"\r\n          [(ngModel)]=\"selectedRegularChangeDetail\" />\r\n        } @else {\r\n        <p-checkbox class=\"mr-2\" name=\"plan\" [inputId]=\"regularChangeDetailData.CRegularChangeDetailId?.toString()\"\r\n          [value]=\"regularChangeDetailData\" [(ngModel)]=\"selectedRegularChangeDetail\"\r\n          (onChange)=\"validateTotalAnswer()\" />\r\n        }\r\n        <label class=\"cursor-pointer text-xl text-center w-full\"\r\n          [for]=\"regularChangeDetailData.CRegularChangeDetailId?.toString()\">\r\n          {{regularChangeDetailData.CSelectName}}\r\n        </label>\r\n      </div>\r\n      <div class=\"flex justify-center text-base\">\r\n        <span>\r\n          {{regularChangeDetailData.CDescription}}\r\n        </span>\r\n      </div>\r\n      <div class=\"flex justify-center text-sm mt-1\" *ngIf=\"regularChangeDetailData.CPrice\">\r\n        <span class=\"text-[#B8A676] font-medium\">\r\n          NT$ {{formatPrice(regularChangeDetailData.CPrice)}}\r\n        </span>\r\n      </div>\r\n    </div>\r\n    }\r\n  </div>\r\n  <div class=\"flex items-center justify-center my-6\" id=\"action\">\r\n    <button class=\"button1 butn1 flex justify-center items-center mr-4 !w-40\" pButton (click)=\"reset()\">\r\n      返回列表\r\n    </button>\r\n    <button class=\"button2 butn1 flex justify-center items-center ml-4 !w-40\" pButton (click)=\"save()\">\r\n      儲存\r\n    </button>\r\n  </div>\r\n  }\r\n  @case (2) {\r\n  <div class=\"flex flex-col text-center justify-center items-center\">\r\n    <span class=\"text-2xl text-black\" *ngIf=\"regularChangeDetail\">\r\n      {{regularChangeDetail.CItemName!}}\r\n    </span>\r\n    <span class=\"text-base mt-2\" *ngIf=\"regularChangeDetail\">\r\n      請選擇 {{regularChangeDetail.CRequireAnswer!}} 個選項，點擊可放大檢視圖片詳細。\r\n    </span>\r\n    <div *ngIf=\"regularChangeDetail?.CDesignFileUrl\"\r\n      class=\"bg-[#008FC7] bg-opacity-[0.04] px-3 py-2 mt-2 cursor-pointer w-[50%] rounded-md\"\r\n      (click)=\"enlargeimg(regularChangeDetail.CDesignFileUrl!)\">\r\n      <div class=\"flex items-center justify-center\">\r\n        <img class=\"mr-2 fit-size\" src=\"/assets/designFile.svg\">\r\n        <span class=\"text-[#008FC7]\">\r\n          檢視設計概念\r\n        </span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"grid grid-cols-10 max-xl:grid-cols-6 max-lg:grid-cols-4 my-6 w-full\" *ngIf=\"!!regularChangeDetail\">\r\n    @for (regularChangeDetailData of regularChangeDetail.regularChangeDetails!; track $index) {\r\n    <div class=\"relative !w-[107px] m-auto mb-4\">\r\n      <div class=\"relative !w-[107px] !h-[107px]\">\r\n        <label [for]=\"regularChangeDetailData.CRegularChangeDetailId?.toString()\">\r\n          <img class=\"fit-img-size\" [src]=\"regularChangeDetailData.CPicture?.CFile! | addBaseFile\"\r\n            (click)=\"enlargeimg(regularChangeDetailData.CInfoPicture?.CFile!,regularChangeDetailData.CSelectName)\">\r\n          @if (regularChangeDetail.CRequireAnswer!\r\n          <= 1) { <p-radioButton class=\"absolute left-3 top-3\" name=\"itemColor\"\r\n            [inputId]=\"regularChangeDetailData.CRegularChangeDetailId?.toString()\" [value]=\"regularChangeDetailData\"\r\n            [(ngModel)]=\"selectedRegularChangeDetail\" />\r\n          }@else {\r\n          <p-checkbox class=\"absolute left-0 top-1\" name=\"itemColor\"\r\n            [inputId]=\"regularChangeDetailData.CRegularChangeDetailId?.toString()\" [value]=\"regularChangeDetailData\"\r\n            [(ngModel)]=\"selectedRegularChangeDetail\" (onChange)=\"validateTotalAnswer()\" />\r\n          }\r\n        </label>\r\n      </div>\r\n      <div class=\"text-center mt-1\" *ngIf=\"regularChangeDetailData.CPrice\">\r\n        <span class=\"text-[#B8A676] font-medium text-xs\">\r\n          NT$ {{formatPrice(regularChangeDetailData.CPrice)}}\r\n        </span>\r\n      </div>\r\n    </div>\r\n    }\r\n  </div>\r\n  <div class=\"flex items-center justify-center my-6 max-lg:w-full\" id=\"action\">\r\n    <button class=\"button1 butn1 flex justify-center items-center m-2\" pButton (click)=\"reset()\">\r\n      返回列表\r\n    </button>\r\n    <button class=\"button2 butn1 flex justify-center items-center m-2\" pButton (click)=\"save()\">\r\n      儲存\r\n    </button>\r\n  </div>\r\n  }\r\n  @case (3){\r\n  <div class=\"flex flex-col text-center justify-center items-center\">\r\n    <span class=\"text-2xl text-black\" *ngIf=\"buildingSampleSelection\">\r\n      {{buildingSampleSelection.CItemName!}}\r\n    </span>\r\n    <span class=\"text-base mt-2\">\r\n      請選擇 1 個選項，點擊可放大檢視圖片詳細。\r\n    </span>\r\n    <div *ngIf=\"regularChangeDetail?.CDesignFileUrl\"\r\n      class=\"bg-[#008FC7] bg-opacity-[0.04] px-3 py-2 mt-2 cursor-pointer w-[50%] rounded-md\"\r\n      (click)=\"enlargeimg(buildingSampleSelection.CDesignFileUrl!)\">\r\n      <div class=\"flex items-center justify-center\">\r\n        <img class=\"mr-2\" src=\"/assets/designFile.svg\">\r\n        <span class=\"text-[#008FC7]\">\r\n          檢視設計概念\r\n        </span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div *ngIf=\"!!buildingSampleSelection && !!selectedBuildingSample\"\r\n    class=\"flex flex-row max-md:flex-col-reverse my-6 flex-wrap w-full justify-between\">\r\n    <div class=\"w-[760px]\">\r\n      <div class=\"flex flex-row justify-between\">\r\n        <div class=\"w-[660px] h-[145px]\">\r\n          <p-dropdown class=\"drop100 h-[47px]\" [options]=\"buildingSampleSelection.buildingSamples!\"\r\n            [(ngModel)]=\"selectedBuildingSample\" appendTo=\"body\" optionLabel=\"CSelectName\"\r\n            (ngModelChange)=\"changeBuilding($event)\" />\r\n          <div class=\"text-base mt-3\">備註</div>\r\n          <div class=\"flex\">\r\n            <p-dropdown class=\"drop50 mr-4 h-[47px]\" [options]=\"selectedBuildingSample.RegularRemark!\"\r\n              [(ngModel)]=\"selectedRemark\" appendTo=\"body\" optionLabel=\"CTypeName\" (onChange)=\"changeRemark($event)\" />\r\n            <input pInputText class=\"input input-inline ml-2 fs-12 !mt-0\" [(ngModel)]=\"selectedBuildingSample.CRemark\"\r\n              [disabled]=\"selectedRemark != null ? (selectedRemark.CRegularChangeItemRemarkTypeID != null ? true : false) : false\"\r\n              placeholder=\"在此輸入您的文字\">\r\n          </div>\r\n        </div>\r\n        <div class=\"h-[80px] w-[80px]\">\r\n          <img class=\"object-cover m-auto w-full h-full rounded\"\r\n            [src]=\"(selectedBuildingSample.CInfoPictureFile?.length && selectedBuildingSample.CInfoPictureFile![0].CFile) ? (selectedBuildingSample.CInfoPictureFile![0].CFile | addBaseFile) : ''\"\r\n            (click)=\"enlargeimg(\r\n              selectedBuildingSample.CInfoPictureFile![0].CFile ? (selectedBuildingSample.CInfoPictureFile![0].CFile) :'', selectedBuildingSample.CSelectName)\">\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"w-[400px]\">\r\n      <div class=\"flex flex-col max-md:my-4\">\r\n        <div class=\"overflow-hidden\">\r\n          <img class=\"object-cover w-[400px] h-[280px] rounded-md\"\r\n            (click)=\"enlargeimg((selectedBuildingSample.CPictureFile?.length && selectedBuildingSample.CPictureFile![0].CFile ) ? selectedBuildingSample.CPictureFile![0].CFile : '', selectedBuildingSample.CSelectName)\"\r\n            [src]=\"(selectedBuildingSample.CPictureFile?.length && selectedBuildingSample.CPictureFile![0].CFile ) ? (selectedBuildingSample.CPictureFile![0].CFile | addBaseFile):''\">\r\n        </div>\r\n        <div class=\"text-left mt-3\">\r\n          <div class=\"text text-base\">{{(selectedBuildingSample.CDescription)\r\n            ? selectedBuildingSample.CDescription\r\n            : ''\r\n            }}</div>\r\n          <div class=\"text-left mt-2\" *ngIf=\"selectedBuildingSample.CPrice\">\r\n            <span class=\"text-[#B8A676] font-medium text-base\">\r\n              NT$ {{formatPrice(selectedBuildingSample.CPrice)}}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"flex items-center justify-center my-6 max-lg:w-full\" id=\"action\">\r\n    <button class=\"button1 butn1 flex justify-center items-center m-2\" pButton (click)=\"reset()\">\r\n      返回列表\r\n    </button>\r\n    <button class=\"button2 butn1 flex justify-center items-center m-2\" pButton (click)=\"save()\">\r\n      儲存\r\n    </button>\r\n  </div>\r\n  }\r\n  }\r\n</section>\r\n\r\n<ng-container>\r\n  <p-dialog [(visible)]=\"visibleimg\" [draggable]=\"false\" [closable]=\"false\" [resizable]=\"false\"\r\n    [style]=\"{'width': '1000px','min-height':'auto'}\" [breakpoints]=\"{ '600px': '100vw','auto': '90vw' }\">\r\n    <ng-template pTemplate=\"header\">\r\n      <span class=\"title\">\r\n        <span> {{ conentenlargeimg }}</span>\r\n      </span>\r\n    </ng-template>\r\n    <div class=\"w-full flex items-center justify-center\">\r\n      <div class=\"w-[80%] h-auto flex items-center justify-center\">\r\n        <img class=\"fit-size\" [src]=\"choiceenlargeimg! | addBaseFile\">\r\n      </div>\r\n    </div>\r\n    <ng-template pTemplate=\"footer\">\r\n      <button class=\"button2 m-0\" (click)=\"closeimg()\">關閉</button>\r\n    </ng-template>\r\n    <div>\r\n    </div>\r\n  </p-dialog>\r\n</ng-container>"], "mappings": "AAAA,SAASA,YAAY,QAAe,iBAAiB;AACrD,SAAoBC,YAAY,QAA+B,eAAe;AAC9E,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,qBAAqB;AASvD,SAASC,YAAY,QAAQ,yCAAyC;AACtE,SAASC,cAAc,QAAQ,6CAA6C;AAC5E,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAAyCC,GAAG,QAAQ,MAAM;AAC1D,OAAOC,WAAW,MAAM,aAAa;AAErC,SAASC,cAAc,QAAQ,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICHvCC,EALN,CAAAC,cAAA,cAE0D,cACtB,cACoE,eAC/D;IACjCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IACNH,EAAA,CAAAC,cAAA,eAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IACNH,EAAA,CAAAC,cAAA,iBAIwG;IAAtGD,EAAA,CAAAI,UAAA,mBAAAC,0EAAA;MAAA,MAAAC,oBAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAP,oBAAA,CAA+B;IAAA,EAAC;IACzCN,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAdEH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAe,kBAAA,MAAAT,oBAAA,CAAAU,YAAA,cAAAV,oBAAA,CAAAW,cAAA,MACF;IAGAjB,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAe,kBAAA,MAAAT,oBAAA,CAAAY,SAAA,MACF;IAKgDlB,EAAA,CAAAc,SAAA,EAAkC;IACxCd,EADM,CAAAmB,UAAA,aAAAT,MAAA,CAAAU,qBAAA,CAAkC,YAAAV,MAAA,CAAAW,iBAAA,CAAAf,oBAAA,CAAAgB,SAAA,EACmB;IACrGtB,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,MAAAL,MAAA,CAAAa,iBAAA,CAAAjB,oBAAA,CAAAgB,SAAA,OACF;;;;;IApBJtB,EAAA,CAAAC,cAAA,cAA0G;IACxGD,EAAA,CAAAwB,UAAA,IAAAC,iDAAA,kBAE0D;IAmB5DzB,EAAA,CAAAG,YAAA,EAAM;;;;IAnB4BH,EAAA,CAAAc,SAAA,EAAwB;IAAxBd,EAAA,CAAAmB,UAAA,YAAAT,MAAA,CAAAgB,qBAAA,CAAwB;;;;;IAqBxD1B,EADF,CAAAC,cAAA,cAAwF,cACjE;IACnBD,EAAA,CAAAE,MAAA,6CACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;IAENH,EAAA,CAAAC,cAAA,iBAE4D;IAA1DD,EAAA,CAAAI,UAAA,mBAAAuB,uEAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAmB,IAAA,EAAM;IAAA,EAAC;IAChB7B,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHuBH,EAA9B,CAAAmB,UAAA,aAAAT,MAAA,CAAAoB,cAAA,GAA6B,YAAA9B,EAAA,CAAA+B,eAAA,IAAAC,GAAA,GAAAtB,MAAA,CAAAoB,cAAA,IAAApB,MAAA,CAAAoB,cAAA,IAA+E;;;;;IArC5G9B,EADF,CAAAC,cAAA,aAAqE,cACxB;IACzCD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAE,MAAA,+DACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IA8BNH,EA7BA,CAAAwB,UAAA,IAAAS,2CAAA,kBAA0G,IAAAC,2CAAA,kBAuBlB,IAAAC,8CAAA,qBAQ5B;;;;IA/BMnC,EAAA,CAAAc,SAAA,GAAsC;IAAtCd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAAgB,qBAAA,CAAAU,MAAA,KAAsC;IAuBzDpC,EAAA,CAAAc,SAAA,EAAuC;IAAvCd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAAgB,qBAAA,CAAAU,MAAA,MAAuC;IAQlEpC,EAAA,CAAAc,SAAA,EAAsC;IAAtCd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAAgB,qBAAA,CAAAU,MAAA,KAAsC;;;;;IAMxDpC,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,MAAAL,MAAA,CAAA2B,mBAAA,CAAAnB,SAAA,MACF;;;;;IACAlB,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,yBAAAL,MAAA,CAAA2B,mBAAA,CAAApB,cAAA,kGACF;;;;;;IACAjB,EAAA,CAAAC,cAAA,cAE4D;IAA1DD,EAAA,CAAAI,UAAA,mBAAAkC,iEAAA;MAAAtC,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA7B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8B,UAAA,CAAA9B,MAAA,CAAA2B,mBAAA,CAAAI,cAAA,CAA+C;IAAA,EAAC;IACzDzC,EAAA,CAAAC,cAAA,cAA8C;IAC5CD,EAAA,CAAA0C,SAAA,cAA+C;IAC/C1C,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,MAAA,6CACF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACF;;;;;;IAYMH,EAAA,CAAAC,cAAA,wBAEsC;IAA5CD,EAAA,CAAA2C,gBAAA,2BAAAC,uGAAAC,MAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAAuC,GAAA;MAAA,MAAApC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA+C,kBAAA,CAAArC,MAAA,CAAAsC,2BAAA,EAAAH,MAAA,MAAAnC,MAAA,CAAAsC,2BAAA,GAAAH,MAAA;MAAA,OAAA7C,EAAA,CAAAY,WAAA,CAAAiC,MAAA;IAAA,EAAyC;IAFnC7C,EAAA,CAAAG,YAAA,EAEsC;;;;;IAD2BH,EAAvE,CAAAmB,UAAA,YAAA8B,0BAAA,CAAAC,sBAAA,kBAAAD,0BAAA,CAAAC,sBAAA,CAAAC,QAAA,GAAsE,UAAAF,0BAAA,CAAkC;IACxGjD,EAAA,CAAAoD,gBAAA,YAAA1C,MAAA,CAAAsC,2BAAA,CAAyC;;;;;;IAE3ChD,EAAA,CAAAC,cAAA,qBAEuC;IADHD,EAAA,CAAA2C,gBAAA,2BAAAU,oGAAAR,MAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAA+C,IAAA;MAAA,MAAA5C,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA+C,kBAAA,CAAArC,MAAA,CAAAsC,2BAAA,EAAAH,MAAA,MAAAnC,MAAA,CAAAsC,2BAAA,GAAAH,MAAA;MAAA,OAAA7C,EAAA,CAAAY,WAAA,CAAAiC,MAAA;IAAA,EAAyC;IAC3E7C,EAAA,CAAAI,UAAA,sBAAAmD,+FAAA;MAAAvD,EAAA,CAAAO,aAAA,CAAA+C,IAAA;MAAA,MAAA5C,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAYF,MAAA,CAAA8C,mBAAA,EAAqB;IAAA,EAAC;IAFpCxD,EAAA,CAAAG,YAAA,EAEuC;;;;;IADrCH,EADmC,CAAAmB,UAAA,YAAA8B,0BAAA,CAAAC,sBAAA,kBAAAD,0BAAA,CAAAC,sBAAA,CAAAC,QAAA,GAAsE,UAAAF,0BAAA,CACxE;IAACjD,EAAA,CAAAoD,gBAAA,YAAA1C,MAAA,CAAAsC,2BAAA,CAAyC;;;;;IAc7EhD,EADF,CAAAC,cAAA,cAAqF,eAC1C;IACvCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;;IAFFH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAe,kBAAA,UAAAL,MAAA,CAAA+C,WAAA,CAAAR,0BAAA,CAAAS,MAAA,OACF;;;;;;IA1BA1D,EAHJ,CAAAC,cAAA,cACuF,cACpE,cAE2F;;IAAxGD,EAAA,CAAAI,UAAA,mBAAAuD,uEAAA;MAAA,MAAAV,0BAAA,GAAAjD,EAAA,CAAAO,aAAA,CAAAqD,GAAA,EAAAnD,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8B,UAAA,CAAAS,0BAAA,CAAAY,YAAA,kBAAAZ,0BAAA,CAAAY,YAAA,CAAAC,KAAA,EAAAb,0BAAA,CAAAc,WAAA,CAA6F;IAAA,EAAC;IAC3G/D,EAFE,CAAAG,YAAA,EAC0G,EACtG;IACNH,EAAA,CAAAC,cAAA,cAAyE;IAKrED,EAJF,CAAAwB,UAAA,IAAAwC,+DAAA,4BACO,IAAAC,+DAAA,OAGE;IAKTjE,EAAA,CAAAC,cAAA,gBACqE;IACnED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;IAEJH,EADF,CAAAC,cAAA,cAA2C,YACnC;IACJD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IACNH,EAAA,CAAAwB,UAAA,KAAA0C,wDAAA,kBAAqF;IAKvFlE,EAAA,CAAAG,YAAA,EAAM;;;;;IA9BJH,EAAA,CAAAmB,UAAA,YAAAnB,EAAA,CAAAmE,eAAA,IAAAC,GAAA,QAAA1D,MAAA,CAAA2B,mBAAA,CAAAgC,oBAAA,CAAAjC,MAAA,QAAoF;IAE5DpC,EAAA,CAAAc,SAAA,GAA8D;IAA9Dd,EAAA,CAAAmB,UAAA,QAAAnB,EAAA,CAAAsE,WAAA,OAAArB,0BAAA,CAAAsB,QAAA,kBAAAtB,0BAAA,CAAAsB,QAAA,CAAAT,KAAA,GAAA9D,EAAA,CAAAwE,aAAA,CAA8D;IAIpFxE,EAAA,CAAAc,SAAA,GAQC;IARDd,EAAA,CAAAyE,aAAA,IAAA/D,MAAA,CAAA2B,mBAAA,CAAApB,cAAA,cAQC;IAECjB,EAAA,CAAAc,SAAA,GAAkE;IAAlEd,EAAA,CAAAmB,UAAA,QAAA8B,0BAAA,CAAAC,sBAAA,kBAAAD,0BAAA,CAAAC,sBAAA,CAAAC,QAAA,GAAkE;IAClEnD,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,MAAAkC,0BAAA,CAAAc,WAAA,MACF;IAIE/D,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAe,kBAAA,MAAAkC,0BAAA,CAAAyB,YAAA,MACF;IAE6C1E,EAAA,CAAAc,SAAA,EAAoC;IAApCd,EAAA,CAAAmB,UAAA,SAAA8B,0BAAA,CAAAS,MAAA,CAAoC;;;;;IA5BvF1D,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAA2E,gBAAA,IAAAC,iDAAA,qBAAA5E,EAAA,CAAA6E,sBAAA,CAiCC;IACH7E,EAAA,CAAAG,YAAA,EAAM;;;;IAlCJH,EAAA,CAAAc,SAAA,EAiCC;IAjCDd,EAAA,CAAA8E,UAAA,CAAApE,MAAA,CAAA2B,mBAAA,CAAAgC,oBAAA,CAiCC;;;;;;IApDHrE,EAAA,CAAAC,cAAA,cAAmE;IAOjED,EANA,CAAAwB,UAAA,IAAAuD,4CAAA,mBAA8D,IAAAC,4CAAA,mBAGL,IAAAC,2CAAA,kBAKG;IAQ9DjF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAwB,UAAA,IAAA0D,2CAAA,kBAAiF;IAqC/ElF,EADF,CAAAC,cAAA,cAA+D,iBACuC;IAAlBD,EAAA,CAAAI,UAAA,mBAAA+E,8DAAA;MAAAnF,EAAA,CAAAO,aAAA,CAAA6E,GAAA;MAAA,MAAA1E,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2E,KAAA,EAAO;IAAA,EAAC;IACjGrF,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAmG;IAAjBD,EAAA,CAAAI,UAAA,mBAAAkF,8DAAA;MAAAtF,EAAA,CAAAO,aAAA,CAAA6E,GAAA;MAAA,MAAA1E,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA6E,IAAA,EAAM;IAAA,EAAC;IAChGvF,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IA5D+BH,EAAA,CAAAc,SAAA,EAAyB;IAAzBd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAA2B,mBAAA,CAAyB;IAG9BrC,EAAA,CAAAc,SAAA,EAAyB;IAAzBd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAA2B,mBAAA,CAAyB;IAGjDrC,EAAA,CAAAc,SAAA,EAAyC;IAAzCd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAA2B,mBAAA,kBAAA3B,MAAA,CAAA2B,mBAAA,CAAAI,cAAA,CAAyC;IAWGzC,EAAA,CAAAc,SAAA,EAA2B;IAA3Bd,EAAA,CAAAmB,UAAA,WAAAT,MAAA,CAAA2B,mBAAA,CAA2B;;;;;IA+C7ErC,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,MAAAL,MAAA,CAAA2B,mBAAA,CAAAnB,SAAA,MACF;;;;;IACAlB,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,yBAAAL,MAAA,CAAA2B,mBAAA,CAAApB,cAAA,uGACF;;;;;;IACAjB,EAAA,CAAAC,cAAA,cAE4D;IAA1DD,EAAA,CAAAI,UAAA,mBAAAoF,iEAAA;MAAAxF,EAAA,CAAAO,aAAA,CAAAkF,IAAA;MAAA,MAAA/E,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8B,UAAA,CAAA9B,MAAA,CAAA2B,mBAAA,CAAAI,cAAA,CAA+C;IAAA,EAAC;IACzDzC,EAAA,CAAAC,cAAA,cAA8C;IAC5CD,EAAA,CAAA0C,SAAA,cAAwD;IACxD1C,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,MAAA,6CACF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACF;;;;;;IAUQH,EAAA,CAAAC,cAAA,wBAEsC;IAA5CD,EAAA,CAAA2C,gBAAA,2BAAA+C,uGAAA7C,MAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAAoF,IAAA;MAAA,MAAAjF,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA+C,kBAAA,CAAArC,MAAA,CAAAsC,2BAAA,EAAAH,MAAA,MAAAnC,MAAA,CAAAsC,2BAAA,GAAAH,MAAA;MAAA,OAAA7C,EAAA,CAAAY,WAAA,CAAAiC,MAAA;IAAA,EAAyC;IAFnC7C,EAAA,CAAAG,YAAA,EAEsC;;;;;IAD2BH,EAAvE,CAAAmB,UAAA,YAAAyE,2BAAA,CAAA1C,sBAAA,kBAAA0C,2BAAA,CAAA1C,sBAAA,CAAAC,QAAA,GAAsE,UAAAyC,2BAAA,CAAkC;IACxG5F,EAAA,CAAAoD,gBAAA,YAAA1C,MAAA,CAAAsC,2BAAA,CAAyC;;;;;;IAE3ChD,EAAA,CAAAC,cAAA,qBAEiF;IAA/ED,EAAA,CAAA2C,gBAAA,2BAAAkD,oGAAAhD,MAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAAuF,IAAA;MAAA,MAAApF,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA+C,kBAAA,CAAArC,MAAA,CAAAsC,2BAAA,EAAAH,MAAA,MAAAnC,MAAA,CAAAsC,2BAAA,GAAAH,MAAA;MAAA,OAAA7C,EAAA,CAAAY,WAAA,CAAAiC,MAAA;IAAA,EAAyC;IAAC7C,EAAA,CAAAI,UAAA,sBAAA2F,+FAAA;MAAA/F,EAAA,CAAAO,aAAA,CAAAuF,IAAA;MAAA,MAAApF,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAYF,MAAA,CAAA8C,mBAAA,EAAqB;IAAA,EAAC;IAF9ExD,EAAA,CAAAG,YAAA,EAEiF;;;;;IADRH,EAAvE,CAAAmB,UAAA,YAAAyE,2BAAA,CAAA1C,sBAAA,kBAAA0C,2BAAA,CAAA1C,sBAAA,CAAAC,QAAA,GAAsE,UAAAyC,2BAAA,CAAkC;IACxG5F,EAAA,CAAAoD,gBAAA,YAAA1C,MAAA,CAAAsC,2BAAA,CAAyC;;;;;IAK7ChD,EADF,CAAAC,cAAA,cAAqE,eAClB;IAC/CD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;;IAFFH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAe,kBAAA,UAAAL,MAAA,CAAA+C,WAAA,CAAAmC,2BAAA,CAAAlC,MAAA,OACF;;;;;;IAhBE1D,EAHN,CAAAC,cAAA,cAA6C,cACC,gBACgC,cAEiC;;IAAvGD,EAAA,CAAAI,UAAA,mBAAA4F,uEAAA;MAAA,MAAAJ,2BAAA,GAAA5F,EAAA,CAAAO,aAAA,CAAA0F,IAAA,EAAAxF,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8B,UAAA,CAAAoD,2BAAA,CAAA/B,YAAA,kBAAA+B,2BAAA,CAAA/B,YAAA,CAAAC,KAAA,EAAA8B,2BAAA,CAAA7B,WAAA,CAA4F;IAAA,EAAC;IADxG/D,EAAA,CAAAG,YAAA,EACyG;IAKxGH,EAJD,CAAAwB,UAAA,IAAA0E,+DAAA,4BACO,IAAAC,+DAAA,OAGC;IAMZnG,EADE,CAAAG,YAAA,EAAQ,EACJ;IACNH,EAAA,CAAAwB,UAAA,IAAA4E,uDAAA,kBAAqE;IAKvEpG,EAAA,CAAAG,YAAA,EAAM;;;;;IAnBKH,EAAA,CAAAc,SAAA,GAAkE;IAAlEd,EAAA,CAAAmB,UAAA,QAAAyE,2BAAA,CAAA1C,sBAAA,kBAAA0C,2BAAA,CAAA1C,sBAAA,CAAAC,QAAA,GAAkE;IAC7CnD,EAAA,CAAAc,SAAA,EAA8D;IAA9Dd,EAAA,CAAAmB,UAAA,QAAAnB,EAAA,CAAAsE,WAAA,OAAAsB,2BAAA,CAAArB,QAAA,kBAAAqB,2BAAA,CAAArB,QAAA,CAAAT,KAAA,GAAA9D,EAAA,CAAAwE,aAAA,CAA8D;IAExFxE,EAAA,CAAAc,SAAA,GAQC;IARDd,EAAA,CAAAyE,aAAA,IAAA/D,MAAA,CAAA2B,mBAAA,CAAApB,cAAA,cAQC;IAG0BjB,EAAA,CAAAc,SAAA,GAAoC;IAApCd,EAAA,CAAAmB,UAAA,SAAAyE,2BAAA,CAAAlC,MAAA,CAAoC;;;;;IAlBvE1D,EAAA,CAAAC,cAAA,cAA+G;IAC7GD,EAAA,CAAA2E,gBAAA,IAAA0B,iDAAA,mBAAArG,EAAA,CAAA6E,sBAAA,CAuBC;IACH7E,EAAA,CAAAG,YAAA,EAAM;;;;IAxBJH,EAAA,CAAAc,SAAA,EAuBC;IAvBDd,EAAA,CAAA8E,UAAA,CAAApE,MAAA,CAAA2B,mBAAA,CAAAgC,oBAAA,CAuBC;;;;;;IA1CHrE,EAAA,CAAAC,cAAA,cAAmE;IAOjED,EANA,CAAAwB,UAAA,IAAA8E,4CAAA,mBAA8D,IAAAC,4CAAA,mBAGL,IAAAC,2CAAA,kBAKG;IAQ9DxG,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAwB,UAAA,IAAAiF,2CAAA,kBAA+G;IA2B7GzG,EADF,CAAAC,cAAA,cAA6E,iBACkB;IAAlBD,EAAA,CAAAI,UAAA,mBAAAsG,8DAAA;MAAA1G,EAAA,CAAAO,aAAA,CAAAoG,IAAA;MAAA,MAAAjG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2E,KAAA,EAAO;IAAA,EAAC;IAC1FrF,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA4F;IAAjBD,EAAA,CAAAI,UAAA,mBAAAwG,8DAAA;MAAA5G,EAAA,CAAAO,aAAA,CAAAoG,IAAA;MAAA,MAAAjG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA6E,IAAA,EAAM;IAAA,EAAC;IACzFvF,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAlD+BH,EAAA,CAAAc,SAAA,EAAyB;IAAzBd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAA2B,mBAAA,CAAyB;IAG9BrC,EAAA,CAAAc,SAAA,EAAyB;IAAzBd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAA2B,mBAAA,CAAyB;IAGjDrC,EAAA,CAAAc,SAAA,EAAyC;IAAzCd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAA2B,mBAAA,kBAAA3B,MAAA,CAAA2B,mBAAA,CAAAI,cAAA,CAAyC;IAWiCzC,EAAA,CAAAc,SAAA,EAA2B;IAA3Bd,EAAA,CAAAmB,UAAA,WAAAT,MAAA,CAAA2B,mBAAA,CAA2B;;;;;IAqC3GrC,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,MAAAL,MAAA,CAAAmG,uBAAA,CAAA3F,SAAA,MACF;;;;;;IAIAlB,EAAA,CAAAC,cAAA,cAEgE;IAA9DD,EAAA,CAAAI,UAAA,mBAAA0G,iEAAA;MAAA9G,EAAA,CAAAO,aAAA,CAAAwG,IAAA;MAAA,MAAArG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8B,UAAA,CAAA9B,MAAA,CAAAmG,uBAAA,CAAApE,cAAA,CAAmD;IAAA,EAAC;IAC7DzC,EAAA,CAAAC,cAAA,cAA8C;IAC5CD,EAAA,CAAA0C,SAAA,cAA+C;IAC/C1C,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,MAAA,6CACF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACF;;;;;IAwCEH,EADF,CAAAC,cAAA,cAAkE,eACb;IACjDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAe,kBAAA,UAAAL,MAAA,CAAA+C,WAAA,CAAA/C,MAAA,CAAAsG,sBAAA,CAAAtD,MAAA,OACF;;;;;;IAnCF1D,EALR,CAAAC,cAAA,cACsF,cAC7D,cACsB,cACR,qBAGc;IAD3CD,EAAA,CAAA2C,gBAAA,2BAAAsE,gFAAApE,MAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAA2G,IAAA;MAAA,MAAAxG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA+C,kBAAA,CAAArC,MAAA,CAAAsG,sBAAA,EAAAnE,MAAA,MAAAnC,MAAA,CAAAsG,sBAAA,GAAAnE,MAAA;MAAA,OAAA7C,EAAA,CAAAY,WAAA,CAAAiC,MAAA;IAAA,EAAoC;IACpC7C,EAAA,CAAAI,UAAA,2BAAA6G,gFAAApE,MAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAA2G,IAAA;MAAA,MAAAxG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAiBF,MAAA,CAAAyG,cAAA,CAAAtE,MAAA,CAAsB;IAAA,EAAC;IAF1C7C,EAAA,CAAAG,YAAA,EAE6C;IAC7CH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAElCH,EADF,CAAAC,cAAA,cAAkB,qBAE2F;IAAzGD,EAAA,CAAA2C,gBAAA,2BAAAyE,gFAAAvE,MAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAA2G,IAAA;MAAA,MAAAxG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA+C,kBAAA,CAAArC,MAAA,CAAA2G,cAAA,EAAAxE,MAAA,MAAAnC,MAAA,CAAA2G,cAAA,GAAAxE,MAAA;MAAA,OAAA7C,EAAA,CAAAY,WAAA,CAAAiC,MAAA;IAAA,EAA4B;IAAyC7C,EAAA,CAAAI,UAAA,sBAAAkH,2EAAAzE,MAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAA2G,IAAA;MAAA,MAAAxG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAYF,MAAA,CAAA6G,YAAA,CAAA1E,MAAA,CAAoB;IAAA,EAAC;IADxG7C,EAAA,CAAAG,YAAA,EAC2G;IAC3GH,EAAA,CAAAC,cAAA,gBAEyB;IAFqCD,EAAA,CAAA2C,gBAAA,2BAAA6E,2EAAA3E,MAAA;MAAA7C,EAAA,CAAAO,aAAA,CAAA2G,IAAA;MAAA,MAAAxG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA+C,kBAAA,CAAArC,MAAA,CAAAsG,sBAAA,CAAAS,OAAA,EAAA5E,MAAA,MAAAnC,MAAA,CAAAsG,sBAAA,CAAAS,OAAA,GAAA5E,MAAA;MAAA,OAAA7C,EAAA,CAAAY,WAAA,CAAAiC,MAAA;IAAA,EAA4C;IAI9G7C,EAJI,CAAAG,YAAA,EAEyB,EACrB,EACF;IAEJH,EADF,CAAAC,cAAA,eAA+B,eAIyH;;IADpJD,EAAA,CAAAI,UAAA,mBAAAsH,kEAAA;MAAA1H,EAAA,CAAAO,aAAA,CAAA2G,IAAA;MAAA,MAAAxG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8B,UAAA,CAAA9B,MAAA,CAAAsG,sBAAA,CAAAW,gBAAA,CACiC,CAAC,EAAA7D,KAAA,GAAApD,MAAA,CAAAsG,sBAAA,CAAAW,gBAAA,CAAoD,CAAC,EAAA7D,KAAA,GAAU,EAAE,EAAApD,MAAA,CAAAsG,sBAAA,CAAAjD,WAAA,CAAqC;IAAA,EAAE;IAG3J/D,EANM,CAAAG,YAAA,EAGsJ,EAClJ,EACF,EACF;IAIAH,EAHN,CAAAC,cAAA,eAAuB,eACkB,eACR,eAGkJ;;IAD3KD,EAAA,CAAAI,UAAA,mBAAAwH,kEAAA;MAAA5H,EAAA,CAAAO,aAAA,CAAA2G,IAAA;MAAA,MAAAxG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8B,UAAA,EAAA9B,MAAA,CAAAsG,sBAAA,CAAAa,YAAA,kBAAAnH,MAAA,CAAAsG,sBAAA,CAAAa,YAAA,CAAAzF,MAAA,KAAA1B,MAAA,CAAAsG,sBAAA,CAAAa,YAAA,CAAgG,CAAC,EAAA/D,KAAA,GAAApD,MAAA,CAAAsG,sBAAA,CAAAa,YAAA,CAAiD,CAAC,EAAA/D,KAAA,GAAU,EAAE,EAAApD,MAAA,CAAAsG,sBAAA,CAAAjD,WAAA,CAAqC;IAAA,EAAC;IAElN/D,EAHE,CAAAG,YAAA,EAE6K,EACzK;IAEJH,EADF,CAAAC,cAAA,eAA4B,eACE;IAAAD,EAAA,CAAAE,MAAA,IAGxB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACVH,EAAA,CAAAwB,UAAA,KAAAsG,kDAAA,kBAAkE;IAQ1E9H,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IAxCuCH,EAAA,CAAAc,SAAA,GAAoD;IAApDd,EAAA,CAAAmB,UAAA,YAAAT,MAAA,CAAAmG,uBAAA,CAAAkB,eAAA,CAAoD;IACvF/H,EAAA,CAAAoD,gBAAA,YAAA1C,MAAA,CAAAsG,sBAAA,CAAoC;IAIKhH,EAAA,CAAAc,SAAA,GAAiD;IAAjDd,EAAA,CAAAmB,UAAA,YAAAT,MAAA,CAAAsG,sBAAA,CAAAgB,aAAA,CAAiD;IACxFhI,EAAA,CAAAoD,gBAAA,YAAA1C,MAAA,CAAA2G,cAAA,CAA4B;IACgCrH,EAAA,CAAAc,SAAA,EAA4C;IAA5Cd,EAAA,CAAAoD,gBAAA,YAAA1C,MAAA,CAAAsG,sBAAA,CAAAS,OAAA,CAA4C;IACxGzH,EAAA,CAAAmB,UAAA,aAAAT,MAAA,CAAA2G,cAAA,WAAA3G,MAAA,CAAA2G,cAAA,CAAAY,8BAAA,gCAAoH;IAMtHjI,EAAA,CAAAc,SAAA,GAAuL;IAAvLd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAAsG,sBAAA,CAAAW,gBAAA,kBAAAjH,MAAA,CAAAsG,sBAAA,CAAAW,gBAAA,CAAAvF,MAAA,KAAA1B,MAAA,CAAAsG,sBAAA,CAAAW,gBAAA,IAAA7D,KAAA,GAAA9D,EAAA,CAAAsE,WAAA,SAAA5D,MAAA,CAAAsG,sBAAA,CAAAW,gBAAA,IAAA7D,KAAA,QAAA9D,EAAA,CAAAwE,aAAA,CAAuL;IAWvLxE,EAAA,CAAAc,SAAA,GAA0K;IAA1Kd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAAsG,sBAAA,CAAAa,YAAA,kBAAAnH,MAAA,CAAAsG,sBAAA,CAAAa,YAAA,CAAAzF,MAAA,KAAA1B,MAAA,CAAAsG,sBAAA,CAAAa,YAAA,IAAA/D,KAAA,GAAA9D,EAAA,CAAAsE,WAAA,SAAA5D,MAAA,CAAAsG,sBAAA,CAAAa,YAAA,IAAA/D,KAAA,QAAA9D,EAAA,CAAAwE,aAAA,CAA0K;IAGhJxE,EAAA,CAAAc,SAAA,GAGxB;IAHwBd,EAAA,CAAAkI,iBAAA,CAAAxH,MAAA,CAAAsG,sBAAA,CAAAtC,YAAA,GAAAhE,MAAA,CAAAsG,sBAAA,CAAAtC,YAAA,MAGxB;IACyB1E,EAAA,CAAAc,SAAA,EAAmC;IAAnCd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAAsG,sBAAA,CAAAtD,MAAA,CAAmC;;;;;;IAvDxE1D,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAwB,UAAA,IAAA2G,4CAAA,mBAAkE;IAGlEnI,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAE,MAAA,8HACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAwB,UAAA,IAAA4G,2CAAA,kBAEgE;IAQlEpI,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAwB,UAAA,IAAA6G,2CAAA,oBACsF;IA8CpFrI,EADF,CAAAC,cAAA,cAA6E,iBACkB;IAAlBD,EAAA,CAAAI,UAAA,mBAAAkI,8DAAA;MAAAtI,EAAA,CAAAO,aAAA,CAAAgI,IAAA;MAAA,MAAA7H,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2E,KAAA,EAAO;IAAA,EAAC;IAC1FrF,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA4F;IAAjBD,EAAA,CAAAI,UAAA,mBAAAoI,8DAAA;MAAAxI,EAAA,CAAAO,aAAA,CAAAgI,IAAA;MAAA,MAAA7H,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA6E,IAAA,EAAM;IAAA,EAAC;IACzFvF,EAAA,CAAAE,MAAA,sBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAtE+BH,EAAA,CAAAc,SAAA,EAA6B;IAA7Bd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAAmG,uBAAA,CAA6B;IAM1D7G,EAAA,CAAAc,SAAA,GAAyC;IAAzCd,EAAA,CAAAmB,UAAA,SAAAT,MAAA,CAAA2B,mBAAA,kBAAA3B,MAAA,CAAA2B,mBAAA,CAAAI,cAAA,CAAyC;IAW3CzC,EAAA,CAAAc,SAAA,EAA2D;IAA3Dd,EAAA,CAAAmB,UAAA,WAAAT,MAAA,CAAAmG,uBAAA,MAAAnG,MAAA,CAAAsG,sBAAA,CAA2D;;;;;IA+D3DhH,EADF,CAAAC,cAAA,eAAoB,WACZ;IAACD,EAAA,CAAAE,MAAA,GAAsB;IAC/BF,EAD+B,CAAAG,YAAA,EAAO,EAC/B;;;;IADEH,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAe,kBAAA,MAAAL,MAAA,CAAA+H,gBAAA,KAAsB;;;;;;IAS/BzI,EAAA,CAAAC,cAAA,iBAAiD;IAArBD,EAAA,CAAAI,UAAA,mBAAAsI,sEAAA;MAAA1I,EAAA,CAAAO,aAAA,CAAAoI,IAAA;MAAA,MAAAjI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAkI,QAAA,EAAU;IAAA,EAAC;IAAC5I,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADrNlE,OAAM,MAAO0I,qBAAqB;EAqBhCC,YACUC,qBAA+C,EAC/CC,aAA2B;IAD3B,KAAAD,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,aAAa,GAAbA,aAAa;IAtBd,KAAAtH,qBAAqB,GAAkC,EAAE;IAEzD,KAAAN,qBAAqB,GAAY,KAAK;IAErC,KAAA6H,SAAS,GAAG,IAAI5J,YAAY,EAAE;IAC9B,KAAA6J,mBAAmB,GAAG,IAAI7J,YAAY,EAAE;IACxC,KAAA8J,kBAAkB,GAAG,IAAI9J,YAAY,EAAE;IACvC,KAAA+J,2BAA2B,GAAG,IAAI/J,YAAY,EAAE;IAW1D,KAAAgK,UAAU,GAAY,KAAK;EAKvB;EAEJC,QAAQA,CAAA,GACR;EAEA9G,UAAUA,CAAC+G,GAAkB,EAAE7E,YAAoB;IACjD,IAAI,CAAC+D,gBAAgB,GAAG/D,YAAY,GAAGA,YAAY,GAAG,EAAE;IACxD,IAAI,CAAC2E,UAAU,GAAG,IAAI;IACtB,IAAI,CAACG,gBAAgB,GAAGD,GAAG;EAC7B;EACAX,QAAQA,CAAA;IACN,IAAI,CAACS,UAAU,GAAG,KAAK;EACzB;EAEA9H,iBAAiBA,CAACkI,QAAiB;IACjC,OAAO,CAACA,QAAQ,GAAG,KAAK,GAAG,KAAK;EAClC;EAEApI,iBAAiBA,CAACoI,QAAiB;IACjC,IAAIA,QAAQ,EAAE;MACZ,OAAO;QAAE,YAAY,EAAE,0CAA0C;QAAE,OAAO,EAAE;MAAO,CAAE;;IAEvF,OAAO;MAAE,iBAAiB,EAAE,SAAS;MAAE,OAAO,EAAE;IAAW,CAAE;EAC/D;EAEA5I,YAAYA,CAAC6I,aAA0C;IACrD,IAAI,CAACC,aAAa,GAAGD,aAAa,CAACE,OAAQ;IAC3C,IAAI,CAACC,wBAAwB,GAAGH,aAAa;IAC7C,IAAI,CAACI,oBAAoB,CAACJ,aAAa,CAAC,CAACK,SAAS,EAAE;EACtD;EAEAlI,IAAIA,CAAA;IACF,IAAI,CAACoH,SAAS,CAACe,IAAI,EAAE;EACvB;EAEAzE,IAAIA,CAAA;IACF5F,cAAc,CAACsK,OAAO,CAAC,IAAI,CAAC;IAE5B;IACA,IAAI,IAAI,CAACN,aAAa,KAAK,CAAC,EAAE;MAC5B;MACA,IAAI,CAAC,IAAI,CAAC3C,sBAAsB,EAAE;QAChC,IAAI,CAACgC,aAAa,CAACkB,YAAY,CAAC,SAAS,CAAC;QAC1CvK,cAAc,CAACsK,OAAO,CAAC,KAAK,CAAC;QAC7B;;KAEH,MAAM;MACL;MACA,IAAI,IAAI,CAAC5H,mBAAmB,EAAEpB,cAAe,IAAI,CAAC,EAAE;QAClD,IAAI,IAAI,CAAC+B,2BAA2B,IAAI,IAAI,EAAE;UAC5C,IAAI,CAACgG,aAAa,CAACkB,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC7H,mBAAmB,EAAEpB,cAAe,CAAC;UACpFtB,cAAc,CAACsK,OAAO,CAAC,KAAK,CAAC;UAC7B;;OAEH,MACI;QACH,IAAI,IAAI,CAAC5H,mBAAmB,EAAEpB,cAAe,IAAI,IAAI,CAAC+B,2BAA2B,EAAEZ,MAAM,EAAE;UACzF,IAAI,CAAC4G,aAAa,CAACkB,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC7H,mBAAmB,EAAEpB,cAAe,CAAC;UACpFtB,cAAc,CAACsK,OAAO,CAAC,KAAK,CAAC;UAC7B;;;;IAKN,IAAIE,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,kBAAkB,CAAC;IAC5DvK,WAAW,CAACqK,UAAW,EAAE;MACvBG,UAAU,EAAE,IAAI;MAAEC,OAAO,EAAE,IAAI;MAC/BC,cAAc,EAAGC,OAAO,IAAI;QAC1B,IAAIA,OAAO,CAACC,EAAE,KAAK,QAAQ,EAAE;UAC3B,OAAO,IAAI;;QAEb,OAAO,KAAK;MACd;KACD,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;MACjB,MAAMC,WAAW,GAAGD,MAAM,CAACE,SAAS,CAAC,WAAW,CAAC;MACjD,IAAIC,OAAO,GAAqC,EAAE;MAClD,IAAI,IAAI,CAACpB,aAAa,KAAK,CAAC,EAAE;QAC5BoB,OAAO,CAACC,IAAI,CAAC;UACX9H,sBAAsB,EAAE,IAAI,CAAC+H,cAAc,EAAE,CAAC/H,sBAAsB;UACpEgI,qBAAqB,EAAE,IAAI,CAACD,cAAc,EAAE,CAACE,oBAAoB;UACjE1D,OAAO,EAAE,IAAI,CAACkC,aAAa,KAAK,CAAC,GAAG,IAAI,CAAC3C,sBAAsB,CAACS,OAAO,GAAG,IAAI;UAC9E2D,WAAW,EAAEP,WAAW;UACxB7C,aAAa,EAAE;YACbC,8BAA8B,EAAE,IAAI,CAAC0B,aAAa,KAAK,CAAC,GAAG,IAAI,CAACtC,cAAc,CAACY,8BAA+B,GAAG;;SAEpH,CAAC;OACH,MAAM;QACL,IAAI,IAAI,CAAC5F,mBAAmB,EAAEpB,cAAc,IAAI,CAAC,EAAE;UACjD,IAAI,IAAI,CAAC+B,2BAA2B,EAAE;YACpC+H,OAAO,CAACC,IAAI,CAAC;cACX9H,sBAAsB,EAAG,IAAI,CAACF,2BAAmD,CAACE,sBAAsB;cACxGgI,qBAAqB,EAAE,IAAI,CAAC7I,mBAAmB,CAAC8I,oBAAqB;cACrE1D,OAAO,EAAE,IAAI;cACb2D,WAAW,EAAEP,WAAW;cACxB7C,aAAa,EAAE;gBACbC,8BAA8B,EAAE;;aAEnC,CAAC;;SAEL,MACI;UACH,IAAI,CAACjF,2BAA2B,CAACqI,OAAO,CAACC,CAAC,IAAG;YAC3CP,OAAO,CAACC,IAAI,CAAC;cACX9H,sBAAsB,EAAEoI,CAAC,CAACpI,sBAAsB;cAChDgI,qBAAqB,EAAE,IAAI,CAAC7I,mBAAmB,CAAC8I,oBAAqB;cACrE1D,OAAO,EAAE,IAAI;cACb2D,WAAW,EAAEP,WAAW;cACxB7C,aAAa,EAAE;gBACbC,8BAA8B,EAAE;;aAEnC,CAAC;UACJ,CAAC,CAAC;;;MAGN,IAAI,CAACc,qBAAqB,CAACwC,oDAAoD,CAAC;QAC9EC,IAAI,EAAET;OACP,CAAC,CAACU,IAAI,CACL5L,GAAG,CAAC6L,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvBhM,cAAc,CAACsK,OAAO,CAAC,KAAK,CAAC;UAC7B,IAAI,CAAC5E,KAAK,EAAE;UACZ,IAAI,CAAC8D,kBAAkB,CAACa,IAAI,EAAE;;MAElC,CAAC,CAAC,CACH,CAACD,SAAS,EAAE;IACf,CAAC,CAAC;EAEJ;EAEA1E,KAAKA,CAAA;IACH,IAAI,CAACsE,aAAa,GAAG,CAAC;IACtB,IAAI,CAACtH,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACW,2BAA2B,GAAG,EAAE;IACrC,IAAI,CAACgE,sBAAsB,GAAG,EAAoB;IAClD,IAAI,CAACH,uBAAuB,GAAG,EAAmC;IAClE,IAAI,CAACQ,cAAc,GAAG,EAAmB;EAC3C;EAEA4D,cAAcA,CAAA;IACZ,IAAIE,oBAAoB,GAAGS,SAAS;IACpC,IAAI1I,sBAAsB,GAAG0I,SAAS;IAEtC,IAAI,IAAI,CAACjC,aAAa,IAAI,CAAC,EAAE;MAC3BwB,oBAAoB,GAAG,IAAI,CAACtE,uBAAuB,CAACsE,oBAAqB;MACzEjI,sBAAsB,GAAG,IAAI,CAAC8D,sBAAsB,CAAC9D,sBAAuB;;IAE9E,OAAO;MAAEA,sBAAsB;MAAEiI;IAAoB,CAAE;EACzD;EAEArB,oBAAoBA,CAACJ,aAA0C;IAC7D,OAAO,IAAI,CAACX,qBAAqB,CAAC8C,+DAA+D,CAAC;MAChGL,IAAI,EAAE9B,aAAa,CAACyB;KACrB,CAAC,CAACM,IAAI,CACL5L,GAAG,CAAC6L,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtJ,mBAAmB,GAAGqJ,GAAG,CAACI,OAAQ;QAEvC,IAAI,IAAI,CAACnC,aAAa,KAAK,CAAC,EAAE;UAC5B;UACA,IAAI,CAAC9C,uBAAuB,GAAG;YAC7BpE,cAAc,EAAEiJ,GAAG,CAACI,OAAQ,CAACrJ,cAAc;YAC3CvB,SAAS,EAAEwK,GAAG,CAACI,OAAQ,CAAC5K,SAAS;YACjCiK,oBAAoB,EAAEO,GAAG,CAACI,OAAQ,CAACX;YACnC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;WACD;UAED;UACA,IAAI,CAACtE,uBAAuB,CAACkB,eAAe,EAAEsD,OAAO,CAACC,CAAC,IAAG;YACxDA,CAAC,CAACtD,aAAa,EAAEgD,IAAI,CAAC;cACpB1J,SAAS,EAAE,IAAI;cACf2G,8BAA8B,EAAE,IAAI;cACpC8D,SAAS,EAAE;aACZ,CAAC;UACJ,CAAC,CAAC;UAEF;UACA,MAAMC,iBAAiB,GAAG,IAAI,CAACnF,uBAAuB,CAACkB,eAAgB,CAACkE,IAAI,CAACX,CAAC,IAAIA,CAAC,CAAChK,SAAS,CAAC;UAC9F,IAAI,CAAC0F,sBAAsB,GAAGgF,iBAAiB,IAAI,IAAI,CAACnF,uBAAuB,CAACkB,eAAgB,CAAC,CAAC,CAAC;UAEnG;UACA,IAAI,IAAI,CAACf,sBAAsB,EAAE;YAC/B,IAAI,CAACK,cAAc,GAAG,IAAI,CAACL,sBAAsB,CAACgB,aAAa,EAAEiE,IAAI,CAACX,CAAC,IAAIA,CAAC,CAACS,SAAS,IAAI,IAAI,CAAC/E,sBAAsB,CAACS,OAAO,CAAC,IACzH,IAAI,CAACT,sBAAsB,CAACgB,aAAa,GAAG,CAAC,CAAC,IAC9C,IAAI,CAAChB,sBAAsB,CAACgB,aAAa,GAAG,CAAC,CAAC,IAC9C,EAAmB;YAExB,IAAI,CAAChB,sBAAsB,CAACS,OAAO,GAAG,IAAI,CAACT,sBAAsB,CAACS,OAAO,IAAI,IAAI,CAACJ,cAAc,CAAC0E,SAAS,IAAI,EAAE;;SAEnH,MAAM;UACL;UACA,IAAIG,QAAQ,GAAGR,GAAG,CAACI,OAAO,EAAEzH,oBAAoB,EAAE8H,MAAM,CAACb,CAAC,IAAIA,CAAC,CAAChK,SAAS,CAAC;UAC1E,IAAI8K,YAAiB;UACrBA,YAAY,GAAG1C,aAAa,EAAEzI,cAAc,IAAI,CAAC,GAAGiL,QAAS,CAAC,CAAC,CAAC,GAAGA,QAAQ;UAC3E,IAAI,CAAClJ,2BAA2B,GAAGoJ,YAAY;;;IAGrD,CAAC,CAAC,CACH;EACH;EAEAjF,cAAcA,CAACkF,KAAU;IACvB,IAAI,IAAI,CAAChF,cAAc,CAACY,8BAA8B,IAAI,IAAI,EAAE;MAC9D,IAAI,CAACjB,sBAAsB,CAACS,OAAO,GAAG,IAAI,CAACJ,cAAc,CAAC0E,SAAS;;EAEvE;EAEAxE,YAAYA,CAAC8E,KAAU;IACrB,IAAIA,KAAK,CAACC,KAAK,CAACrE,8BAA8B,KAAK,IAAI,EAAE;MACvD,IAAI,CAACjB,sBAAsB,CAACS,OAAO,GAAG4E,KAAK,CAACC,KAAK,CAACP,SAAS;KAC5D,MAAM;MACL,IAAI,CAAC/E,sBAAsB,CAACS,OAAO,GAAG,EAAE;;EAE5C;EAEA3F,cAAcA,CAAA;IACZ;IACA,OAAO,KAAK;EACd;EAEA0B,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACnB,mBAAmB,EAAEpB,cAAc,EAAE;MAC5C,IAAI,IAAI,CAAC+B,2BAA2B,CAACZ,MAAM,GAAG,IAAI,CAACC,mBAAmB,EAAEpB,cAAc,EAAE;QACtF,IAAI,CAAC+B,2BAA2B,CAACuJ,GAAG,EAAE;QACtC,IAAI,CAACvD,aAAa,CAACkB,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC7H,mBAAmB,EAAEpB,cAAe,CAAC;QACpFtB,cAAc,CAACsK,OAAO,CAAC,KAAK,CAAC;;;EAGnC;EAEAxG,WAAWA,CAAC+I,KAAyB;IACnC,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IACrB,OAAOA,KAAK,CAACC,cAAc,CAAC,OAAO,CAAC;EACtC;EAAC,QAAAC,CAAA,G;qBA1QU7D,qBAAqB,EAAA7I,EAAA,CAAA2M,iBAAA,CAAAC,EAAA,CAAAC,wBAAA,GAAA7M,EAAA,CAAA2M,iBAAA,CAAAG,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBnE,qBAAqB;IAAAoE,SAAA;IAAAC,MAAA;MAAAxL,qBAAA;MAAAiI,aAAA;MAAAvI,qBAAA;IAAA;IAAA+L,OAAA;MAAAlE,SAAA;MAAAC,mBAAA;MAAAC,kBAAA;MAAAC,2BAAA;IAAA;IAAAgE,UAAA;IAAAC,QAAA,GAAArN,EAAA,CAAAsN,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC1ClC5N,EAAA,CAAAC,cAAA,iBAA6F;QAoK3FD,EAlKA,CAAAwB,UAAA,IAAAsM,qCAAA,OAAW,IAAAC,qCAAA,QA4CA,IAAAC,qCAAA,QAgEA,IAAAC,qCAAA,QAsDD;QA2EZjO,EAAA,CAAAG,YAAA,EAAU;QAEVH,EAAA,CAAAkO,uBAAA,GAAc;QACZlO,EAAA,CAAAC,cAAA,kBACwG;QAD9FD,EAAA,CAAA2C,gBAAA,2BAAAwL,iEAAAtL,MAAA;UAAA7C,EAAA,CAAA+C,kBAAA,CAAA8K,GAAA,CAAAxE,UAAA,EAAAxG,MAAA,MAAAgL,GAAA,CAAAxE,UAAA,GAAAxG,MAAA;UAAA,OAAAA,MAAA;QAAA,EAAwB;QAEhC7C,EAAA,CAAAwB,UAAA,IAAA4M,4CAAA,yBAAgC;QAM9BpO,EADF,CAAAC,cAAA,aAAqD,aACU;QAC3DD,EAAA,CAAA0C,SAAA,cAA8D;;QAElE1C,EADE,CAAAG,YAAA,EAAM,EACF;QACNH,EAAA,CAAAwB,UAAA,KAAA6M,6CAAA,yBAAgC;QAGhCrO,EAAA,CAAA0C,SAAA,WACM;QACR1C,EAAA,CAAAG,YAAA,EAAW;;;;;QAlQXH,EAAA,CAAAc,SAAA,EA6OC;QA7ODd,EAAA,CAAAyE,aAAA,KAAA6J,OAAA,GAAAT,GAAA,CAAAlE,aAAA,OAAC,OAAA2E,OAAA,KAAD,CAAC,OAAAA,OAAA,KAAD,CAAC,OAAAA,OAAA,KAAD,CAAC,UA6OA;QAKCtO,EAAA,CAAAc,SAAA,GAAiD;QAAjDd,EAAA,CAAAuO,UAAA,CAAAvO,EAAA,CAAAwO,eAAA,KAAAC,GAAA,EAAiD;QADzCzO,EAAA,CAAAoD,gBAAA,YAAAyK,GAAA,CAAAxE,UAAA,CAAwB;QACkBrJ,EADjB,CAAAmB,UAAA,oBAAmB,mBAAmB,oBAAoB,gBAAAnB,EAAA,CAAAwO,eAAA,KAAAE,GAAA,EACU;QAQ3E1O,EAAA,CAAAc,SAAA,GAAuC;QAAvCd,EAAA,CAAAmB,UAAA,QAAAnB,EAAA,CAAAsE,WAAA,QAAAuJ,GAAA,CAAArE,gBAAA,GAAAxJ,EAAA,CAAAwE,aAAA,CAAuC;;;mBDjOjEpF,YAAY,EAAAuP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,OAAA,EACZnP,YAAY,EAAAoP,EAAA,CAAAC,eAAA,EAAAC,EAAA,CAAAC,aAAA,EACZ1P,iBAAiB,EAAA2P,EAAA,CAAAC,WAAA,EACjB/P,WAAW,EAAAgQ,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXjQ,cAAc,EAAAkQ,EAAA,CAAAC,QAAA,EACdpQ,YAAY,EAAAqQ,EAAA,CAAAC,MAAA,EACZ9P,cAAc,EAAA+P,GAAA,CAAAC,QAAA,EAIdrQ,YAAY;IAAAsQ,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}