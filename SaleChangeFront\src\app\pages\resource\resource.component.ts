import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { GroupedBuildCaseFile } from '../../../model/buildCaseFile';
import { BuildCaseGetFileRespone } from '../../../services/api/models';
import { LoadingService } from '../../shared/services/loading.service';
import { finalize } from 'rxjs';
import _ from 'lodash';
import { UtilityService } from '../../shared/services/utility.service';
import { BuildCaseService } from '../../../services/api/services/build-case.service';

@Component({
  selector: 'app-resource',
  standalone: true,
  imports: [RouterModule, CommonModule, HttpClientModule],
  templateUrl: './resource.component.html',
  styleUrl: './resource.component.scss'
})

export class ResourceComponent {
  constructor(
    private _buildcaseService: BuildCaseService,
    private utilityService: UtilityService
  ) {}
  
  buildCaseFileList = [] as GroupedBuildCaseFile[]

  ngOnInit(): void {
    this.getBuildCaseFile()
  }

  getBuildCaseFile() {
    LoadingService.loading(true);
    this._buildcaseService.apiBuildCaseGetBuildCaseFilePost$Json({})
      .pipe(
        finalize(() => LoadingService.loading(false))
      )
      .subscribe(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.convertArray(res.Entries)
        }
      })
  }

  convertArray(inputArray: BuildCaseGetFileRespone[]) {
    Object.entries(_.groupBy(inputArray, 'CCategoryName')).forEach(
      ([key, value]) => this.buildCaseFileList.push({
        CCategoryName: key,
        files: value
      })
    )
  }


  downloadFile(file: BuildCaseGetFileRespone) {
    this.utilityService.downloadFileFromUrl(file)
  }

}
