@if (!isReviewing) {
<section class="flex flex-col justify-center items-center w-full">
  <div class="flex flex-col text-center">
    <span class="text-2xl text-black">
      標準圖面審閱總覽
    </span>
    <span class="text-base mt-2">
      點擊項目可前往審閱
    </span>
  </div>
  <div class="flex flex-col lg:!w-[800px] w-full m-auto my-6 px-4">
    <div
      class="flex lg:!flex-row flex-col
        sm:items-center items-start justify-between
        cursor-pointer bg-[#F3F1EA99] mb-4 rounded-lg
        py-4 lg:!px-[22px] px-4"
      *ngFor="let houseReview of listHouseReview">
      <div class="w-full items-center sm:mx-0 sm:pl-3 mx-4 mt-3 sm:mt-1">
        <span class="text-base block w-full text-left text-black">
          {{houseReview.CReviewName!}}
        </span>
      </div>
      <button
        class="flex justify-center text-white font-medium items-center rounded-3xl bg-gray-500 p-2 lg:!mx-0 py-3
          lg:!w-[180px] w-full h-[47px] mt-3 lg:!mt-0"
        pButton (click)="gotoPickItem(houseReview)" [ngStyle]="handleStyleOption(houseReview.CIsReview!)">
        {{hanldeTitleOption(houseReview.CIsReview!)}}
      </button>
    </div>
  </div>

  <button class="btn-next butn1 flex justify-center items-center my-6 w-[180px] h-[47px]" pButton
    [disabled]="checkDisable()" [ngClass]="{'btn-enable': !checkDisable(), 'btn-disable': checkDisable()}"
    (click)="next()">
    下一步
  </button>
</section>
}@else {
<section class="flex flex-col justify-center items-center w-full">
  <div class="flex flex-col justify-center items-center w-full">
    <span class="text-2xl text-black">
      {{currentHouseReview.CReviewType == 1 ? '標準圖' : '設備圖'}}
    </span>
    @if(!!currentHouseReview.CFileUrl) {
    <iframe class="mt-3 w-full" [src]="currentHouseReview.CFileUrl | addBaseFile" width="800" height="500">
    </iframe>
    } @else {
    <div class="w-full h-[40vh] bg-[#d9d9d9] flex justify-center items-center">
      <div class="font-semibold text-lg">標準圖圖面<br>
        （內嵌PDF）</div>
    </div>
    }

    <button class="button2 !w-48 butn1 flex justify-center items-center mt-4" pButton (click)="handleReview()">
      確認
    </button>
  </div>
</section>
}
