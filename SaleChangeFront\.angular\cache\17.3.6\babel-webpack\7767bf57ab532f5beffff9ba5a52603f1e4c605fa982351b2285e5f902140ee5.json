{"ast": null, "code": "import { signal } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DialogPopupComponent } from '../../components/dialog-popup/dialog-popup.component';\nimport { QUOTATION_TEMPLATE } from '../../../assets/template/quotation-template';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/api/services/quotation.service\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = () => ({\n  header: \"\\u7248\\u672C\\u6B77\\u7A0B\",\n  content: \"\",\n  titleButtonLeft: \"\",\n  titleButtonRight: \"\"\n});\nfunction QuotationComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6);\n    i0.ɵɵtext(2, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QuotationComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8);\n    i0.ɵɵelement(2, \"i\", 9);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.loadQuotationHistory());\n    });\n    i0.ɵɵtext(5, \"\\u91CD\\u65B0\\u8F09\\u5165\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error(), \" \");\n  }\n}\nfunction QuotationComponent_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 16);\n    i0.ɵɵelement(3, \"i\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 18);\n    i0.ɵɵtext(5, \"\\u66AB\\u7121\\u5831\\u50F9\\u55AE\\u8A18\\u9304\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 19);\n    i0.ɵɵtext(7, \" \\u76EE\\u524D\\u7CFB\\u7D71\\u4E2D\\u6C92\\u6709\\u4EFB\\u4F55\\u5831\\u50F9\\u55AE\\uFF0C\");\n    i0.ɵɵelement(8, \"br\");\n    i0.ɵɵtext(9, \" \\u8ACB\\u78BA\\u8A8D\\u662F\\u5426\\u6709\\u5831\\u50F9\\u55AE\\u8CC7\\u6599\\u6216\\u5617\\u8A66\\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loadQuotationHistory());\n    });\n    i0.ɵɵelement(12, \"i\", 21);\n    i0.ɵɵtext(13, \" \\u91CD\\u65B0\\u8F09\\u5165\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"div\", 73)(3, \"div\", 74)(4, \"div\", 75);\n    i0.ɵɵelement(5, \"i\", 76);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"\\u5831\\u50F9\\u55AE\\u865F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 77)(9, \"span\", 78);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 79)(12, \"div\", 75);\n    i0.ɵɵelement(13, \"i\", 80);\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 81)(17, \"span\", 82);\n    i0.ɵɵelement(18, \"i\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"div\", 83)(21, \"div\", 84)(22, \"div\", 85);\n    i0.ɵɵelement(23, \"i\", 86);\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25, \"\\u5EFA\\u7ACB\\u65E5\\u671F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 87);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 88)(30, \"div\", 85);\n    i0.ɵɵelement(31, \"i\", 89);\n    i0.ɵɵelementStart(32, \"span\");\n    i0.ɵɵtext(33, \"\\u7E3D\\u91D1\\u984D\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 90);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"number\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r1.getVersionNumber(ctx_r1.selectedVersion()));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.selectedVersion()));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.getStatusIcon(ctx_r1.selectedVersion()));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusText(ctx_r1.selectedVersion()), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(28, 7, ctx_r1.getCreateDate(ctx_r1.selectedVersion()), \"yyyy/MM/dd\"), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" NT$ \", i0.ɵɵpipeBind2(36, 10, ctx_r1.selectedVersion().CTotalAmount, \"1.0-0\"), \" \");\n  }\n}\nfunction QuotationComponent_div_4_div_5_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_5_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewContract());\n    });\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵelement(2, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33)(4, \"span\", 34);\n    i0.ɵɵtext(5, \"\\u67E5\\u770B\\u5408\\u7D04\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 35);\n    i0.ɵɵtext(7, \"\\u6AA2\\u8996\\u5408\\u7D04\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 6);\n    i0.ɵɵelement(2, \"i\", 93);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u7248\\u672C\\u8CC7\\u6599\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95);\n    i0.ɵɵelement(2, \"i\", 96);\n    i0.ɵɵelementStart(3, \"span\", 97);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_5_div_30_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.loadQuotationHistory());\n    });\n    i0.ɵɵelement(6, \"i\", 21);\n    i0.ɵɵtext(7, \" \\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.error());\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98)(1, \"div\", 99);\n    i0.ɵɵelement(2, \"i\", 17);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u66AB\\u7121\\u7248\\u672C\\u8A18\\u9304\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_32_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"span\", 113);\n    i0.ɵɵelement(2, \"i\", 114);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const version_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", version_r8.tblQuotationItems.length, \" \\u500B\\u9805\\u76EE \");\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_32_div_1_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"span\", 116);\n    i0.ɵɵelement(2, \"i\", 117);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const version_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", version_r8.COtherName || \"\\u984D\\u5916\\u8CBB\\u7528\", \" (\", version_r8.COtherPercent, \"%) \");\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_32_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_5_div_32_div_1_Template_div_click_0_listener() {\n      const version_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectVersion(version_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 103)(2, \"div\", 104);\n    i0.ɵɵelement(3, \"i\", 17);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 105)(7, \"span\", 82);\n    i0.ɵɵelement(8, \"i\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 106)(11, \"div\", 107);\n    i0.ɵɵelement(12, \"i\", 108);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 109);\n    i0.ɵɵelement(17, \"i\", 59);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, QuotationComponent_div_4_div_5_div_32_div_1_div_21_Template, 4, 1, \"div\", 110)(22, QuotationComponent_div_4_div_5_div_32_div_1_div_22_Template, 4, 2, \"div\", 111);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const version_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"selected\", ctx_r1.selectedVersion() === version_r8);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.getVersionNumber(version_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(version_r8));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.getStatusIcon(version_r8));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusText(version_r8), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 11, ctx_r1.getCreateDate(version_r8), \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind2(20, 14, version_r8.CTotalAmount, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", version_r8.tblQuotationItems);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", version_r8.CShowOther && version_r8.COtherPercent);\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵtemplate(1, QuotationComponent_div_4_div_5_div_32_div_1_Template, 23, 17, \"div\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.quotationVersions());\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_34_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 136);\n    i0.ɵɵelement(1, \"i\", 129);\n    i0.ɵɵelementStart(2, \"span\", 137);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 138);\n    i0.ɵɵtext(5, \"\\u500B\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(((tmp_4_0 = ctx_r1.selectedVersion()) == null ? null : tmp_4_0.tblQuotationItems == null ? null : tmp_4_0.tblQuotationItems.length) || 0);\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_34_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 139)(1, \"div\", 124)(2, \"div\", 140)(3, \"span\", 141);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 126)(6, \"span\", 142);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 128)(9, \"span\", 143);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 130)(12, \"span\", 144);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 131)(16, \"span\", 145);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 132)(20, \"span\", 146);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    i0.ɵɵclassProp(\"row-even\", i_r10 % 2 === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r9.CItemName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r9.CUnit || \"-\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r9.CCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind2(14, 8, item_r9.CUnitPrice, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind2(18, 11, item_r9.CSubtotal, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r9.CRemark || \"-\");\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"div\", 119)(2, \"div\", 48)(3, \"div\", 49)(4, \"div\", 120)(5, \"h2\", 50);\n    i0.ɵɵelement(6, \"i\", 114);\n    i0.ɵɵtext(7, \" \\u5831\\u50F9\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, QuotationComponent_div_4_div_5_div_34_div_8_Template, 6, 1, \"div\", 121);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 122)(10, \"div\", 123)(11, \"div\", 124);\n    i0.ɵɵelement(12, \"i\", 125);\n    i0.ɵɵtext(13, \" \\u9805\\u76EE\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 126);\n    i0.ɵɵelement(15, \"i\", 127);\n    i0.ɵɵtext(16, \" \\u55AE\\u4F4D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 128);\n    i0.ɵɵelement(18, \"i\", 129);\n    i0.ɵɵtext(19, \" \\u6578\\u91CF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 130);\n    i0.ɵɵelement(21, \"i\", 59);\n    i0.ɵɵtext(22, \" \\u55AE\\u50F9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 131);\n    i0.ɵɵelement(24, \"i\", 51);\n    i0.ɵɵtext(25, \" \\u5C0F\\u8A08 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 132);\n    i0.ɵɵelement(27, \"i\", 133);\n    i0.ɵɵtext(28, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 134);\n    i0.ɵɵtemplate(30, QuotationComponent_div_4_div_5_div_34_div_30_Template, 22, 14, \"div\", 135);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.selectedVersion()) == null ? null : tmp_3_0.tblQuotationItems);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", (tmp_4_0 = ctx_r1.selectedVersion()) == null ? null : tmp_4_0.tblQuotationItems);\n  }\n}\nfunction QuotationComponent_div_4_div_5_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"div\", 54);\n    i0.ɵɵelement(2, \"i\", 117);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 56);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.selectedVersion().COtherName || \"\\u984D\\u5916\\u8CBB\\u7528\", \" (\", ctx_r1.selectedVersion().COtherPercent, \"%)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" NT$ \", i0.ɵɵpipeBind2(7, 3, ctx_r1.calculateOtherFee(ctx_r1.selectedVersion().tblQuotationItems, ctx_r1.selectedVersion().COtherPercent), \"1.0-0\"), \" \");\n  }\n}\nfunction QuotationComponent_div_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 22)(2, \"div\", 23);\n    i0.ɵɵtemplate(3, QuotationComponent_div_4_div_5_div_3_Template, 37, 13, \"div\", 24);\n    i0.ɵɵelementStart(4, \"div\", 25)(5, \"div\", 26)(6, \"h3\", 27);\n    i0.ɵɵelement(7, \"i\", 28);\n    i0.ɵɵtext(8, \" \\u64CD\\u4F5C\\u529F\\u80FD \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 29)(10, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_5_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleVersionHistory());\n    });\n    i0.ɵɵelementStart(11, \"div\", 31);\n    i0.ɵɵelement(12, \"i\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 33)(14, \"span\", 34);\n    i0.ɵɵtext(15, \"\\u7248\\u672C\\u6B77\\u7A0B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 35);\n    i0.ɵɵtext(17, \"\\u67E5\\u770B\\u6240\\u6709\\u7248\\u672C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function QuotationComponent_div_4_div_5_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.printPreview());\n    });\n    i0.ɵɵelementStart(19, \"div\", 31);\n    i0.ɵɵelement(20, \"i\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 33)(22, \"span\", 34);\n    i0.ɵɵtext(23, \"\\u9810\\u89BD\\u5217\\u5370\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 35);\n    i0.ɵɵtext(25, \"\\u5217\\u5370\\u5831\\u50F9\\u55AE\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(26, QuotationComponent_div_4_div_5_button_26_Template, 8, 0, \"button\", 38);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(27, \"app-dialog-popup\", 39);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function QuotationComponent_div_4_div_5_Template_app_dialog_popup_visibleChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.showVersionHistory, $event) || (ctx_r1.showVersionHistory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(28, \"div\", 40);\n    i0.ɵɵtemplate(29, QuotationComponent_div_4_div_5_div_29_Template, 5, 0, \"div\", 41)(30, QuotationComponent_div_4_div_5_div_30_Template, 8, 1, \"div\", 42)(31, QuotationComponent_div_4_div_5_div_31_Template, 5, 0, \"div\", 43)(32, QuotationComponent_div_4_div_5_div_32_Template, 2, 1, \"div\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 45);\n    i0.ɵɵtemplate(34, QuotationComponent_div_4_div_5_div_34_Template, 31, 2, \"div\", 46);\n    i0.ɵɵelementStart(35, \"div\", 47)(36, \"div\", 48)(37, \"div\", 49)(38, \"h2\", 50);\n    i0.ɵɵelement(39, \"i\", 51);\n    i0.ɵɵtext(40, \" \\u91D1\\u984D\\u660E\\u7D30 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 52)(42, \"div\", 53)(43, \"div\", 54);\n    i0.ɵɵelement(44, \"i\", 55);\n    i0.ɵɵelementStart(45, \"span\");\n    i0.ɵɵtext(46, \"\\u5C0F\\u8A08\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 56);\n    i0.ɵɵtext(48);\n    i0.ɵɵpipe(49, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(50, QuotationComponent_div_4_div_5_div_50_Template, 8, 6, \"div\", 57);\n    i0.ɵɵelementStart(51, \"div\", 58)(52, \"div\", 54);\n    i0.ɵɵelement(53, \"i\", 59);\n    i0.ɵɵelementStart(54, \"span\");\n    i0.ɵɵtext(55, \"\\u7E3D\\u8A08\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 60);\n    i0.ɵɵtext(57);\n    i0.ɵɵpipe(58, \"number\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(59, \"div\", 61)(60, \"div\", 48)(61, \"div\", 49)(62, \"h2\", 50);\n    i0.ɵɵelement(63, \"i\", 62);\n    i0.ɵɵtext(64, \" \\u91CD\\u8981\\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 63)(66, \"div\", 64)(67, \"h4\", 65);\n    i0.ɵɵelement(68, \"i\", 66);\n    i0.ɵɵtext(69, \" \\u6548\\u671F\\u8207\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"div\", 67)(71, \"span\", 68);\n    i0.ɵɵtext(72, \"\\u5831\\u50F9\\u55AE\\u6709\\u6548\\u671F\\u9650\\u70BA30\\u5929\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(73, \"div\", 64)(74, \"h4\", 65);\n    i0.ɵɵelement(75, \"i\", 59);\n    i0.ɵɵtext(76, \" \\u50F9\\u683C\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 67)(78, \"span\", 68);\n    i0.ɵɵtext(79, \"\\u50F9\\u683C\\u542B\\u7A05\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(80, \"div\", 64)(81, \"h4\", 65);\n    i0.ɵɵelement(82, \"i\", 69);\n    i0.ɵɵtext(83, \" \\u4ED8\\u6B3E\\u689D\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"div\", 67)(85, \"span\", 68);\n    i0.ɵɵtext(86, \"\\u4ED8\\u6B3E\\u65B9\\u5F0F\\uFF1A\\u4EA4\\u8CA8\\u5F8C30\\u5929\\u5167\\u4ED8\\u6B3E\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(87, \"div\", 64)(88, \"h4\", 65);\n    i0.ɵɵelement(89, \"i\", 70);\n    i0.ɵɵtext(90, \" \\u806F\\u7E6B\\u65B9\\u5F0F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"div\", 67)(92, \"span\", 68);\n    i0.ɵɵtext(93, \"\\u5982\\u6709\\u7591\\u554F\\u8ACB\\u6D3D\\u8A62\\u696D\\u52D9\\u4EBA\\u54E1\");\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedVersion());\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedVersion() && ctx_r1.convertStatusFromAPI(ctx_r1.selectedVersion().CQuotationStatus) === \"confirmed\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r1.showVersionHistory);\n    i0.ɵɵproperty(\"textData\", i0.ɵɵpureFunction0(18, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.error());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading() && !ctx_r1.error() && ctx_r1.quotationVersions().length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading() && !ctx_r1.error() && ctx_r1.quotationVersions().length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedVersion());\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" NT$ \", i0.ɵɵpipeBind2(49, 12, ctx_r1.calculateSubtotal(ctx_r1.selectedVersion().tblQuotationItems), \"1.0-0\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedVersion().CShowOther && ctx_r1.selectedVersion().COtherPercent);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" NT$ \", i0.ɵɵpipeBind2(58, 15, ctx_r1.calculateTotalWithOther(ctx_r1.selectedVersion()), \"1.0-0\"), \" \");\n  }\n}\nfunction QuotationComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 11)(2, \"div\", 12);\n    i0.ɵɵtext(3, \"\\u5831\\u50F9\\u55AE\\u7BA1\\u7406\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, QuotationComponent_div_4_div_4_Template, 14, 0, \"div\", 13)(5, QuotationComponent_div_4_div_5_Template, 94, 19, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.quotationVersions().length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.quotationVersions().length > 0);\n  }\n}\nexport class QuotationComponent {\n  constructor(quotationService) {\n    this.quotationService = quotationService;\n    this.quotationVersions = signal([]);\n    this.selectedVersion = signal(null);\n    this.showVersionHistory = signal(false);\n    this.isLoading = signal(false);\n    this.error = signal(null);\n  }\n  ngOnInit() {\n    this.loadQuotationHistory();\n  }\n  loadQuotationHistory() {\n    this.isLoading.set(true);\n    this.error.set(null);\n    this.quotationService.apiQuotationGetQuotationHistoryPost$Json().subscribe({\n      next: response => {\n        if (response.Entries) {\n          this.quotationVersions.set(response.Entries);\n          if (response.Entries.length > 0) {\n            this.selectedVersion.set(response.Entries[0]); // 選擇第一個版本\n          }\n        } else {\n          this.quotationVersions.set([]); // 確保設置為空陣列\n        }\n        this.isLoading.set(false);\n      },\n      error: error => {\n        console.error('載入報價歷程失敗:', error);\n        this.error.set('載入報價歷程失敗，請稍後再試。');\n        this.quotationVersions.set([]); // 確保錯誤時清空資料\n        this.selectedVersion.set(null);\n        this.isLoading.set(false);\n      }\n    });\n  }\n  selectVersion(version) {\n    this.selectedVersion.set(version);\n    this.showVersionHistory.set(false); // 選擇後關閉 dialog\n  }\n  toggleVersionHistory() {\n    this.showVersionHistory.set(!this.showVersionHistory());\n  }\n  onVersionHistoryClose() {\n    this.showVersionHistory.set(false);\n  }\n  // 輔助方法來獲取狀態樣式類別\n  getStatusClass(version) {\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\n    switch (status) {\n      case 'pending':\n        return 'status-pending';\n      case 'quoted':\n        return 'status-quoted';\n      case 'confirmed':\n        return 'status-confirmed';\n      case 'sent':\n        return 'status-sent';\n      case 'approved':\n        return 'status-approved';\n      case 'rejected':\n        return 'status-rejected';\n      case 'expired':\n        return 'status-expired';\n      default:\n        return 'status-pending';\n    }\n  }\n  // 輔助方法來獲取狀態圖示\n  getStatusIcon(version) {\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\n    switch (status) {\n      case 'pending':\n        return 'icon-clock';\n      case 'quoted':\n        return 'icon-file-text';\n      case 'confirmed':\n        return 'icon-check-circle';\n      case 'sent':\n        return 'icon-send';\n      case 'approved':\n        return 'icon-check-circle-2';\n      case 'rejected':\n        return 'icon-x-circle';\n      case 'expired':\n        return 'icon-alert-triangle';\n      default:\n        return 'icon-clock';\n    }\n  }\n  // 輔助方法來獲取狀態文字\n  getStatusText(version) {\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\n    switch (status) {\n      case 'pending':\n        return '待報價';\n      case 'quoted':\n        return '已報價';\n      case 'confirmed':\n        return '已簽回';\n      case 'sent':\n        return '已發送';\n      case 'approved':\n        return '已核准';\n      case 'rejected':\n        return '已拒絕';\n      case 'expired':\n        return '已過期';\n      default:\n        return '待報價';\n    }\n  }\n  printPreview() {\n    // 創建新視窗顯示套印模板\n    const printWindow = window.open('', '_blank', 'width=800,height=600');\n    if (printWindow && this.selectedVersion()) {\n      const templateContent = this.generatePrintTemplate();\n      printWindow.document.write(templateContent);\n      printWindow.document.close();\n      // 等待內容載入後執行列印\n      printWindow.onload = () => {\n        printWindow.print();\n        printWindow.close();\n      };\n    }\n  }\n  generatePrintTemplate() {\n    const version = this.selectedVersion();\n    if (!version) return '';\n    // 使用新的模板\n    let template = QUOTATION_TEMPLATE;\n    // 準備模板變數\n    const items = version.tblQuotationItems || [];\n    const subtotal = this.calculateSubtotal(items);\n    const otherFee = version.CShowOther ? this.calculateOtherFee(items, version.COtherPercent) : 0;\n    const totalAmount = this.calculateTotalWithOther(version);\n    // 生成項目HTML\n    const itemsHtml = items.map((item, index) => `\n      <tr>\n        <td class=\"text-center\">${index + 1}</td>\n        <td>${item.CItemName || ''}</td>\n        <td class=\"text-right\">NT$ ${(item.CUnitPrice || 0).toLocaleString()}</td>\n        <td class=\"text-center\">${item.CUnit || ''}</td>\n        <td class=\"text-center\">${item.CCount || 0}</td>\n        <td class=\"text-right\">NT$ ${(item.CSubtotal || 0).toLocaleString()}</td>\n        <td>${item.CRemark || ''}</td>\n      </tr>\n    `).join('');\n    // 生成額外費用HTML\n    const additionalFeeHtml = version.CShowOther && version.COtherPercent ? `<div class=\"additional-fee\">${version.COtherName || '額外費用'} (${version.COtherPercent}%)：NT$ ${otherFee.toLocaleString()}</div>` : '';\n    // 替換模板變數\n    template = template.replace(/{{buildCaseName}}/g, '建案名稱') // 這裡可以根據實際需求調整\n    .replace(/{{houseHold}}/g, '戶別資訊') // 這裡可以根據實際需求調整\n    .replace(/{{floor}}/g, '樓層') // 這裡可以根據實際需求調整\n    .replace(/{{printDate}}/g, new Date().toLocaleDateString('zh-TW')).replace(/{{itemsHtml}}/g, itemsHtml).replace(/{{subtotalAmount}}/g, `NT$ ${subtotal.toLocaleString()}`).replace(/{{additionalFeeHtml}}/g, additionalFeeHtml).replace(/{{totalAmount}}/g, `NT$ ${totalAmount.toLocaleString()}`).replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\n    return template;\n  }\n  viewVersionDetails(version) {\n    this.selectedVersion.set(version);\n    // 可以添加額外的檢視邏輯，如滾動到詳情區域\n    const detailsElement = document.querySelector('.quotation-details');\n    if (detailsElement) {\n      detailsElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n    }\n  }\n  compareVersion(version) {\n    // TODO: 實作版本比較功能\n    alert(`比較版本 ${version.CversionNo} 與當前版本 ${this.selectedVersion()?.CversionNo} 的功能開發中...`);\n  }\n  reviseQuotation() {\n    // TODO: 實作修改報價功能\n    alert('修改報價功能開發中...');\n  }\n  viewContract() {\n    // TODO: 實作查看合約功能\n    alert('查看合約功能開發中...');\n  }\n  convertStatusFromAPI(statusCode) {\n    switch (statusCode) {\n      case 1:\n        return 'pending';\n      // 待報價\n      case 2:\n        return 'quoted';\n      // 已報價\n      case 3:\n        return 'confirmed';\n      // 已簽回\n      default:\n        return 'pending';\n      // 預設為待報價\n    }\n  }\n  // 輔助方法來獲取報價編號\n  getQuotationNumber(version) {\n    return `Q${version.CQuotationVersionID}`;\n  }\n  // 輔助方法來獲取版本號\n  getVersionNumber(version) {\n    return version.CversionNo || 'v1.0';\n  }\n  // 輔助方法來獲取建立日期\n  getCreateDate(version) {\n    return version.CCreateDT ? new Date(version.CCreateDT) : new Date();\n  }\n  // 輔助方法來計算小計\n  calculateSubtotal(items) {\n    if (!items) return 0;\n    return items.reduce((sum, item) => sum + (item.CSubtotal || 0), 0);\n  }\n  // 輔助方法來計算稅額\n  calculateTax(items) {\n    const subtotal = this.calculateSubtotal(items);\n    return Math.round(subtotal * 0.1); // 10% 稅率\n  }\n  // 輔助方法來計算額外費用\n  calculateOtherFee(items, otherPercent) {\n    if (!otherPercent || otherPercent <= 0) return 0;\n    const subtotal = this.calculateSubtotal(items);\n    return Math.round(subtotal * (otherPercent / 100));\n  }\n  // 輔助方法來計算含額外費用的總計\n  calculateTotalWithOther(version) {\n    const subtotal = this.calculateSubtotal(version.tblQuotationItems);\n    const otherFee = version.CShowOther ? this.calculateOtherFee(version.tblQuotationItems, version.COtherPercent) : 0;\n    return subtotal + otherFee;\n  }\n  static #_ = this.ɵfac = function QuotationComponent_Factory(t) {\n    return new (t || QuotationComponent)(i0.ɵɵdirectiveInject(i1.QuotationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: QuotationComponent,\n    selectors: [[\"app-quotation\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 5,\n    vars: 3,\n    consts: [[1, \"wrapper\"], [1, \"content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"error-container\"], [1, \"error-message\"], [1, \"icon-alert\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"flex\", \"justify-center\", \"mb-6\"], [1, \"page-title\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-icon\"], [1, \"icon-file-text\"], [1, \"empty-title\"], [1, \"empty-description\"], [1, \"empty-actions\"], [1, \"icon-refresh-cw\"], [1, \"quotation-header\"], [1, \"header-content\"], [\"class\", \"quotation-info\", 4, \"ngIf\"], [1, \"header-actions-container\"], [1, \"actions-header\"], [1, \"actions-title\"], [1, \"pi\", \"pi-cog\"], [1, \"header-actions\"], [1, \"btn\", \"btn-action\", \"btn-outline\", 3, \"click\"], [1, \"btn-icon\"], [1, \"pi\", \"pi-history\"], [1, \"btn-content\"], [1, \"btn-label\"], [1, \"btn-description\"], [1, \"btn\", \"btn-action\", \"btn-primary\", 3, \"click\"], [1, \"pi\", \"pi-print\"], [\"class\", \"btn btn-action btn-success\", 3, \"click\", 4, \"ngIf\"], [3, \"visibleChange\", \"visible\", \"textData\"], [1, \"version-dialog-content\"], [\"class\", \"dialog-loading\", 4, \"ngIf\"], [\"class\", \"dialog-error\", 4, \"ngIf\"], [\"class\", \"dialog-empty\", 4, \"ngIf\"], [\"class\", \"version-list\", 4, \"ngIf\"], [1, \"quotation-content\"], [\"class\", \"quotation-details\", 4, \"ngIf\"], [1, \"section\", \"calculation-section\"], [1, \"section-card\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"icon-calculator\"], [1, \"calculation-table\"], [1, \"calc-row\", \"subtotal-row\"], [1, \"calc-label\"], [1, \"icon-plus\"], [1, \"calc-value\"], [\"class\", \"calc-row other-fee-row\", 4, \"ngIf\"], [1, \"calc-row\", \"total-row\"], [1, \"icon-dollar-sign\"], [1, \"calc-value\", \"total-amount\"], [1, \"section\", \"notes-section\"], [1, \"icon-info\"], [1, \"notes-content\"], [1, \"note-category\"], [1, \"note-category-title\"], [1, \"icon-clock\"], [1, \"note-item\"], [1, \"note-text\"], [1, \"icon-credit-card\"], [1, \"icon-help-circle\"], [1, \"quotation-info\"], [1, \"quotation-main-section\"], [1, \"quotation-number-section\"], [1, \"number-container\"], [1, \"field-title\"], [1, \"pi\", \"pi-file\"], [1, \"number-badge\"], [1, \"number-text\"], [1, \"status-container\"], [1, \"pi\", \"pi-info-circle\"], [1, \"status-wrapper\"], [1, \"status-badge\", 3, \"ngClass\"], [1, \"quotation-details-grid\"], [1, \"detail-item\"], [1, \"detail-label\"], [1, \"pi\", \"pi-calendar\"], [1, \"detail-value\"], [1, \"detail-item\", \"amount-highlight\"], [1, \"pi\", \"pi-dollar\"], [1, \"detail-value\", \"amount-value\"], [1, \"btn\", \"btn-action\", \"btn-success\", 3, \"click\"], [1, \"dialog-loading\"], [1, \"icon-loader\"], [1, \"dialog-error\"], [1, \"error-content\"], [1, \"icon-alert-triangle\"], [1, \"error-text\"], [1, \"dialog-empty\"], [1, \"empty-content\"], [1, \"version-list\"], [\"class\", \"version-item\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"version-item\", 3, \"click\"], [1, \"version-header\"], [1, \"version-number\"], [1, \"version-status\"], [1, \"version-details\"], [1, \"version-date\"], [1, \"icon-calendar\"], [1, \"version-amount\"], [\"class\", \"version-items\", 4, \"ngIf\"], [\"class\", \"version-other-fee\", 4, \"ngIf\"], [1, \"version-items\"], [1, \"items-count\"], [1, \"icon-list\"], [1, \"version-other-fee\"], [1, \"other-fee-info\"], [1, \"icon-plus-circle\"], [1, \"quotation-details\"], [1, \"section\", \"items-section\"], [1, \"section-title-wrapper\"], [\"class\", \"items-count-badge\", 4, \"ngIf\"], [1, \"items-table\"], [1, \"table-header\"], [1, \"col-item\"], [1, \"icon-package\"], [1, \"col-unit\"], [1, \"icon-tag\"], [1, \"col-qty\"], [1, \"icon-hash\"], [1, \"col-price\"], [1, \"col-total\"], [1, \"col-remark\"], [1, \"icon-message-square\"], [1, \"table-body\"], [\"class\", \"table-row\", 3, \"row-even\", 4, \"ngFor\", \"ngForOf\"], [1, \"items-count-badge\"], [1, \"count-text\"], [1, \"count-label\"], [1, \"table-row\"], [1, \"item-info\"], [1, \"item-name\"], [1, \"unit-value\"], [1, \"qty-value\"], [1, \"price-value\"], [1, \"total-value\"], [1, \"remark-value\"], [1, \"calc-row\", \"other-fee-row\"]],\n    template: function QuotationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, QuotationComponent_div_2_Template, 3, 0, \"div\", 2)(3, QuotationComponent_div_3_Template, 6, 1, \"div\", 3)(4, QuotationComponent_div_4_Template, 6, 2, \"div\", 4);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.error());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading() && !ctx.error());\n      }\n    },\n    dependencies: [CommonModule, i2.NgClass, i2.NgForOf, i2.NgIf, i2.DecimalPipe, i2.DatePipe, DialogPopupComponent],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}[_nghost-%COMP%]{display:block}.wrapper[_ngcontent-%COMP%]{min-height:100vh;background:#f8f7f4}.content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.page-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#231815;text-align:center;margin-bottom:2rem}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:200px;background:#fff;border-radius:12px;margin:20px 0;box-shadow:0 1px 3px #ae9b661a}.loading-spinner[_ngcontent-%COMP%]{font-size:18px;color:#b8a676;animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px;background:#fff;border-radius:12px;margin:20px 0;border:1px solid #FFDD64;box-shadow:0 1px 3px #ae9b661a}.error-message[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:10px;margin-bottom:20px;font-size:16px;color:#f1502f}.status-draft[_ngcontent-%COMP%]{background:#b8a67626;color:#9b8a5a;border:1px solid rgba(184,166,118,.25)}.status-sent[_ngcontent-%COMP%]{background:#b8a6761a;color:#9b8a5a;border:1px solid rgba(184,166,118,.25)}.status-approved[_ngcontent-%COMP%]{background:#23a4151a;color:#23a415;border:1px solid rgba(35,164,21,.2)}.status-rejected[_ngcontent-%COMP%]{background:#f1502f14;color:#f1502f;border:1px solid rgba(241,80,47,.2)}.status-expired[_ngcontent-%COMP%]{background:#ffdd641a;color:#e4b200;border:1px solid rgba(255,221,100,.2)}.header-actions[_ngcontent-%COMP%]{display:flex;gap:1rem}.btn[_ngcontent-%COMP%]{padding:.5rem 1rem;border:none;border-radius:.5rem;font-weight:500;cursor:pointer;display:flex;align-items:center;gap:.5rem;transition:all .3s ease;font-size:.875rem}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;box-shadow:0 2px 8px #ae9b6633}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#ae9b66,#9b8a5a);box-shadow:0 4px 12px #ae9b664d;transform:translateY(-1px)}.btn-secondary[_ngcontent-%COMP%]{background:#b8a6760d;color:#231815;border:1px solid rgba(184,166,118,.2)}.btn-secondary[_ngcontent-%COMP%]:hover{background:#b8a67626;border-color:#b8a6764d;transform:translateY(-1px)}.btn-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,#23a415,#23a415e6);color:#fff;box-shadow:#23a41540 0 2px 8px}.btn-success[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#23a415f2,#23a415d9);box-shadow:#23a41559 0 4px 12px;transform:translateY(-1px)}.quotation-content[_ngcontent-%COMP%]{width:100%}.quotation-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1.5rem}.quotation-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#fffffff2);border-radius:16px;box-shadow:0 8px 32px #00000014;border:1px solid rgba(184,166,118,.1);margin-bottom:2rem;overflow:hidden;position:relative}.quotation-header[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;right:0;width:200px;height:100%;background:linear-gradient(45deg,transparent 0%,rgba(184,166,118,.03) 100%);pointer-events:none}.header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;padding:2rem;gap:2rem;position:relative;z-index:1}@media (max-width: 768px){.header-content[_ngcontent-%COMP%]{flex-direction:column;gap:1.5rem}}.quotation-info[_ngcontent-%COMP%]{flex:1;min-width:0}.quotation-main-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1.5rem}.quotation-number-section[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:2rem;flex-wrap:wrap}@media (max-width: 768px){.quotation-number-section[_ngcontent-%COMP%]{flex-direction:column;gap:1.5rem}}.number-container[_ngcontent-%COMP%], .status-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem;min-width:0;flex:1}@media (max-width: 768px){.number-container[_ngcontent-%COMP%], .status-container[_ngcontent-%COMP%]{width:100%}}.field-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.875rem;font-weight:600;color:#23181599;text-transform:uppercase;letter-spacing:.5px;margin-bottom:.25rem}.field-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem;color:#b8a676;opacity:.8}.field-title[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{line-height:1.2}.number-badge[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;padding:.875rem 1.5rem;border-radius:12px;font-size:1.125rem;font-weight:700;box-shadow:0 4px 16px #b8a6764d;position:relative;min-height:48px;transition:all .3s ease}.number-badge[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(135deg,rgba(255,255,255,.2) 0%,transparent 100%);border-radius:12px;pointer-events:none}.number-badge[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 6px 20px #b8a67666}.number-badge[_ngcontent-%COMP%]   .number-text[_ngcontent-%COMP%]{font-size:1.125rem;letter-spacing:.5px;line-height:1.2;position:relative;z-index:1}@media (max-width: 768px){.number-badge[_ngcontent-%COMP%]{padding:.75rem 1.25rem;font-size:1rem;min-height:44px}.number-badge[_ngcontent-%COMP%]   .number-text[_ngcontent-%COMP%]{font-size:1rem}}.status-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-start;min-height:48px}@media (max-width: 768px){.status-wrapper[_ngcontent-%COMP%]{min-height:44px;justify-content:center}}.status-badge[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1.25rem;border-radius:12px;font-size:.875rem;font-weight:600;text-transform:uppercase;letter-spacing:.5px;box-shadow:0 3px 12px #0000001a;transition:all .3s ease;min-height:48px;justify-content:center}.status-badge[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 16px #00000026}.status-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}@media (max-width: 768px){.status-badge[_ngcontent-%COMP%]{padding:.625rem 1rem;font-size:.8rem;min-height:44px}.status-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem}}.status-badge.status-pending[_ngcontent-%COMP%], .status-badge.status-draft[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67626,#b8a67614);color:#9b8a5a;border:1px solid rgba(184,166,118,.25)}.status-badge.status-sent[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a6761f,#b8a6760f);color:#9b8a5a;border:1px solid rgba(184,166,118,.25)}.status-badge.status-approved[_ngcontent-%COMP%], .status-badge.status-confirmed[_ngcontent-%COMP%]{background:linear-gradient(135deg,#23a41526,#23a41514);color:#23a415;border:1px solid rgba(35,164,21,.25)}.status-badge.status-rejected[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f1502f26,#f1502f14);color:#f1502f;border:1px solid rgba(241,80,47,.25)}.status-badge.status-expired[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffdd6426,#ffdd6414);color:#e4b200;border:1px solid rgba(255,221,100,.25)}.quotation-details-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1.25rem}@media (max-width: 480px){.quotation-details-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}.detail-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem;padding:1rem;background:#e6f0f34d;border-radius:12px;border:1px solid rgba(184,166,118,.08);transition:all .3s ease}.detail-item[_ngcontent-%COMP%]:hover{background:#e6f0f380;border-color:#b8a67626;transform:translateY(-1px)}.detail-item.amount-highlight[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a6760d,#ae9b6614);border-color:#b8a67633}.detail-item.amount-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#b8a67614,#ae9b661f);border-color:#b8a6764d}.detail-item.amount-highlight[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%]{color:#9b8a5a;font-weight:700}.detail-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#23181599;font-size:.875rem;font-weight:500;text-transform:uppercase;letter-spacing:.5px}.detail-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem;opacity:.7}.detail-value[_ngcontent-%COMP%]{color:#231815;font-size:1rem;font-weight:600}.detail-value.amount-value[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:700}.header-actions-container[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67608,#b8a67603);border:2px solid rgba(184,166,118,.15);border-radius:16px;padding:1.5rem;box-shadow:0 4px 20px #b8a67614;position:relative;overflow:hidden;min-width:320px}.header-actions-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:-50%;right:-50%;width:100px;height:100px;background:radial-gradient(circle,rgba(184,166,118,.1) 0%,transparent 70%);border-radius:50%;pointer-events:none}@media (max-width: 768px){.header-actions-container[_ngcontent-%COMP%]{min-width:auto;width:100%;padding:1.25rem}}.actions-header[_ngcontent-%COMP%]{margin-bottom:1.25rem;position:relative;z-index:1}.actions-header[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:1rem;font-weight:600;color:#9b8a5a;margin:0;text-transform:uppercase;letter-spacing:.5px}.actions-header[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.125rem;color:#b8a676}.header-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem;position:relative;z-index:1}@media (max-width: 768px){.header-actions[_ngcontent-%COMP%]{gap:.625rem}}.btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1.25rem;border:none;border-radius:12px;font-weight:600;font-size:.875rem;cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);text-decoration:none;position:relative;overflow:hidden;min-width:120px;justify-content:center}.btn[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(135deg,rgba(255,255,255,.1) 0%,transparent 100%);opacity:0;transition:opacity .3s ease}.btn[_ngcontent-%COMP%]:hover:before{opacity:1}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{white-space:nowrap}.btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #00000026}.btn[_ngcontent-%COMP%]:active{transform:translateY(0);box-shadow:0 4px 15px #0000001a}.btn-action[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:1rem 1.5rem;border-radius:14px;font-weight:600;cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);text-decoration:none;position:relative;overflow:hidden;min-width:200px;justify-content:flex-start;border:2px solid transparent}.btn-action[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(135deg,rgba(255,255,255,.1) 0%,transparent 100%);opacity:0;transition:opacity .3s ease}.btn-action[_ngcontent-%COMP%]:hover:before{opacity:1}.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:10px;background:#ffffff26;flex-shrink:0;transition:all .3s ease}.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem}.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start;gap:.25rem;flex:1}.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{font-size:.9rem;font-weight:600;line-height:1.2}.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{font-size:.75rem;opacity:.8;font-weight:400;line-height:1.2}.btn-action[_ngcontent-%COMP%]:hover{transform:translateY(-3px);box-shadow:0 12px 30px #00000026}.btn-action[_ngcontent-%COMP%]:hover   .btn-icon[_ngcontent-%COMP%]{background:#ffffff40;transform:scale(1.05)}.btn-action[_ngcontent-%COMP%]:active{transform:translateY(-1px);box-shadow:0 6px 20px #0000001a}@media (max-width: 768px){.btn-action[_ngcontent-%COMP%]{min-width:auto;padding:.875rem 1.25rem;gap:.875rem}.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{width:36px;height:36px}.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.125rem}.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{font-size:.85rem}.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{font-size:.7rem}}.btn-outline[_ngcontent-%COMP%]{background:#fff;color:#231815;border:2px solid rgba(184,166,118,.2);box-shadow:0 2px 8px #0000000d}.btn-outline[_ngcontent-%COMP%]:hover{background:#b8a6760d;border-color:#b8a67666;color:#9b8a5a}.btn-outline.btn-action[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#fffffff2);border:2px solid rgba(184,166,118,.25);color:#231815;box-shadow:0 4px 16px #b8a6761a}.btn-outline.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67626,#b8a67614);border:1px solid rgba(184,166,118,.2)}.btn-outline.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#9b8a5a}.btn-outline.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{color:#231815}.btn-outline.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{color:#23181599}.btn-outline.btn-action[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#b8a67614,#b8a6760d);border-color:#b8a67666;box-shadow:0 8px 25px #b8a67633}.btn-outline.btn-action[_ngcontent-%COMP%]:hover   .btn-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67640,#b8a67626);border-color:#b8a6764d}.btn-outline.btn-action[_ngcontent-%COMP%]:hover   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{color:#9b8a5a}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;box-shadow:0 4px 16px #b8a6764d}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#ae9b66,#9b8a5a);box-shadow:0 6px 20px #b8a67666}.btn-primary.btn-action[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;box-shadow:0 6px 20px #b8a6764d;border:2px solid rgba(174,155,102,.3)}.btn-primary.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{background:#fff3;border:1px solid rgba(255,255,255,.15)}.btn-primary.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .btn-primary.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%], .btn-primary.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{color:#fff}.btn-primary.btn-action[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#ae9b66,#9b8a5a);box-shadow:0 10px 30px #b8a67666;border-color:#9b8a5a66}.btn-primary.btn-action[_ngcontent-%COMP%]:hover   .btn-icon[_ngcontent-%COMP%]{background:#ffffff4d;border-color:#ffffff40}.btn-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,#23a415,#23a415e6);color:#fff;box-shadow:0 4px 16px #23a41540}.btn-success[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#23a415f2,#23a415d9);box-shadow:0 6px 20px #23a41559}.btn-success.btn-action[_ngcontent-%COMP%]{background:linear-gradient(135deg,#23a415,#23a415e6);color:#fff;box-shadow:0 6px 20px #23a41540;border:2px solid rgba(35,164,21,.3)}.btn-success.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{background:#fff3;border:1px solid rgba(255,255,255,.15)}.btn-success.btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .btn-success.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%], .btn-success.btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{color:#fff}.btn-success.btn-action[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#23a415f2,#23a415d9);box-shadow:0 10px 30px #23a41559;border-color:#23a41566}.btn-success.btn-action[_ngcontent-%COMP%]:hover   .btn-icon[_ngcontent-%COMP%]{background:#ffffff4d;border-color:#ffffff40}.section[_ngcontent-%COMP%]{margin-bottom:1.5rem}.section-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 1px 3px #ae9b661a;overflow:hidden}.section-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#231815;margin:0;padding:1.5rem 1.5rem 0}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1.5rem 1.5rem 0}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{padding:0;display:flex;align-items:center;gap:.5rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ae9b66;font-size:1.1rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;background:linear-gradient(135deg,#b8a6761a,#b8a6760d);border:1px solid rgba(184,166,118,.2);padding:.5rem .75rem;border-radius:20px;font-size:.875rem;color:#9b8a5a;font-weight:500;transition:all .2s ease}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem;color:#ae9b66}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%]{font-weight:600;color:#9b8a5a;min-width:1.2rem;text-align:center}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-label[_ngcontent-%COMP%]{color:#23181599;margin-left:.1rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#b8a67626,#b8a67614);border-color:#b8a6764d;transform:translateY(-1px);box-shadow:0 2px 8px #b8a67626}@media (max-width: 768px){.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.75rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]{align-self:flex-end}}.items-table[_ngcontent-%COMP%]{margin:1.5rem;border-radius:8px;overflow:hidden;border:1px solid #E5E3E1}.table-header[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr 1fr 1.5fr 1.5fr 1.5fr;background:#e6f0f3;font-weight:600;color:#231815}.table-header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{padding:1rem;border-right:1px solid #E5E3E1}.table-header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child{border-right:none}.table-body[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr 1fr 1.5fr 1.5fr 1.5fr;border-bottom:1px solid #E5E3E1;transition:background-color .2s ease}.table-body[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]:hover{background:#f8f7f4}.table-body[_ngcontent-%COMP%]   .table-row.row-even[_ngcontent-%COMP%]{background:#f8f7f480}.table-body[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]:last-child{border-bottom:none}.table-body[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{padding:1rem;border-right:1px solid #E5E3E1;display:flex;align-items:center}.table-body[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child{border-right:none}.calculation-table[_ngcontent-%COMP%]{margin:1.5rem;border-radius:8px;overflow:hidden;border:1px solid #E5E3E1}.calc-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem 1.5rem;border-bottom:1px solid #E5E3E1}.calc-row[_ngcontent-%COMP%]:last-child{border-bottom:none}.calc-row.total-row[_ngcontent-%COMP%]{background:#e6f0f3;font-weight:600;font-size:1.125rem}.calc-row.other-fee-row[_ngcontent-%COMP%]{background:#ffc1071a;border-left:3px solid #ffc107}.calc-row.other-fee-row[_ngcontent-%COMP%]   .calc-label[_ngcontent-%COMP%]{color:#856404;font-weight:500}.calc-row.other-fee-row[_ngcontent-%COMP%]   .calc-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffc107}.calc-row.other-fee-row[_ngcontent-%COMP%]   .calc-value[_ngcontent-%COMP%]{color:#856404;font-weight:600}.calc-row[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#231815;font-weight:500}.calc-row[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#231815;font-weight:600}.total-amount[_ngcontent-%COMP%]{color:#ae9b66;font-size:1.25rem}.notes-content[_ngcontent-%COMP%]{padding:1.5rem}.notes-content[_ngcontent-%COMP%]   .note-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;margin-bottom:1rem;padding:.75rem;background:#f8f7f4;border-radius:6px}.notes-content[_ngcontent-%COMP%]   .note-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.notes-content[_ngcontent-%COMP%]   .note-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#b8a676;font-size:1.125rem}.notes-content[_ngcontent-%COMP%]   .note-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#231815;font-weight:500}.dialog-loading[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:200px;padding:40px}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{font-size:16px;color:#b8a676;animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}.dialog-error[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:300px;padding:3rem;background:linear-gradient(135deg,#f1502f0d,#fff)}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:1.5rem;text-align:center}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;color:#f1502f}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .error-text[_ngcontent-%COMP%]{font-size:1rem;color:#f1502f;max-width:300px}.dialog-empty[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:300px;padding:3rem;background:linear-gradient(135deg,#fff,#f8f7f44d)}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:1rem;text-align:center}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;color:#23181599;opacity:.6}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:1.125rem;color:#23181599;font-weight:500}  .p-dialog .p-dialog-content{padding:0!important;overflow:hidden}.version-dialog-content[_ngcontent-%COMP%]{width:700px;max-height:70vh;overflow:hidden}@media (max-width: 768px){.version-dialog-content[_ngcontent-%COMP%]{width:95vw;max-height:80vh}}.version-summary[_ngcontent-%COMP%]{padding:20px 24px 16px;background:linear-gradient(135deg,#b8a676,#b8a676cc);border-bottom:1px solid rgba(174,155,102,.2);color:#fff}.version-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:12px}.version-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]{font-size:14px;color:#ffffffe6;background:#ffffff26;padding:4px 12px;border-radius:20px;border:1px solid rgba(255,255,255,.2)}.version-summary[_ngcontent-%COMP%]   .current-version-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.version-summary[_ngcontent-%COMP%]   .current-version-info[_ngcontent-%COMP%]   .current-label[_ngcontent-%COMP%]{font-size:14px;color:#ffffffe6}.version-summary[_ngcontent-%COMP%]   .current-version-info[_ngcontent-%COMP%]   .current-version[_ngcontent-%COMP%]{font-weight:600;color:#fff;background:#fff3;padding:2px 8px;border-radius:4px;font-size:14px}.version-list-container[_ngcontent-%COMP%]{max-height:50vh;overflow-y:auto;padding:8px 0}.version-list-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.version-list-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f8f7f4;border-radius:3px}.version-list-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#b8a676;border-radius:3px}.version-list-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#ae9b66}.version-items[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px 24px;border-bottom:1px solid #E5E3E1;cursor:pointer;transition:all .2s ease;position:relative}.version-items[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#b8a67614,#b8a6760d)}.version-items[_ngcontent-%COMP%]   .version-item.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67626,#b8a6761a);border-left:4px solid #AE9B66;padding-left:20px}.version-items[_ngcontent-%COMP%]   .version-item.active[_ngcontent-%COMP%]   .version-arrow[_ngcontent-%COMP%]{display:block}.version-items[_ngcontent-%COMP%]   .version-item.current[_ngcontent-%COMP%]   .version-current-badge[_ngcontent-%COMP%]{display:inline-flex}.version-items[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]{flex:1}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:12px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-title[_ngcontent-%COMP%]   .version-name[_ngcontent-%COMP%]{font-weight:600;color:#231815;font-size:16px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-title[_ngcontent-%COMP%]   .version-current-badge[_ngcontent-%COMP%]{display:none;align-items:center;gap:4px;background:linear-gradient(135deg,#ae9b66,#9b8a5a);color:#fff;padding:2px 8px;border-radius:12px;font-size:12px;font-weight:500;box-shadow:0 2px 4px #ae9b664d}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-title[_ngcontent-%COMP%]   .version-current-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;padding:4px 8px;border-radius:4px;font-size:12px;font-weight:500}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-pending[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffdd6426,#ffdd641a);color:#ffcc18;border:1px solid rgba(255,221,100,.2)}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-quoted[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67626,#b8a6761a);color:#9b8a5a}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-confirmed[_ngcontent-%COMP%]{background:#23a4151a;color:#23a415}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-sent[_ngcontent-%COMP%]{background:#b8a6761a;color:#ae9b66}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-approved[_ngcontent-%COMP%]{background:#23a4151a;color:#23a415}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-rejected[_ngcontent-%COMP%]{background:#f1502f1a;color:#f1502f}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status.status-expired[_ngcontent-%COMP%]{background:#2318151a;color:#23181599}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-meta[_ngcontent-%COMP%]{margin-bottom:12px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-meta[_ngcontent-%COMP%]   .version-date[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-size:14px;color:#23181599}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-meta[_ngcontent-%COMP%]   .version-date[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]{display:flex;flex-direction:column}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]   .amount-label[_ngcontent-%COMP%]{font-size:12px;color:#23181599;margin-bottom:2px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]   .amount-value[_ngcontent-%COMP%]{font-weight:600;color:#231815;font-size:14px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]   .version-items-count[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;font-size:12px;color:#23181599;background:#f8f7f4;padding:4px 8px;border-radius:4px}.version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]   .version-items-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.version-items[_ngcontent-%COMP%]   .version-arrow[_ngcontent-%COMP%]{display:none;margin-left:12px;color:#ae9b66}.version-items[_ngcontent-%COMP%]   .version-arrow[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px;animation:_ngcontent-%COMP%_bounce 1s ease-in-out infinite}.version-list[_ngcontent-%COMP%]{max-height:50vh;overflow-y:auto;padding:0;margin:0}.version-list[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]{padding:12px 16px;border-bottom:1px solid #E5E3E1;cursor:pointer;transition:all .3s ease;background:#fff;position:relative;box-sizing:border-box}.version-list[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]:hover{background:#b8a6760d;border-left:3px solid #B8A676;padding-left:13px;box-shadow:0 1px 3px #ae9b661a}.version-list[_ngcontent-%COMP%]   .version-item.selected[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a67614,#b8a67608);border-left:3px solid #AE9B66;padding-left:13px;box-shadow:0 2px 8px #ae9b6633}.version-list[_ngcontent-%COMP%]   .version-item.selected[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-number[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#9b8a5a;font-weight:600}.version-list[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:6px;gap:12px}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-number[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-weight:500;color:#231815;flex:1;overflow:hidden}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-number[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#b8a676;font-size:14px;flex-shrink:0}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-number[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;line-height:1.2}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]{flex-shrink:0}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:4px;font-size:11px;padding:3px 8px;border-radius:10px;white-space:nowrap}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-pending[_ngcontent-%COMP%]{background:#ffdd641a;color:#fdc600}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-quoted[_ngcontent-%COMP%]{background:#b8a6761a;color:#9b8a5a}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-confirmed[_ngcontent-%COMP%]{background:#23a4151a;color:#19770f}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-sent[_ngcontent-%COMP%]{background:#b8a67626;color:#9b8a5a}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-approved[_ngcontent-%COMP%]{background:#23a4151a;color:#23a415}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-rejected[_ngcontent-%COMP%]{background:#f1502f14;color:#de320f}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge.status-expired[_ngcontent-%COMP%]{background:#ffdd641a;color:#ca9e00}.version-list[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:4px;color:#23181599;font-size:12px;gap:12px}.version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-date[_ngcontent-%COMP%], .version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;white-space:nowrap}.version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-date[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px;flex-shrink:0;color:#b8a676}.version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-amount[_ngcontent-%COMP%]{font-weight:500;color:#231815}@media (max-width: 768px){.version-list[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:2px}}.version-list[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .items-count[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;color:#23181599;font-size:11px}.version-list[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .items-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px;flex-shrink:0}.version-list[_ngcontent-%COMP%]   .version-other-fee[_ngcontent-%COMP%]   .other-fee-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;color:#856404;font-size:11px;font-weight:500;background:#ffc1071a;padding:2px 6px;border-radius:4px;margin-top:4px}.version-list[_ngcontent-%COMP%]   .version-other-fee[_ngcontent-%COMP%]   .other-fee-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px;color:#ffc107;flex-shrink:0}.version-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.version-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f8f7f4;border-radius:3px}.version-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#b8a676;border-radius:3px}.version-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#ae9b66}@keyframes _ngcontent-%COMP%_bounce{0%,20%,50%,80%,to{transform:translate(0)}40%{transform:translate(-3px)}60%{transform:translate(-1px)}}@media (max-width: 768px){.version-dialog-content[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .version-item[_ngcontent-%COMP%]{padding:12px 16px}.version-dialog-content[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .version-item.active[_ngcontent-%COMP%]{padding-left:12px}.version-dialog-content[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-header[_ngcontent-%COMP%]   .version-title[_ngcontent-%COMP%]   .version-name[_ngcontent-%COMP%]{font-size:14px}.version-dialog-content[_ngcontent-%COMP%]   .version-items[_ngcontent-%COMP%]   .version-content[_ngcontent-%COMP%]   .version-details[_ngcontent-%COMP%]   .version-summary-info[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:8px}}@media print{.wrapper[_ngcontent-%COMP%]{background:#fff!important;-webkit-print-color-adjust:exact;print-color-adjust:exact}.header-actions[_ngcontent-%COMP%], .loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%], .btn[_ngcontent-%COMP%]{display:none!important}.content[_ngcontent-%COMP%]{max-width:none;margin:0;padding:0}.items-table[_ngcontent-%COMP%], .calculation-table[_ngcontent-%COMP%]{page-break-inside:avoid}.page-title[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:1rem}.section-title[_ngcontent-%COMP%]{font-size:1.2rem}.section-card[_ngcontent-%COMP%]{box-shadow:none!important;border:1px solid #ddd;background:#fff!important}*[_ngcontent-%COMP%]{color:#000!important;background:transparent!important}.table-header[_ngcontent-%COMP%]{background:#f5f5f5!important;color:#000!important}.total-row[_ngcontent-%COMP%]{border-top:2px solid black!important;font-weight:700!important}}@media (max-width: 912px){.quotation-header[_ngcontent-%COMP%]{flex-direction:column;gap:1rem;align-items:stretch}.header-actions[_ngcontent-%COMP%]{justify-content:center}.table-header[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.table-header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{border-right:none;border-bottom:1px solid #E5E3E1}.table-header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child, .table-row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child{border-bottom:none}.table-header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:before, .table-row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:before{content:attr(data-label) \\\": \\\";font-weight:600;color:#23181599;margin-right:.5rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]{font-size:.8rem;padding:.4rem .6rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%]{min-width:1rem;font-size:.85rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-label[_ngcontent-%COMP%]{font-size:.75rem}}@media (max-width: 640px){.content[_ngcontent-%COMP%]{padding:1rem}.page-title[_ngcontent-%COMP%]{font-size:1.5rem}.btn[_ngcontent-%COMP%]{min-width:100px;font-size:.75rem}.quotation-info[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.5rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]{padding:1rem 1rem 0}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:1.1rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]{font-size:.75rem;padding:.35rem .5rem;border-radius:16px}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.7rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%]{font-size:.8rem;min-width:.8rem}.section-header[_ngcontent-%COMP%]   .section-title-wrapper[_ngcontent-%COMP%]   .items-count-badge[_ngcontent-%COMP%]   .count-label[_ngcontent-%COMP%]{font-size:.7rem}}.dialog-loading[_ngcontent-%COMP%], .dialog-error[_ngcontent-%COMP%], .dialog-empty[_ngcontent-%COMP%]{padding:40px 20px;text-align:center;color:#23181599}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px;color:#b8a676;animation:_ngcontent-%COMP%_spin 1s linear infinite}.dialog-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;color:#231815}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:16px}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:32px;color:#f1502f}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .error-text[_ngcontent-%COMP%]{color:#231815;font-size:14px;margin-bottom:8px}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:8px 16px;font-size:14px;border-radius:6px;border:none;cursor:pointer;transition:background-color .3s ease}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);color:#fff;box-shadow:0 2px 8px #ae9b6633}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);box-shadow:0 4px 12px #ae9b664d}.dialog-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;margin-right:6px}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:32px;color:#23181599}.dialog-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#23181599;font-size:14px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 1024px){.quotation-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{padding:1.5rem}.quotation-header[_ngcontent-%COMP%]   .quotation-details-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1rem}.quotation-header[_ngcontent-%COMP%]   .header-actions-container[_ngcontent-%COMP%]{min-width:280px;padding:1.25rem}.quotation-header[_ngcontent-%COMP%]   .header-actions-container[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%]{font-size:.9rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{gap:.625rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.625rem 1rem;font-size:.8125rem;min-width:100px}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]{min-width:180px;padding:.875rem 1.25rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{width:36px;height:36px}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.125rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{font-size:.85rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{font-size:.7rem}}@media (max-width: 480px){.quotation-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{padding:1rem}.quotation-header[_ngcontent-%COMP%]   .quotation-number-section[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:1.25rem}.quotation-header[_ngcontent-%COMP%]   .quotation-number-section[_ngcontent-%COMP%]   .number-container[_ngcontent-%COMP%], .quotation-header[_ngcontent-%COMP%]   .quotation-number-section[_ngcontent-%COMP%]   .status-container[_ngcontent-%COMP%]{width:100%;align-items:center;text-align:center}.quotation-header[_ngcontent-%COMP%]   .quotation-number-section[_ngcontent-%COMP%]   .number-container[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%], .quotation-header[_ngcontent-%COMP%]   .quotation-number-section[_ngcontent-%COMP%]   .status-container[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{justify-content:center;margin-bottom:.5rem}.quotation-header[_ngcontent-%COMP%]   .number-badge[_ngcontent-%COMP%]{padding:.625rem 1rem;font-size:1rem}.quotation-header[_ngcontent-%COMP%]   .number-badge[_ngcontent-%COMP%]   .number-text[_ngcontent-%COMP%]{font-size:1rem}.quotation-header[_ngcontent-%COMP%]   .header-actions-container[_ngcontent-%COMP%]{min-width:auto;width:100%;padding:1rem}.quotation-header[_ngcontent-%COMP%]   .header-actions-container[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%]{font-size:.85rem;text-align:center}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{width:100%}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{flex:1;min-width:auto}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]{min-width:auto;width:100%;padding:.75rem 1rem;gap:.75rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]{width:32px;height:32px}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-label[_ngcontent-%COMP%]{font-size:.8rem}.quotation-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .btn-description[_ngcontent-%COMP%]{font-size:.65rem}}@keyframes _ngcontent-%COMP%_slideInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_fadeInScale{0%{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}.quotation-header[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInScale .6s cubic-bezier(.4,0,.2,1)}.detail-item[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInUp .6s cubic-bezier(.4,0,.2,1)}.detail-item[_ngcontent-%COMP%]:nth-child(1){animation-delay:.1s}.detail-item[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.btn[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInUp .6s cubic-bezier(.4,0,.2,1)}.btn[_ngcontent-%COMP%]:nth-child(1){animation-delay:.3s}.btn[_ngcontent-%COMP%]:nth-child(2){animation-delay:.4s}.btn[_ngcontent-%COMP%]:nth-child(3){animation-delay:.5s}\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["signal", "CommonModule", "DialogPopupComponent", "QUOTATION_TEMPLATE", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "QuotationComponent_div_3_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "loadQuotationHistory", "ɵɵadvance", "ɵɵtextInterpolate1", "error", "QuotationComponent_div_4_div_4_Template_button_click_11_listener", "_r3", "ɵɵtextInterpolate", "getVersionNumber", "selectedVersion", "ɵɵproperty", "getStatusClass", "ɵɵclassMap", "getStatusIcon", "getStatusText", "ɵɵpipeBind2", "getCreateDate", "CTotalAmount", "QuotationComponent_div_4_div_5_button_26_Template_button_click_0_listener", "_r5", "viewContract", "QuotationComponent_div_4_div_5_div_30_Template_button_click_5_listener", "_r6", "version_r8", "tblQuotationItems", "length", "ɵɵtextInterpolate2", "COtherName", "COtherPercent", "QuotationComponent_div_4_div_5_div_32_div_1_Template_div_click_0_listener", "_r7", "$implicit", "selectVersion", "ɵɵtemplate", "QuotationComponent_div_4_div_5_div_32_div_1_div_21_Template", "QuotationComponent_div_4_div_5_div_32_div_1_div_22_Template", "ɵɵclassProp", "CShowOther", "QuotationComponent_div_4_div_5_div_32_div_1_Template", "quotationVersions", "tmp_4_0", "i_r10", "item_r9", "CItemName", "CUnit", "CCount", "CUnitPrice", "CSubtotal", "CRemark", "QuotationComponent_div_4_div_5_div_34_div_8_Template", "QuotationComponent_div_4_div_5_div_34_div_30_Template", "tmp_3_0", "calculateOtherFee", "QuotationComponent_div_4_div_5_div_3_Template", "QuotationComponent_div_4_div_5_Template_button_click_10_listener", "_r4", "toggleVersionHistory", "QuotationComponent_div_4_div_5_Template_button_click_18_listener", "printPreview", "QuotationComponent_div_4_div_5_button_26_Template", "ɵɵtwoWayListener", "QuotationComponent_div_4_div_5_Template_app_dialog_popup_visibleChange_27_listener", "$event", "ɵɵtwoWayBindingSet", "showVersionHistory", "QuotationComponent_div_4_div_5_div_29_Template", "QuotationComponent_div_4_div_5_div_30_Template", "QuotationComponent_div_4_div_5_div_31_Template", "QuotationComponent_div_4_div_5_div_32_Template", "QuotationComponent_div_4_div_5_div_34_Template", "QuotationComponent_div_4_div_5_div_50_Template", "convertStatusFromAPI", "CQuotationStatus", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c0", "isLoading", "calculateSubtotal", "calculateTotalWithOther", "QuotationComponent_div_4_div_4_Template", "QuotationComponent_div_4_div_5_Template", "QuotationComponent", "constructor", "quotationService", "ngOnInit", "set", "apiQuotationGetQuotationHistoryPost$Json", "subscribe", "next", "response", "Entries", "console", "version", "onVersionHistoryClose", "status", "printWindow", "window", "open", "templateContent", "generatePrintTemplate", "document", "write", "close", "onload", "print", "template", "items", "subtotal", "otherFee", "totalAmount", "itemsHtml", "map", "item", "index", "toLocaleString", "join", "additionalFeeHtml", "replace", "Date", "toLocaleDateString", "viewVersionDetails", "detailsElement", "querySelector", "scrollIntoView", "behavior", "block", "compareVersion", "alert", "CversionNo", "reviseQuotation", "statusCode", "getQuotationNumber", "CQuotationVersionID", "CCreateDT", "reduce", "sum", "calculateTax", "Math", "round", "otherPercent", "_", "ɵɵdirectiveInject", "i1", "QuotationService", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "QuotationComponent_Template", "rf", "ctx", "QuotationComponent_div_2_Template", "QuotationComponent_div_3_Template", "QuotationComponent_div_4_Template", "i2", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "DatePipe", "styles", "changeDetection"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\quotation\\quotation.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\quotation\\quotation.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, OnInit, signal } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { QuotationService } from '../../../services/api/services/quotation.service';\r\nimport { GetQuotationVersionsListResponseBase } from '../../../services/api/models/get-quotation-versions-list-response-base';\r\nimport { GetQuotationVersions } from '../../../services/api/models/get-quotation-versions';\r\nimport { TblQuotationItem } from '../../../services/api/models/tbl-quotation-item';\r\nimport { DialogPopupComponent } from '../../components/dialog-popup/dialog-popup.component';\r\nimport { QUOTATION_TEMPLATE } from '../../../assets/template/quotation-template';\r\n\r\n@Component({\r\n  selector: 'app-quotation',\r\n  standalone: true,\r\n  imports: [CommonModule, DialogPopupComponent],\r\n  templateUrl: './quotation.component.html',\r\n  styleUrl: './quotation.component.scss',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class QuotationComponent implements OnInit {\r\n  quotationVersions = signal<GetQuotationVersions[]>([]);\r\n  selectedVersion = signal<GetQuotationVersions | null>(null);\r\n  showVersionHistory = signal(false);\r\n  isLoading = signal(false);\r\n  error = signal<string | null>(null);\r\n\r\n  constructor(private quotationService: QuotationService) { }\r\n\r\n  ngOnInit() {\r\n    this.loadQuotationHistory();\r\n  }\r\n\r\n  loadQuotationHistory() {\r\n    this.isLoading.set(true);\r\n    this.error.set(null);\r\n\r\n    this.quotationService.apiQuotationGetQuotationHistoryPost$Json()\r\n      .subscribe({\r\n        next: (response: GetQuotationVersionsListResponseBase) => {\r\n          if (response.Entries) {\r\n            this.quotationVersions.set(response.Entries);\r\n            if (response.Entries.length > 0) {\r\n              this.selectedVersion.set(response.Entries[0]); // 選擇第一個版本\r\n            }\r\n          } else {\r\n            this.quotationVersions.set([]); // 確保設置為空陣列\r\n          }\r\n          this.isLoading.set(false);\r\n        },\r\n        error: (error) => {\r\n          console.error('載入報價歷程失敗:', error);\r\n          this.error.set('載入報價歷程失敗，請稍後再試。');\r\n          this.quotationVersions.set([]); // 確保錯誤時清空資料\r\n          this.selectedVersion.set(null);\r\n          this.isLoading.set(false);\r\n        }\r\n      });\r\n  }\r\n  selectVersion(version: GetQuotationVersions) {\r\n    this.selectedVersion.set(version);\r\n    this.showVersionHistory.set(false); // 選擇後關閉 dialog\r\n  }\r\n\r\n  toggleVersionHistory() {\r\n    this.showVersionHistory.set(!this.showVersionHistory());\r\n  }\r\n\r\n  onVersionHistoryClose() {\r\n    this.showVersionHistory.set(false);\r\n  }\r\n\r\n  // 輔助方法來獲取狀態樣式類別\r\n  getStatusClass(version: GetQuotationVersions): string {\r\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\r\n    switch (status) {\r\n      case 'pending': return 'status-pending';\r\n      case 'quoted': return 'status-quoted';\r\n      case 'confirmed': return 'status-confirmed';\r\n      case 'sent': return 'status-sent';\r\n      case 'approved': return 'status-approved';\r\n      case 'rejected': return 'status-rejected';\r\n      case 'expired': return 'status-expired';\r\n      default: return 'status-pending';\r\n    }\r\n  }\r\n\r\n  // 輔助方法來獲取狀態圖示\r\n  getStatusIcon(version: GetQuotationVersions): string {\r\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\r\n    switch (status) {\r\n      case 'pending': return 'icon-clock';\r\n      case 'quoted': return 'icon-file-text';\r\n      case 'confirmed': return 'icon-check-circle';\r\n      case 'sent': return 'icon-send';\r\n      case 'approved': return 'icon-check-circle-2';\r\n      case 'rejected': return 'icon-x-circle';\r\n      case 'expired': return 'icon-alert-triangle';\r\n      default: return 'icon-clock';\r\n    }\r\n  }\r\n\r\n  // 輔助方法來獲取狀態文字\r\n  getStatusText(version: GetQuotationVersions): string {\r\n    const status = this.convertStatusFromAPI(version.CQuotationStatus);\r\n    switch (status) {\r\n      case 'pending': return '待報價';\r\n      case 'quoted': return '已報價';\r\n      case 'confirmed': return '已簽回';\r\n      case 'sent': return '已發送';\r\n      case 'approved': return '已核准';\r\n      case 'rejected': return '已拒絕';\r\n      case 'expired': return '已過期';\r\n      default: return '待報價';\r\n    }\r\n  }\r\n\r\n  printPreview() {\r\n    // 創建新視窗顯示套印模板\r\n    const printWindow = window.open('', '_blank', 'width=800,height=600');\r\n    if (printWindow && this.selectedVersion()) {\r\n      const templateContent = this.generatePrintTemplate();\r\n      printWindow.document.write(templateContent);\r\n      printWindow.document.close();\r\n\r\n      // 等待內容載入後執行列印\r\n      printWindow.onload = () => {\r\n        printWindow.print();\r\n        printWindow.close();\r\n      };\r\n    }\r\n  }\r\n\r\n  private generatePrintTemplate(): string {\r\n    const version = this.selectedVersion();\r\n    if (!version) return '';\r\n\r\n    // 使用新的模板\r\n    let template = QUOTATION_TEMPLATE;\r\n\r\n    // 準備模板變數\r\n    const items = version.tblQuotationItems || [];\r\n    const subtotal = this.calculateSubtotal(items);\r\n    const otherFee = version.CShowOther ? this.calculateOtherFee(items, version.COtherPercent) : 0;\r\n    const totalAmount = this.calculateTotalWithOther(version);\r\n\r\n    // 生成項目HTML\r\n    const itemsHtml = items.map((item, index) => `\r\n      <tr>\r\n        <td class=\"text-center\">${index + 1}</td>\r\n        <td>${item.CItemName || ''}</td>\r\n        <td class=\"text-right\">NT$ ${(item.CUnitPrice || 0).toLocaleString()}</td>\r\n        <td class=\"text-center\">${item.CUnit || ''}</td>\r\n        <td class=\"text-center\">${item.CCount || 0}</td>\r\n        <td class=\"text-right\">NT$ ${(item.CSubtotal || 0).toLocaleString()}</td>\r\n        <td>${item.CRemark || ''}</td>\r\n      </tr>\r\n    `).join('');\r\n\r\n    // 生成額外費用HTML\r\n    const additionalFeeHtml = version.CShowOther && version.COtherPercent ?\r\n      `<div class=\"additional-fee\">${version.COtherName || '額外費用'} (${version.COtherPercent}%)：NT$ ${otherFee.toLocaleString()}</div>` : '';\r\n\r\n    // 替換模板變數\r\n    template = template\r\n      .replace(/{{buildCaseName}}/g, '建案名稱') // 這裡可以根據實際需求調整\r\n      .replace(/{{houseHold}}/g, '戶別資訊') // 這裡可以根據實際需求調整\r\n      .replace(/{{floor}}/g, '樓層') // 這裡可以根據實際需求調整\r\n      .replace(/{{printDate}}/g, new Date().toLocaleDateString('zh-TW'))\r\n      .replace(/{{itemsHtml}}/g, itemsHtml)\r\n      .replace(/{{subtotalAmount}}/g, `NT$ ${subtotal.toLocaleString()}`)\r\n      .replace(/{{additionalFeeHtml}}/g, additionalFeeHtml)\r\n      .replace(/{{totalAmount}}/g, `NT$ ${totalAmount.toLocaleString()}`)\r\n      .replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\r\n\r\n    return template;\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n  viewVersionDetails(version: GetQuotationVersions) {\r\n    this.selectedVersion.set(version);\r\n    // 可以添加額外的檢視邏輯，如滾動到詳情區域\r\n    const detailsElement = document.querySelector('.quotation-details');\r\n    if (detailsElement) {\r\n      detailsElement.scrollIntoView({ behavior: 'smooth', block: 'start' });\r\n    }\r\n  }\r\n\r\n  compareVersion(version: GetQuotationVersions) {\r\n    // TODO: 實作版本比較功能\r\n    alert(`比較版本 ${version.CversionNo} 與當前版本 ${this.selectedVersion()?.CversionNo} 的功能開發中...`);\r\n  }\r\n\r\n  reviseQuotation() {\r\n    // TODO: 實作修改報價功能\r\n    alert('修改報價功能開發中...');\r\n  }\r\n\r\n  viewContract() {\r\n    // TODO: 實作查看合約功能\r\n    alert('查看合約功能開發中...');\r\n  }\r\n\r\n  convertStatusFromAPI(statusCode?: number): 'pending' | 'quoted' | 'confirmed' | 'sent' | 'approved' | 'rejected' | 'expired' {\r\n    switch (statusCode) {\r\n      case 1: return 'pending';  // 待報價\r\n      case 2: return 'quoted';   // 已報價\r\n      case 3: return 'confirmed'; // 已簽回\r\n      default: return 'pending'; // 預設為待報價\r\n    }\r\n  }\r\n\r\n  // 輔助方法來獲取報價編號\r\n  getQuotationNumber(version: GetQuotationVersions): string {\r\n    return `Q${version.CQuotationVersionID}`;\r\n  }\r\n\r\n  // 輔助方法來獲取版本號\r\n  getVersionNumber(version: GetQuotationVersions): string {\r\n    return version.CversionNo || 'v1.0';\r\n  }\r\n\r\n  // 輔助方法來獲取建立日期\r\n  getCreateDate(version: GetQuotationVersions): Date {\r\n    return version.CCreateDT ? new Date(version.CCreateDT) : new Date();\r\n  }\r\n\r\n  // 輔助方法來計算小計\r\n  calculateSubtotal(items?: TblQuotationItem[] | null): number {\r\n    if (!items) return 0;\r\n    return items.reduce((sum, item) => sum + (item.CSubtotal || 0), 0);\r\n  }\r\n\r\n  // 輔助方法來計算稅額\r\n  calculateTax(items?: TblQuotationItem[] | null): number {\r\n    const subtotal = this.calculateSubtotal(items);\r\n    return Math.round(subtotal * 0.1); // 10% 稅率\r\n  }\r\n\r\n  // 輔助方法來計算額外費用\r\n  calculateOtherFee(items?: TblQuotationItem[] | null, otherPercent?: number): number {\r\n    if (!otherPercent || otherPercent <= 0) return 0;\r\n    const subtotal = this.calculateSubtotal(items);\r\n    return Math.round(subtotal * (otherPercent / 100));\r\n  }\r\n\r\n  // 輔助方法來計算含額外費用的總計\r\n  calculateTotalWithOther(version: GetQuotationVersions): number {\r\n    const subtotal = this.calculateSubtotal(version.tblQuotationItems);\r\n    const otherFee = version.CShowOther ? this.calculateOtherFee(version.tblQuotationItems, version.COtherPercent) : 0;\r\n    return subtotal + otherFee;\r\n  }\r\n\r\n}\r\n", "<div class=\"wrapper\">\r\n  <div class=\"content\">\r\n\r\n    <!-- 載入狀態 -->\r\n    <div *ngIf=\"isLoading()\" class=\"loading-container\">\r\n      <div class=\"loading-spinner\">載入中...</div>\r\n    </div>\r\n\r\n    <!-- 錯誤狀態 -->\r\n    <div *ngIf=\"error()\" class=\"error-container\">\r\n      <div class=\"error-message\">\r\n        <i class=\"icon-alert\"></i>\r\n        {{ error() }}\r\n      </div>\r\n      <button class=\"btn btn-primary\" (click)=\"loadQuotationHistory()\">重新載入</button>\r\n    </div>\r\n\r\n    <!-- 主要內容 - 只有在沒有載入且沒有錯誤時顯示 -->\r\n    <div *ngIf=\"!isLoading() && !error()\">\r\n\r\n      <!-- 頁面標題 -->\r\n      <div class=\"flex justify-center mb-6\">\r\n        <div class=\"page-title\">報價單管理</div>\r\n      </div>\r\n\r\n      <!-- 無報價單時的空狀態 -->\r\n      <div *ngIf=\"quotationVersions().length === 0\" class=\"empty-state\">\r\n        <div class=\"empty-state-content\">\r\n          <div class=\"empty-icon\">\r\n            <i class=\"icon-file-text\"></i>\r\n          </div>\r\n          <h3 class=\"empty-title\">暫無報價單記錄</h3>\r\n          <p class=\"empty-description\">\r\n            目前系統中沒有任何報價單，<br>\r\n            請確認是否有報價單資料或嘗試重新載入\r\n          </p>\r\n          <div class=\"empty-actions\">\r\n            <button class=\"btn btn-primary\" (click)=\"loadQuotationHistory()\">\r\n              <i class=\"icon-refresh-cw\"></i>\r\n              重新載入資料\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 有報價單時顯示的內容 -->\r\n      <div *ngIf=\"quotationVersions().length > 0\">\r\n        <!-- 頁面標題與版本控制 -->\r\n        <div class=\"quotation-header\">\r\n          <div class=\"header-content\">\r\n            <div class=\"quotation-info\" *ngIf=\"selectedVersion()\">\r\n              <div class=\"quotation-main-section\">\r\n                <!-- 報價單編號區域 -->\r\n                <div class=\"quotation-number-section\">\r\n                  <div class=\"number-container\">\r\n                    <div class=\"field-title\">\r\n                      <i class=\"pi pi-file\"></i>\r\n                      <span>報價單號</span>\r\n                    </div>\r\n                    <div class=\"number-badge\">\r\n                      <span class=\"number-text\">{{ getVersionNumber(selectedVersion()!) }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"status-container\">\r\n                    <div class=\"field-title\">\r\n                      <i class=\"pi pi-info-circle\"></i>\r\n                      <span>狀態</span>\r\n                    </div>\r\n                    <div class=\"status-wrapper\">\r\n                      <span class=\"status-badge\" [ngClass]=\"getStatusClass(selectedVersion()!)\">\r\n                        <i [class]=\"getStatusIcon(selectedVersion()!)\"></i>\r\n                        {{ getStatusText(selectedVersion()!) }}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 詳細資訊區域 -->\r\n                <div class=\"quotation-details-grid\">\r\n                  <div class=\"detail-item\">\r\n                    <div class=\"detail-label\">\r\n                      <i class=\"pi pi-calendar\"></i>\r\n                      <span>建立日期</span>\r\n                    </div>\r\n                    <div class=\"detail-value\">\r\n                      {{ getCreateDate(selectedVersion()!) | date:'yyyy/MM/dd' }}\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"detail-item amount-highlight\">\r\n                    <div class=\"detail-label\">\r\n                      <i class=\"pi pi-dollar\"></i>\r\n                      <span>總金額</span>\r\n                    </div>\r\n                    <div class=\"detail-value amount-value\">\r\n                      NT$ {{ selectedVersion()!.CTotalAmount | number:'1.0-0' }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 操作按鈕區域 - 優化後的獨立區塊 -->\r\n            <div class=\"header-actions-container\">\r\n              <div class=\"actions-header\">\r\n                <h3 class=\"actions-title\">\r\n                  <i class=\"pi pi-cog\"></i>\r\n                  操作功能\r\n                </h3>\r\n              </div>\r\n              <div class=\"header-actions\">\r\n                <button class=\"btn btn-action btn-outline\" (click)=\"toggleVersionHistory()\">\r\n                  <div class=\"btn-icon\">\r\n                    <i class=\"pi pi-history\"></i>\r\n                  </div>\r\n                  <div class=\"btn-content\">\r\n                    <span class=\"btn-label\">版本歷程</span>\r\n                    <span class=\"btn-description\">查看所有版本</span>\r\n                  </div>\r\n                </button>\r\n                <button class=\"btn btn-action btn-primary\" (click)=\"printPreview()\">\r\n                  <div class=\"btn-icon\">\r\n                    <i class=\"pi pi-print\"></i>\r\n                  </div>\r\n                  <div class=\"btn-content\">\r\n                    <span class=\"btn-label\">預覽列印</span>\r\n                    <span class=\"btn-description\">列印報價單</span>\r\n                  </div>\r\n                </button>\r\n\r\n                <!-- 根據狀態顯示不同的操作按鈕 -->\r\n                <button class=\"btn btn-action btn-success\" (click)=\"viewContract()\"\r\n                  *ngIf=\"selectedVersion() && convertStatusFromAPI(selectedVersion()!.CQuotationStatus) === 'confirmed'\">\r\n                  <div class=\"btn-icon\">\r\n                    <i class=\"pi pi-file\"></i>\r\n                  </div>\r\n                  <div class=\"btn-content\">\r\n                    <span class=\"btn-label\">查看合約</span>\r\n                    <span class=\"btn-description\">檢視合約內容</span>\r\n                  </div>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 版本歷程 Dialog -->\r\n        <app-dialog-popup [(visible)]=\"showVersionHistory\"\r\n          [textData]=\"{header: '版本歷程', content: '', titleButtonLeft: '', titleButtonRight: ''}\">\r\n\r\n          <div class=\"version-dialog-content\">\r\n            <!-- 載入狀態 -->\r\n            <div *ngIf=\"isLoading()\" class=\"dialog-loading\">\r\n              <div class=\"loading-spinner\">\r\n                <i class=\"icon-loader\"></i>\r\n                <span>載入版本資料中...</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 錯誤狀態 -->\r\n            <div *ngIf=\"error()\" class=\"dialog-error\">\r\n              <div class=\"error-content\">\r\n                <i class=\"icon-alert-triangle\"></i>\r\n                <span class=\"error-text\">{{ error() }}</span>\r\n                <button class=\"btn btn-primary\" (click)=\"loadQuotationHistory()\">\r\n                  <i class=\"icon-refresh-cw\"></i>\r\n                  重新載入\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空狀態 -->\r\n            <div *ngIf=\"!isLoading() && !error() && quotationVersions().length === 0\" class=\"dialog-empty\">\r\n              <div class=\"empty-content\">\r\n                <i class=\"icon-file-text\"></i>\r\n                <span>暫無版本記錄</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 版本清單 -->\r\n            <div *ngIf=\"!isLoading() && !error() && quotationVersions().length > 0\" class=\"version-list\">\r\n              <div class=\"version-item\" *ngFor=\"let version of quotationVersions(); let i = index\"\r\n                [class.selected]=\"selectedVersion() === version\" (click)=\"selectVersion(version)\">\r\n                <div class=\"version-header\">\r\n                  <div class=\"version-number\">\r\n                    <i class=\"icon-file-text\"></i>\r\n                    <span>{{ getVersionNumber(version) }}</span>\r\n                  </div>\r\n                  <div class=\"version-status\">\r\n                    <span class=\"status-badge\" [ngClass]=\"getStatusClass(version)\">\r\n                      <i [class]=\"getStatusIcon(version)\"></i>\r\n                      {{ getStatusText(version) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"version-details\">\r\n                  <div class=\"version-date\">\r\n                    <i class=\"icon-calendar\"></i>\r\n                    <span>{{ getCreateDate(version) | date:'yyyy/MM/dd HH:mm' }}</span>\r\n                  </div>\r\n                  <div class=\"version-amount\">\r\n                    <i class=\"icon-dollar-sign\"></i>\r\n                    <span>NT$ {{ version.CTotalAmount | number:'1.0-0' }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"version-items\" *ngIf=\"version.tblQuotationItems\">\r\n                  <span class=\"items-count\">\r\n                    <i class=\"icon-list\"></i>\r\n                    {{ version.tblQuotationItems.length }} 個項目\r\n                  </span>\r\n                </div>\r\n                <!-- 額外費用資訊 -->\r\n                <div class=\"version-other-fee\" *ngIf=\"version.CShowOther && version.COtherPercent\">\r\n                  <span class=\"other-fee-info\">\r\n                    <i class=\"icon-plus-circle\"></i>\r\n                    {{ version.COtherName || '額外費用' }} ({{ version.COtherPercent }}%)\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </app-dialog-popup>\r\n\r\n        <!-- 主要內容區域 -->\r\n        <div class=\"quotation-content\">\r\n          <div class=\"quotation-details\" *ngIf=\"selectedVersion()\">\r\n\r\n            <!-- 報價項目 -->\r\n            <div class=\"section items-section\">\r\n              <div class=\"section-card\">\r\n                <div class=\"section-header\">\r\n                  <div class=\"section-title-wrapper\">\r\n                    <h2 class=\"section-title\">\r\n                      <i class=\"icon-list\"></i>\r\n                      報價項目\r\n                    </h2>\r\n                    <div class=\"items-count-badge\" *ngIf=\"selectedVersion()?.tblQuotationItems\">\r\n                      <i class=\"icon-hash\"></i>\r\n                      <span class=\"count-text\">{{ selectedVersion()?.tblQuotationItems?.length || 0 }}</span>\r\n                      <span class=\"count-label\">個項目</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"items-table\">\r\n                  <div class=\"table-header\">\r\n                    <div class=\"col-item\">\r\n                      <i class=\"icon-package\"></i>\r\n                      項目名稱\r\n                    </div>\r\n                    <div class=\"col-unit\">\r\n                      <i class=\"icon-tag\"></i>\r\n                      單位\r\n                    </div>\r\n                    <div class=\"col-qty\">\r\n                      <i class=\"icon-hash\"></i>\r\n                      數量\r\n                    </div>\r\n                    <div class=\"col-price\">\r\n                      <i class=\"icon-dollar-sign\"></i>\r\n                      單價\r\n                    </div>\r\n                    <div class=\"col-total\">\r\n                      <i class=\"icon-calculator\"></i>\r\n                      小計\r\n                    </div>\r\n                    <div class=\"col-remark\">\r\n                      <i class=\"icon-message-square\"></i>\r\n                      備註\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"table-body\">\r\n                    <div class=\"table-row\" *ngFor=\"let item of selectedVersion()?.tblQuotationItems; let i = index\"\r\n                      [class.row-even]=\"i % 2 === 0\">\r\n                      <div class=\"col-item\">\r\n                        <div class=\"item-info\">\r\n                          <span class=\"item-name\">{{ item.CItemName }}</span>\r\n                        </div>\r\n                      </div>\r\n                      <div class=\"col-unit\">\r\n                        <span class=\"unit-value\">{{ item.CUnit || '-' }}</span>\r\n                      </div>\r\n                      <div class=\"col-qty\">\r\n                        <span class=\"qty-value\">{{ item.CCount }}</span>\r\n                      </div>\r\n                      <div class=\"col-price\">\r\n                        <span class=\"price-value\">NT$ {{ item.CUnitPrice | number:'1.0-0' }}</span>\r\n                      </div>\r\n                      <div class=\"col-total\">\r\n                        <span class=\"total-value\">NT$ {{ item.CSubtotal | number:'1.0-0' }}</span>\r\n                      </div>\r\n                      <div class=\"col-remark\">\r\n                        <span class=\"remark-value\">{{ item.CRemark || '-' }}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 金額計算 -->\r\n          <div class=\"section calculation-section\">\r\n            <div class=\"section-card\">\r\n              <div class=\"section-header\">\r\n                <h2 class=\"section-title\">\r\n                  <i class=\"icon-calculator\"></i>\r\n                  金額明細\r\n                </h2>\r\n              </div>\r\n              <div class=\"calculation-table\">\r\n                <!-- 小計 -->\r\n                <div class=\"calc-row subtotal-row\">\r\n                  <div class=\"calc-label\">\r\n                    <i class=\"icon-plus\"></i>\r\n                    <span>小計</span>\r\n                  </div>\r\n                  <div class=\"calc-value\">\r\n                    NT$ {{ calculateSubtotal(selectedVersion()!.tblQuotationItems) | number:'1.0-0' }}\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 額外費用區域 (自定義) -->\r\n                <div class=\"calc-row other-fee-row\"\r\n                  *ngIf=\"selectedVersion()!.CShowOther && selectedVersion()!.COtherPercent\">\r\n                  <div class=\"calc-label\">\r\n                    <i class=\"icon-plus-circle\"></i>\r\n                    <span>{{ selectedVersion()!.COtherName || '額外費用' }} ({{ selectedVersion()!.COtherPercent }}%)</span>\r\n                  </div>\r\n                  <div class=\"calc-value\">\r\n                    NT$ {{ calculateOtherFee(selectedVersion()!.tblQuotationItems, selectedVersion()!.COtherPercent) |\r\n                    number:'1.0-0' }}\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 總計 -->\r\n                <div class=\"calc-row total-row\">\r\n                  <div class=\"calc-label\">\r\n                    <i class=\"icon-dollar-sign\"></i>\r\n                    <span>總計</span>\r\n                  </div>\r\n                  <div class=\"calc-value total-amount\">\r\n                    NT$ {{ calculateTotalWithOther(selectedVersion()!) | number:'1.0-0' }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 備註 -->\r\n          <div class=\"section notes-section\">\r\n            <div class=\"section-card\">\r\n              <div class=\"section-header\">\r\n                <h2 class=\"section-title\">\r\n                  <i class=\"icon-info\"></i>\r\n                  重要備註\r\n                </h2>\r\n              </div>\r\n              <div class=\"notes-content\">\r\n                <div class=\"note-category\">\r\n                  <h4 class=\"note-category-title\">\r\n                    <i class=\"icon-clock\"></i>\r\n                    效期與時間\r\n                  </h4>\r\n                  <div class=\"note-item\">\r\n                    <span class=\"note-text\">報價單有效期限為30天</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"note-category\">\r\n                  <h4 class=\"note-category-title\">\r\n                    <i class=\"icon-dollar-sign\"></i>\r\n                    價格說明\r\n                  </h4>\r\n                  <div class=\"note-item\">\r\n                    <span class=\"note-text\">價格含稅</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"note-category\">\r\n                  <h4 class=\"note-category-title\">\r\n                    <i class=\"icon-credit-card\"></i>\r\n                    付款條件\r\n                  </h4>\r\n                  <div class=\"note-item\">\r\n                    <span class=\"note-text\">付款方式：交貨後30天內付款</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"note-category\">\r\n                  <h4 class=\"note-category-title\">\r\n                    <i class=\"icon-help-circle\"></i>\r\n                    聯繫方式\r\n                  </h4>\r\n                  <div class=\"note-item\">\r\n                    <span class=\"note-text\">如有疑問請洽詢業務人員</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </div> <!-- 關閉主要內容區塊 -->\r\n\r\n  </div> <!-- 關閉有報價單時顯示的內容 -->\r\n</div>"], "mappings": "AAAA,SAAqDA,MAAM,QAAQ,eAAe;AAClF,SAASC,YAAY,QAAQ,iBAAiB;AAK9C,SAASC,oBAAoB,QAAQ,sDAAsD;AAC3F,SAASC,kBAAkB,QAAQ,6CAA6C;;;;;;;;;;;;ICF1EC,EADF,CAAAC,cAAA,aAAmD,aACpB;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IACrCF,EADqC,CAAAG,YAAA,EAAM,EACrC;;;;;;IAIJH,EADF,CAAAC,cAAA,aAA6C,aAChB;IACzBD,EAAA,CAAAI,SAAA,WAA0B;IAC1BJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAAiE;IAAjCD,EAAA,CAAAK,UAAA,mBAAAC,0DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,oBAAA,EAAsB;IAAA,EAAC;IAACZ,EAAA,CAAAE,MAAA,+BAAI;IACvEF,EADuE,CAAAG,YAAA,EAAS,EAC1E;;;;IAHFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,KAAA,QACF;;;;;;IAeIf,EAFJ,CAAAC,cAAA,cAAkE,cAC/B,cACP;IACtBD,EAAA,CAAAI,SAAA,YAA8B;IAChCJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,YAA6B;IAC3BD,EAAA,CAAAE,MAAA,sFAAa;IAAAF,EAAA,CAAAI,SAAA,SAAI;IACjBJ,EAAA,CAAAE,MAAA,qHACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,eAA2B,kBACwC;IAAjCD,EAAA,CAAAK,UAAA,mBAAAW,iEAAA;MAAAhB,EAAA,CAAAO,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,oBAAA,EAAsB;IAAA,EAAC;IAC9DZ,EAAA,CAAAI,SAAA,aAA+B;IAC/BJ,EAAA,CAAAE,MAAA,8CACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;;IAYQH,EALR,CAAAC,cAAA,cAAsD,cAChB,cAEI,cACN,cACH;IACvBD,EAAA,CAAAI,SAAA,YAA0B;IAC1BJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACZF,EADY,CAAAG,YAAA,EAAO,EACb;IAEJH,EADF,CAAAC,cAAA,cAA0B,eACE;IAAAD,EAAA,CAAAE,MAAA,IAA0C;IAExEF,EAFwE,CAAAG,YAAA,EAAO,EACvE,EACF;IAEJH,EADF,CAAAC,cAAA,eAA8B,eACH;IACvBD,EAAA,CAAAI,SAAA,aAAiC;IACjCJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IACVF,EADU,CAAAG,YAAA,EAAO,EACX;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACgD;IACxED,EAAA,CAAAI,SAAA,SAAmD;IACnDJ,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAO,EACH,EACF,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAAoC,eACT,eACG;IACxBD,EAAA,CAAAI,SAAA,aAA8B;IAC9BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IACZF,EADY,CAAAG,YAAA,EAAO,EACb;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,eAA0C,eACd;IACxBD,EAAA,CAAAI,SAAA,aAA4B;IAC5BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IACXF,EADW,CAAAG,YAAA,EAAO,EACZ;IACNH,EAAA,CAAAC,cAAA,eAAuC;IACrCD,EAAA,CAAAE,MAAA,IACF;;IAIRF,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF;;;;IAvC8BH,EAAA,CAAAa,SAAA,IAA0C;IAA1Cb,EAAA,CAAAkB,iBAAA,CAAAT,MAAA,CAAAU,gBAAA,CAAAV,MAAA,CAAAW,eAAA,IAA0C;IASzCpB,EAAA,CAAAa,SAAA,GAA8C;IAA9Cb,EAAA,CAAAqB,UAAA,YAAAZ,MAAA,CAAAa,cAAA,CAAAb,MAAA,CAAAW,eAAA,IAA8C;IACpEpB,EAAA,CAAAa,SAAA,EAA2C;IAA3Cb,EAAA,CAAAuB,UAAA,CAAAd,MAAA,CAAAe,aAAA,CAAAf,MAAA,CAAAW,eAAA,IAA2C;IAC9CpB,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAgB,aAAA,CAAAhB,MAAA,CAAAW,eAAA,SACF;IAaApB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAd,EAAA,CAAA0B,WAAA,QAAAjB,MAAA,CAAAkB,aAAA,CAAAlB,MAAA,CAAAW,eAAA,wBACF;IAQEpB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,UAAAd,EAAA,CAAA0B,WAAA,SAAAjB,MAAA,CAAAW,eAAA,GAAAQ,YAAA,gBACF;;;;;;IAmCJ5B,EAAA,CAAAC,cAAA,iBACyG;IAD9DD,EAAA,CAAAK,UAAA,mBAAAwB,0EAAA;MAAA7B,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsB,YAAA,EAAc;IAAA,EAAC;IAEjE/B,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,YAA0B;IAC5BJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAyB,eACC;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAExCF,EAFwC,CAAAG,YAAA,EAAO,EACvC,EACC;;;;;IAaXH,EADF,CAAAC,cAAA,cAAgD,aACjB;IAC3BD,EAAA,CAAAI,SAAA,YAA2B;IAC3BJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAEpBF,EAFoB,CAAAG,YAAA,EAAO,EACnB,EACF;;;;;;IAIJH,EADF,CAAAC,cAAA,cAA0C,cACb;IACzBD,EAAA,CAAAI,SAAA,YAAmC;IACnCJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7CH,EAAA,CAAAC,cAAA,iBAAiE;IAAjCD,EAAA,CAAAK,UAAA,mBAAA2B,uEAAA;MAAAhC,EAAA,CAAAO,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,oBAAA,EAAsB;IAAA,EAAC;IAC9DZ,EAAA,CAAAI,SAAA,YAA+B;IAC/BJ,EAAA,CAAAE,MAAA,iCACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IANuBH,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAkB,iBAAA,CAAAT,MAAA,CAAAM,KAAA,GAAa;;;;;IAUxCf,EADF,CAAAC,cAAA,cAA+F,cAClE;IACzBD,EAAA,CAAAI,SAAA,YAA8B;IAC9BJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAEhBF,EAFgB,CAAAG,YAAA,EAAO,EACf,EACF;;;;;IA6BAH,EADF,CAAAC,cAAA,eAA6D,gBACjC;IACxBD,EAAA,CAAAI,SAAA,aAAyB;IACzBJ,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAoB,UAAA,CAAAC,iBAAA,CAAAC,MAAA,yBACF;;;;;IAIApC,EADF,CAAAC,cAAA,eAAmF,gBACpD;IAC3BD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAqC,kBAAA,MAAAH,UAAA,CAAAI,UAAA,sCAAAJ,UAAA,CAAAK,aAAA,QACF;;;;;;IAnCJvC,EAAA,CAAAC,cAAA,eACoF;IAAjCD,EAAA,CAAAK,UAAA,mBAAAmC,0EAAA;MAAA,MAAAN,UAAA,GAAAlC,EAAA,CAAAO,aAAA,CAAAkC,GAAA,EAAAC,SAAA;MAAA,MAAAjC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkC,aAAA,CAAAT,UAAA,CAAsB;IAAA,EAAC;IAE/ElC,EADF,CAAAC,cAAA,eAA4B,eACE;IAC1BD,EAAA,CAAAI,SAAA,YAA8B;IAC9BJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACvCF,EADuC,CAAAG,YAAA,EAAO,EACxC;IAEJH,EADF,CAAAC,cAAA,eAA4B,eACqC;IAC7DD,EAAA,CAAAI,SAAA,QAAwC;IACxCJ,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACF;IAEJH,EADF,CAAAC,cAAA,gBAA6B,gBACD;IACxBD,EAAA,CAAAI,SAAA,cAA6B;IAC7BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAsD;;IAC9DF,EAD8D,CAAAG,YAAA,EAAO,EAC/D;IACNH,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA+C;;IAEzDF,EAFyD,CAAAG,YAAA,EAAO,EACxD,EACF;IAQNH,EAPA,CAAA4C,UAAA,KAAAC,2DAAA,mBAA6D,KAAAC,2DAAA,mBAOsB;IAMrF9C,EAAA,CAAAG,YAAA,EAAM;;;;;IApCJH,EAAA,CAAA+C,WAAA,aAAAtC,MAAA,CAAAW,eAAA,OAAAc,UAAA,CAAgD;IAItClC,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAkB,iBAAA,CAAAT,MAAA,CAAAU,gBAAA,CAAAe,UAAA,EAA+B;IAGVlC,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAqB,UAAA,YAAAZ,MAAA,CAAAa,cAAA,CAAAY,UAAA,EAAmC;IACzDlC,EAAA,CAAAa,SAAA,EAAgC;IAAhCb,EAAA,CAAAuB,UAAA,CAAAd,MAAA,CAAAe,aAAA,CAAAU,UAAA,EAAgC;IACnClC,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAgB,aAAA,CAAAS,UAAA,OACF;IAMMlC,EAAA,CAAAa,SAAA,GAAsD;IAAtDb,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAA0B,WAAA,SAAAjB,MAAA,CAAAkB,aAAA,CAAAO,UAAA,uBAAsD;IAItDlC,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,kBAAA,SAAAd,EAAA,CAAA0B,WAAA,SAAAQ,UAAA,CAAAN,YAAA,eAA+C;IAG7B5B,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAqB,UAAA,SAAAa,UAAA,CAAAC,iBAAA,CAA+B;IAO3BnC,EAAA,CAAAa,SAAA,EAAiD;IAAjDb,EAAA,CAAAqB,UAAA,SAAAa,UAAA,CAAAc,UAAA,IAAAd,UAAA,CAAAK,aAAA,CAAiD;;;;;IAhCrFvC,EAAA,CAAAC,cAAA,eAA6F;IAC3FD,EAAA,CAAA4C,UAAA,IAAAK,oDAAA,qBACoF;IAqCtFjD,EAAA,CAAAG,YAAA,EAAM;;;;IAtC0CH,EAAA,CAAAa,SAAA,EAAwB;IAAxBb,EAAA,CAAAqB,UAAA,YAAAZ,MAAA,CAAAyC,iBAAA,GAAwB;;;;;IAuDhElD,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAI,SAAA,aAAyB;IACzBJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvFH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAC/BF,EAD+B,CAAAG,YAAA,EAAO,EAChC;;;;;IAFqBH,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAkB,iBAAA,GAAAiC,OAAA,GAAA1C,MAAA,CAAAW,eAAA,qBAAA+B,OAAA,CAAAhB,iBAAA,kBAAAgB,OAAA,CAAAhB,iBAAA,CAAAC,MAAA,OAAuD;;;;;IAqC5EpC,EAJN,CAAAC,cAAA,eACiC,eACT,eACG,gBACG;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAEhDF,EAFgD,CAAAG,YAAA,EAAO,EAC/C,EACF;IAEJH,EADF,CAAAC,cAAA,eAAsB,gBACK;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAClDF,EADkD,CAAAG,YAAA,EAAO,EACnD;IAEJH,EADF,CAAAC,cAAA,eAAqB,gBACK;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;IAEJH,EADF,CAAAC,cAAA,gBAAuB,iBACK;IAAAD,EAAA,CAAAE,MAAA,IAA0C;;IACtEF,EADsE,CAAAG,YAAA,EAAO,EACvE;IAEJH,EADF,CAAAC,cAAA,gBAAuB,iBACK;IAAAD,EAAA,CAAAE,MAAA,IAAyC;;IACrEF,EADqE,CAAAG,YAAA,EAAO,EACtE;IAEJH,EADF,CAAAC,cAAA,gBAAwB,iBACK;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAExDF,EAFwD,CAAAG,YAAA,EAAO,EACvD,EACF;;;;;IArBJH,EAAA,CAAA+C,WAAA,aAAAK,KAAA,WAA8B;IAGFpD,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAkB,iBAAA,CAAAmC,OAAA,CAAAC,SAAA,CAAoB;IAIrBtD,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAkB,iBAAA,CAAAmC,OAAA,CAAAE,KAAA,QAAuB;IAGxBvD,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAkB,iBAAA,CAAAmC,OAAA,CAAAG,MAAA,CAAiB;IAGfxD,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAc,kBAAA,SAAAd,EAAA,CAAA0B,WAAA,QAAA2B,OAAA,CAAAI,UAAA,eAA0C;IAG1CzD,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAc,kBAAA,SAAAd,EAAA,CAAA0B,WAAA,SAAA2B,OAAA,CAAAK,SAAA,eAAyC;IAGxC1D,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAkB,iBAAA,CAAAmC,OAAA,CAAAM,OAAA,QAAyB;;;;;IA3DxD3D,EAPV,CAAAC,cAAA,eAAyD,eAGpB,cACP,cACI,eACS,aACP;IACxBD,EAAA,CAAAI,SAAA,aAAyB;IACzBJ,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAA4C,UAAA,IAAAgB,oDAAA,mBAA4E;IAMhF5D,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAyB,gBACG,gBACF;IACpBD,EAAA,CAAAI,SAAA,cAA4B;IAC5BJ,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAsB;IACpBD,EAAA,CAAAI,SAAA,cAAwB;IACxBJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAqB;IACnBD,EAAA,CAAAI,SAAA,cAAyB;IACzBJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAuB;IACrBD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAuB;IACrBD,EAAA,CAAAI,SAAA,aAA+B;IAC/BJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAI,SAAA,cAAmC;IACnCJ,EAAA,CAAAE,MAAA,sBACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAA4C,UAAA,KAAAiB,qDAAA,qBACiC;IA0B3C7D,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF;;;;;;IA9DoCH,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAqB,UAAA,UAAAyC,OAAA,GAAArD,MAAA,CAAAW,eAAA,qBAAA0C,OAAA,CAAA3B,iBAAA,CAA0C;IAmClCnC,EAAA,CAAAa,SAAA,IAAyC;IAAzCb,EAAA,CAAAqB,UAAA,aAAA8B,OAAA,GAAA1C,MAAA,CAAAW,eAAA,qBAAA+B,OAAA,CAAAhB,iBAAA,CAAyC;;;;;IAqDnFnC,EAFF,CAAAC,cAAA,eAC4E,cAClD;IACtBD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAuF;IAC/FF,EAD+F,CAAAG,YAAA,EAAO,EAChG;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,MAAA,GAEF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IANIH,EAAA,CAAAa,SAAA,GAAuF;IAAvFb,EAAA,CAAAqC,kBAAA,KAAA5B,MAAA,CAAAW,eAAA,GAAAkB,UAAA,sCAAA7B,MAAA,CAAAW,eAAA,GAAAmB,aAAA,OAAuF;IAG7FvC,EAAA,CAAAa,SAAA,GAEF;IAFEb,EAAA,CAAAc,kBAAA,UAAAd,EAAA,CAAA0B,WAAA,OAAAjB,MAAA,CAAAsD,iBAAA,CAAAtD,MAAA,CAAAW,eAAA,GAAAe,iBAAA,EAAA1B,MAAA,CAAAW,eAAA,GAAAmB,aAAA,iBAEF;;;;;;IAzRRvC,EAHJ,CAAAC,cAAA,UAA4C,cAEZ,cACA;IAC1BD,EAAA,CAAA4C,UAAA,IAAAoB,6CAAA,oBAAsD;IAsDlDhE,EAFJ,CAAAC,cAAA,cAAsC,cACR,aACA;IACxBD,EAAA,CAAAI,SAAA,YAAyB;IACzBJ,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAG,YAAA,EAAK,EACD;IAEJH,EADF,CAAAC,cAAA,cAA4B,kBACkD;IAAjCD,EAAA,CAAAK,UAAA,mBAAA4D,iEAAA;MAAAjE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0D,oBAAA,EAAsB;IAAA,EAAC;IACzEnE,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAI,SAAA,aAA6B;IAC/BJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACC;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAE,MAAA,4CAAM;IAExCF,EAFwC,CAAAG,YAAA,EAAO,EACvC,EACC;IACTH,EAAA,CAAAC,cAAA,kBAAoE;IAAzBD,EAAA,CAAAK,UAAA,mBAAA+D,iEAAA;MAAApE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4D,YAAA,EAAc;IAAA,EAAC;IACjErE,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAI,SAAA,aAA2B;IAC7BJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACC;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAEvCF,EAFuC,CAAAG,YAAA,EAAO,EACtC,EACC;IAGTH,EAAA,CAAA4C,UAAA,KAAA0B,iDAAA,qBACyG;IAYjHtE,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;IAGNH,EAAA,CAAAC,cAAA,4BACwF;IADtED,EAAA,CAAAuE,gBAAA,2BAAAC,mFAAAC,MAAA;MAAAzE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAA0E,kBAAA,CAAAjE,MAAA,CAAAkE,kBAAA,EAAAF,MAAA,MAAAhE,MAAA,CAAAkE,kBAAA,GAAAF,MAAA;MAAA,OAAAzE,EAAA,CAAAW,WAAA,CAAA8D,MAAA;IAAA,EAAgC;IAGhDzE,EAAA,CAAAC,cAAA,eAAoC;IA8BlCD,EA5BA,CAAA4C,UAAA,KAAAgC,8CAAA,kBAAgD,KAAAC,8CAAA,kBAQN,KAAAC,8CAAA,kBAYqD,KAAAC,8CAAA,kBAQF;IAyCjG/E,EADE,CAAAG,YAAA,EAAM,EACW;IAGnBH,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAA4C,UAAA,KAAAoC,8CAAA,mBAAyD;IA+EnDhF,EAHN,CAAAC,cAAA,eAAyC,eACb,eACI,cACA;IACxBD,EAAA,CAAAI,SAAA,aAA+B;IAC/BJ,EAAA,CAAAE,MAAA,kCACF;IACFF,EADE,CAAAG,YAAA,EAAK,EACD;IAIFH,EAHJ,CAAAC,cAAA,eAA+B,eAEM,eACT;IACtBD,EAAA,CAAAI,SAAA,aAAyB;IACzBJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IACVF,EADU,CAAAG,YAAA,EAAO,EACX;IACNH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAA4C,UAAA,KAAAqC,8CAAA,kBAC4E;IAa1EjF,EADF,CAAAC,cAAA,eAAgC,eACN;IACtBD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IACVF,EADU,CAAAG,YAAA,EAAO,EACX;IACNH,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAE,MAAA,IACF;;IAIRF,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF;IAMAH,EAHN,CAAAC,cAAA,eAAmC,eACP,eACI,cACA;IACxBD,EAAA,CAAAI,SAAA,aAAyB;IACzBJ,EAAA,CAAAE,MAAA,kCACF;IACFF,EADE,CAAAG,YAAA,EAAK,EACD;IAGFH,EAFJ,CAAAC,cAAA,eAA2B,eACE,cACO;IAC9BD,EAAA,CAAAI,SAAA,aAA0B;IAC1BJ,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAuB,gBACG;IAAAD,EAAA,CAAAE,MAAA,gEAAW;IAEvCF,EAFuC,CAAAG,YAAA,EAAO,EACtC,EACF;IAGJH,EADF,CAAAC,cAAA,eAA2B,cACO;IAC9BD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAuB,gBACG;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAEhCF,EAFgC,CAAAG,YAAA,EAAO,EAC/B,EACF;IAGJH,EADF,CAAAC,cAAA,eAA2B,cACO;IAC9BD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAuB,gBACG;IAAAD,EAAA,CAAAE,MAAA,kFAAc;IAE1CF,EAF0C,CAAAG,YAAA,EAAO,EACzC,EACF;IAGJH,EADF,CAAAC,cAAA,eAA2B,cACO;IAC9BD,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAuB,gBACG;IAAAD,EAAA,CAAAE,MAAA,0EAAW;IAOjDF,EAPiD,CAAAG,YAAA,EAAO,EACtC,EACF,EACF,EACF,EACF,EACF,EACF;;;;IA9V6BH,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAW,eAAA,GAAuB;IAiF7CpB,EAAA,CAAAa,SAAA,IAAoG;IAApGb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAW,eAAA,MAAAX,MAAA,CAAAyE,oBAAA,CAAAzE,MAAA,CAAAW,eAAA,GAAA+D,gBAAA,kBAAoG;IAe7FnF,EAAA,CAAAa,SAAA,EAAgC;IAAhCb,EAAA,CAAAoF,gBAAA,YAAA3E,MAAA,CAAAkE,kBAAA,CAAgC;IAChD3E,EAAA,CAAAqB,UAAA,aAAArB,EAAA,CAAAqF,eAAA,KAAAC,GAAA,EAAqF;IAI7EtF,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAA8E,SAAA,GAAiB;IAQjBvF,EAAA,CAAAa,SAAA,EAAa;IAAbb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAM,KAAA,GAAa;IAYbf,EAAA,CAAAa,SAAA,EAAkE;IAAlEb,EAAA,CAAAqB,UAAA,UAAAZ,MAAA,CAAA8E,SAAA,OAAA9E,MAAA,CAAAM,KAAA,MAAAN,MAAA,CAAAyC,iBAAA,GAAAd,MAAA,OAAkE;IAQlEpC,EAAA,CAAAa,SAAA,EAAgE;IAAhEb,EAAA,CAAAqB,UAAA,UAAAZ,MAAA,CAAA8E,SAAA,OAAA9E,MAAA,CAAAM,KAAA,MAAAN,MAAA,CAAAyC,iBAAA,GAAAd,MAAA,KAAgE;IA6CxCpC,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAW,eAAA,GAAuB;IA4F7CpB,EAAA,CAAAa,SAAA,IACF;IADEb,EAAA,CAAAc,kBAAA,UAAAd,EAAA,CAAA0B,WAAA,SAAAjB,MAAA,CAAA+E,iBAAA,CAAA/E,MAAA,CAAAW,eAAA,GAAAe,iBAAA,iBACF;IAKCnC,EAAA,CAAAa,SAAA,GAAuE;IAAvEb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAW,eAAA,GAAA4B,UAAA,IAAAvC,MAAA,CAAAW,eAAA,GAAAmB,aAAA,CAAuE;IAkBtEvC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,UAAAd,EAAA,CAAA0B,WAAA,SAAAjB,MAAA,CAAAgF,uBAAA,CAAAhF,MAAA,CAAAW,eAAA,mBACF;;;;;IA/TVpB,EAJJ,CAAAC,cAAA,UAAsC,cAGE,cACZ;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAC/BF,EAD+B,CAAAG,YAAA,EAAM,EAC/B;IAuBNH,EApBA,CAAA4C,UAAA,IAAA8C,uCAAA,mBAAkE,IAAAC,uCAAA,mBAoBtB;IAoW9C3F,EAAA,CAAAG,YAAA,EAAM;;;;IAxXEH,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAyC,iBAAA,GAAAd,MAAA,OAAsC;IAoBtCpC,EAAA,CAAAa,SAAA,EAAoC;IAApCb,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAyC,iBAAA,GAAAd,MAAA,KAAoC;;;AD7BhD,OAAM,MAAOwD,kBAAkB;EAO7BC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IANpC,KAAA5C,iBAAiB,GAAGtD,MAAM,CAAyB,EAAE,CAAC;IACtD,KAAAwB,eAAe,GAAGxB,MAAM,CAA8B,IAAI,CAAC;IAC3D,KAAA+E,kBAAkB,GAAG/E,MAAM,CAAC,KAAK,CAAC;IAClC,KAAA2F,SAAS,GAAG3F,MAAM,CAAC,KAAK,CAAC;IACzB,KAAAmB,KAAK,GAAGnB,MAAM,CAAgB,IAAI,CAAC;EAEuB;EAE1DmG,QAAQA,CAAA;IACN,IAAI,CAACnF,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,CAAC2E,SAAS,CAACS,GAAG,CAAC,IAAI,CAAC;IACxB,IAAI,CAACjF,KAAK,CAACiF,GAAG,CAAC,IAAI,CAAC;IAEpB,IAAI,CAACF,gBAAgB,CAACG,wCAAwC,EAAE,CAC7DC,SAAS,CAAC;MACTC,IAAI,EAAGC,QAA8C,IAAI;QACvD,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACnD,iBAAiB,CAAC8C,GAAG,CAACI,QAAQ,CAACC,OAAO,CAAC;UAC5C,IAAID,QAAQ,CAACC,OAAO,CAACjE,MAAM,GAAG,CAAC,EAAE;YAC/B,IAAI,CAAChB,eAAe,CAAC4E,GAAG,CAACI,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;SAElD,MAAM;UACL,IAAI,CAACnD,iBAAiB,CAAC8C,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;;QAElC,IAAI,CAACT,SAAS,CAACS,GAAG,CAAC,KAAK,CAAC;MAC3B,CAAC;MACDjF,KAAK,EAAGA,KAAK,IAAI;QACfuF,OAAO,CAACvF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACA,KAAK,CAACiF,GAAG,CAAC,iBAAiB,CAAC;QACjC,IAAI,CAAC9C,iBAAiB,CAAC8C,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC5E,eAAe,CAAC4E,GAAG,CAAC,IAAI,CAAC;QAC9B,IAAI,CAACT,SAAS,CAACS,GAAG,CAAC,KAAK,CAAC;MAC3B;KACD,CAAC;EACN;EACArD,aAAaA,CAAC4D,OAA6B;IACzC,IAAI,CAACnF,eAAe,CAAC4E,GAAG,CAACO,OAAO,CAAC;IACjC,IAAI,CAAC5B,kBAAkB,CAACqB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;EACtC;EAEA7B,oBAAoBA,CAAA;IAClB,IAAI,CAACQ,kBAAkB,CAACqB,GAAG,CAAC,CAAC,IAAI,CAACrB,kBAAkB,EAAE,CAAC;EACzD;EAEA6B,qBAAqBA,CAAA;IACnB,IAAI,CAAC7B,kBAAkB,CAACqB,GAAG,CAAC,KAAK,CAAC;EACpC;EAEA;EACA1E,cAAcA,CAACiF,OAA6B;IAC1C,MAAME,MAAM,GAAG,IAAI,CAACvB,oBAAoB,CAACqB,OAAO,CAACpB,gBAAgB,CAAC;IAClE,QAAQsB,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,gBAAgB;MACvC,KAAK,QAAQ;QAAE,OAAO,eAAe;MACrC,KAAK,WAAW;QAAE,OAAO,kBAAkB;MAC3C,KAAK,MAAM;QAAE,OAAO,aAAa;MACjC,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC,KAAK,SAAS;QAAE,OAAO,gBAAgB;MACvC;QAAS,OAAO,gBAAgB;;EAEpC;EAEA;EACAjF,aAAaA,CAAC+E,OAA6B;IACzC,MAAME,MAAM,GAAG,IAAI,CAACvB,oBAAoB,CAACqB,OAAO,CAACpB,gBAAgB,CAAC;IAClE,QAAQsB,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,YAAY;MACnC,KAAK,QAAQ;QAAE,OAAO,gBAAgB;MACtC,KAAK,WAAW;QAAE,OAAO,mBAAmB;MAC5C,KAAK,MAAM;QAAE,OAAO,WAAW;MAC/B,KAAK,UAAU;QAAE,OAAO,qBAAqB;MAC7C,KAAK,UAAU;QAAE,OAAO,eAAe;MACvC,KAAK,SAAS;QAAE,OAAO,qBAAqB;MAC5C;QAAS,OAAO,YAAY;;EAEhC;EAEA;EACAhF,aAAaA,CAAC8E,OAA6B;IACzC,MAAME,MAAM,GAAG,IAAI,CAACvB,oBAAoB,CAACqB,OAAO,CAACpB,gBAAgB,CAAC;IAClE,QAAQsB,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,KAAK;MAC5B,KAAK,QAAQ;QAAE,OAAO,KAAK;MAC3B,KAAK,WAAW;QAAE,OAAO,KAAK;MAC9B,KAAK,MAAM;QAAE,OAAO,KAAK;MACzB,KAAK,UAAU;QAAE,OAAO,KAAK;MAC7B,KAAK,UAAU;QAAE,OAAO,KAAK;MAC7B,KAAK,SAAS;QAAE,OAAO,KAAK;MAC5B;QAAS,OAAO,KAAK;;EAEzB;EAEApC,YAAYA,CAAA;IACV;IACA,MAAMqC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,sBAAsB,CAAC;IACrE,IAAIF,WAAW,IAAI,IAAI,CAACtF,eAAe,EAAE,EAAE;MACzC,MAAMyF,eAAe,GAAG,IAAI,CAACC,qBAAqB,EAAE;MACpDJ,WAAW,CAACK,QAAQ,CAACC,KAAK,CAACH,eAAe,CAAC;MAC3CH,WAAW,CAACK,QAAQ,CAACE,KAAK,EAAE;MAE5B;MACAP,WAAW,CAACQ,MAAM,GAAG,MAAK;QACxBR,WAAW,CAACS,KAAK,EAAE;QACnBT,WAAW,CAACO,KAAK,EAAE;MACrB,CAAC;;EAEL;EAEQH,qBAAqBA,CAAA;IAC3B,MAAMP,OAAO,GAAG,IAAI,CAACnF,eAAe,EAAE;IACtC,IAAI,CAACmF,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,IAAIa,QAAQ,GAAGrH,kBAAkB;IAEjC;IACA,MAAMsH,KAAK,GAAGd,OAAO,CAACpE,iBAAiB,IAAI,EAAE;IAC7C,MAAMmF,QAAQ,GAAG,IAAI,CAAC9B,iBAAiB,CAAC6B,KAAK,CAAC;IAC9C,MAAME,QAAQ,GAAGhB,OAAO,CAACvD,UAAU,GAAG,IAAI,CAACe,iBAAiB,CAACsD,KAAK,EAAEd,OAAO,CAAChE,aAAa,CAAC,GAAG,CAAC;IAC9F,MAAMiF,WAAW,GAAG,IAAI,CAAC/B,uBAAuB,CAACc,OAAO,CAAC;IAEzD;IACA,MAAMkB,SAAS,GAAGJ,KAAK,CAACK,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;;kCAEfA,KAAK,GAAG,CAAC;cAC7BD,IAAI,CAACrE,SAAS,IAAI,EAAE;qCACG,CAACqE,IAAI,CAAClE,UAAU,IAAI,CAAC,EAAEoE,cAAc,EAAE;kCAC1CF,IAAI,CAACpE,KAAK,IAAI,EAAE;kCAChBoE,IAAI,CAACnE,MAAM,IAAI,CAAC;qCACb,CAACmE,IAAI,CAACjE,SAAS,IAAI,CAAC,EAAEmE,cAAc,EAAE;cAC7DF,IAAI,CAAChE,OAAO,IAAI,EAAE;;KAE3B,CAAC,CAACmE,IAAI,CAAC,EAAE,CAAC;IAEX;IACA,MAAMC,iBAAiB,GAAGxB,OAAO,CAACvD,UAAU,IAAIuD,OAAO,CAAChE,aAAa,GACnE,+BAA+BgE,OAAO,CAACjE,UAAU,IAAI,MAAM,KAAKiE,OAAO,CAAChE,aAAa,UAAUgF,QAAQ,CAACM,cAAc,EAAE,QAAQ,GAAG,EAAE;IAEvI;IACAT,QAAQ,GAAGA,QAAQ,CAChBY,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;IAAA,CACtCA,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;IAAA,CAClCA,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAAA,CAC5BA,OAAO,CAAC,gBAAgB,EAAE,IAAIC,IAAI,EAAE,CAACC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CACjEF,OAAO,CAAC,gBAAgB,EAAEP,SAAS,CAAC,CACpCO,OAAO,CAAC,qBAAqB,EAAE,OAAOV,QAAQ,CAACO,cAAc,EAAE,EAAE,CAAC,CAClEG,OAAO,CAAC,wBAAwB,EAAED,iBAAiB,CAAC,CACpDC,OAAO,CAAC,kBAAkB,EAAE,OAAOR,WAAW,CAACK,cAAc,EAAE,EAAE,CAAC,CAClEG,OAAO,CAAC,oBAAoB,EAAE,IAAIC,IAAI,EAAE,CAACJ,cAAc,CAAC,OAAO,CAAC,CAAC;IAEpE,OAAOT,QAAQ;EACjB;EAMAe,kBAAkBA,CAAC5B,OAA6B;IAC9C,IAAI,CAACnF,eAAe,CAAC4E,GAAG,CAACO,OAAO,CAAC;IACjC;IACA,MAAM6B,cAAc,GAAGrB,QAAQ,CAACsB,aAAa,CAAC,oBAAoB,CAAC;IACnE,IAAID,cAAc,EAAE;MAClBA,cAAc,CAACE,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAO,CAAE,CAAC;;EAEzE;EAEAC,cAAcA,CAAClC,OAA6B;IAC1C;IACAmC,KAAK,CAAC,QAAQnC,OAAO,CAACoC,UAAU,UAAU,IAAI,CAACvH,eAAe,EAAE,EAAEuH,UAAU,YAAY,CAAC;EAC3F;EAEAC,eAAeA,CAAA;IACb;IACAF,KAAK,CAAC,cAAc,CAAC;EACvB;EAEA3G,YAAYA,CAAA;IACV;IACA2G,KAAK,CAAC,cAAc,CAAC;EACvB;EAEAxD,oBAAoBA,CAAC2D,UAAmB;IACtC,QAAQA,UAAU;MAChB,KAAK,CAAC;QAAE,OAAO,SAAS;MAAG;MAC3B,KAAK,CAAC;QAAE,OAAO,QAAQ;MAAI;MAC3B,KAAK,CAAC;QAAE,OAAO,WAAW;MAAE;MAC5B;QAAS,OAAO,SAAS;MAAE;;EAE/B;EAEA;EACAC,kBAAkBA,CAACvC,OAA6B;IAC9C,OAAO,IAAIA,OAAO,CAACwC,mBAAmB,EAAE;EAC1C;EAEA;EACA5H,gBAAgBA,CAACoF,OAA6B;IAC5C,OAAOA,OAAO,CAACoC,UAAU,IAAI,MAAM;EACrC;EAEA;EACAhH,aAAaA,CAAC4E,OAA6B;IACzC,OAAOA,OAAO,CAACyC,SAAS,GAAG,IAAIf,IAAI,CAAC1B,OAAO,CAACyC,SAAS,CAAC,GAAG,IAAIf,IAAI,EAAE;EACrE;EAEA;EACAzC,iBAAiBA,CAAC6B,KAAiC;IACjD,IAAI,CAACA,KAAK,EAAE,OAAO,CAAC;IACpB,OAAOA,KAAK,CAAC4B,MAAM,CAAC,CAACC,GAAG,EAAEvB,IAAI,KAAKuB,GAAG,IAAIvB,IAAI,CAACjE,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACpE;EAEA;EACAyF,YAAYA,CAAC9B,KAAiC;IAC5C,MAAMC,QAAQ,GAAG,IAAI,CAAC9B,iBAAiB,CAAC6B,KAAK,CAAC;IAC9C,OAAO+B,IAAI,CAACC,KAAK,CAAC/B,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC;EACrC;EAEA;EACAvD,iBAAiBA,CAACsD,KAAiC,EAAEiC,YAAqB;IACxE,IAAI,CAACA,YAAY,IAAIA,YAAY,IAAI,CAAC,EAAE,OAAO,CAAC;IAChD,MAAMhC,QAAQ,GAAG,IAAI,CAAC9B,iBAAiB,CAAC6B,KAAK,CAAC;IAC9C,OAAO+B,IAAI,CAACC,KAAK,CAAC/B,QAAQ,IAAIgC,YAAY,GAAG,GAAG,CAAC,CAAC;EACpD;EAEA;EACA7D,uBAAuBA,CAACc,OAA6B;IACnD,MAAMe,QAAQ,GAAG,IAAI,CAAC9B,iBAAiB,CAACe,OAAO,CAACpE,iBAAiB,CAAC;IAClE,MAAMoF,QAAQ,GAAGhB,OAAO,CAACvD,UAAU,GAAG,IAAI,CAACe,iBAAiB,CAACwC,OAAO,CAACpE,iBAAiB,EAAEoE,OAAO,CAAChE,aAAa,CAAC,GAAG,CAAC;IAClH,OAAO+E,QAAQ,GAAGC,QAAQ;EAC5B;EAAC,QAAAgC,CAAA,G;qBA1OU3D,kBAAkB,EAAA5F,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB/D,kBAAkB;IAAAgE,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA9J,EAAA,CAAA+J,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA9C,QAAA,WAAA+C,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChB7BpK,EADF,CAAAC,cAAA,aAAqB,aACE;QAiBnBD,EAdA,CAAA4C,UAAA,IAAA0H,iCAAA,iBAAmD,IAAAC,iCAAA,iBAKN,IAAAC,iCAAA,iBASP;QAmY1CxK,EADE,CAAAG,YAAA,EAAM,EACF;;;QAjZIH,EAAA,CAAAa,SAAA,GAAiB;QAAjBb,EAAA,CAAAqB,UAAA,SAAAgJ,GAAA,CAAA9E,SAAA,GAAiB;QAKjBvF,EAAA,CAAAa,SAAA,EAAa;QAAbb,EAAA,CAAAqB,UAAA,SAAAgJ,GAAA,CAAAtJ,KAAA,GAAa;QASbf,EAAA,CAAAa,SAAA,EAA8B;QAA9Bb,EAAA,CAAAqB,UAAA,UAAAgJ,GAAA,CAAA9E,SAAA,OAAA8E,GAAA,CAAAtJ,KAAA,GAA8B;;;mBDN5BlB,YAAY,EAAA4K,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,WAAA,EAAAJ,EAAA,CAAAK,QAAA,EAAEhL,oBAAoB;IAAAiL,MAAA;IAAAC,eAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}