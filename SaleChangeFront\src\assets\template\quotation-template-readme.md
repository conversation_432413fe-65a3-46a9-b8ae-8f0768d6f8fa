# 報價單列印模板使用說明

## 模板位置
- 模板文件：`C:\Users\<USER>\Documents\salechange-product\模板\quotation-print-template.html`
- 使用組件：`adminFront/src/app/pages/household-management/household-management.component.ts`

## 模板功能
這個HTML模板用於生成報價單的列印版本，包含以下功能：

### 1. 基本信息區域
- 建案名稱
- 戶別
- 樓層
- 客戶姓名
- 列印日期

### 2. 報價項目表格
- 序號
- 項目名稱
- 單價 (元)
- 單位
- 數量
- 小計 (元)
- 類型

### 3. 總計區域
- 小計金額
- 額外費用（營業稅 5%）
- 總金額

### 4. 簽名區域
- 客戶簽名欄（居中顯示）
- 日期欄位

### 5. 注意事項
- 報價單有效期限為30天
- 報價內容若有異動，請重新確認
- 簽名確認後即視為同意此報價內容

## 模板變數
模板使用以下占位符，會在列印時被實際數據替換：

- `{{buildCaseName}}` - 建案名稱
- `{{houseHold}}` - 戶別
- `{{floor}}` - 樓層
- `{{customerName}}` - 客戶姓名
- `{{printDate}}` - 列印日期
- `{{itemsHtml}}` - 報價項目HTML
- `{{subtotalAmount}}` - 小計金額
- `{{additionalFeeHtml}}` - 額外費用HTML
- `{{totalAmount}}` - 總金額
- `{{printDateTime}}` - 列印時間

## 使用方式

### 在組件中的使用
組件會自動：
1. 嘗試讀取外部模板文件（路徑：`../模板/quotation-print-template.html`）
2. 如果讀取失敗，會回退到內建模板
3. 替換模板中的占位符
4. 生成完整的HTML內容
5. 開啟新視窗進行列印

### 模板修改
如需修改模板樣式或內容：
1. 直接編輯 `模板/quotation-print-template.html`
2. 保持占位符格式不變
3. 可以修改CSS樣式、HTML結構等
4. 重新載入頁面即可看到變更

## 樣式特色
- 使用微軟正黑體字型
- 綠色主題色彩 (#27ae60)
- 響應式設計
- 列印友好的樣式
- 專業的表格設計
- 簡化的簽名區域（僅客戶簽名）

## 最新調整
### v2.0 更新內容：
1. **模板位置調整**：從 `SaleChangeFront` 移動到 `模板` 目錄
2. **簽名區域簡化**：移除業務簽名欄位，僅保留客戶簽名
3. **簽名區域居中**：客戶簽名區域改為居中顯示，更美觀
4. **寬度調整**：簽名框寬度固定為300px，適合列印

## 注意事項
1. 模板文件必須放在 `模板` 目錄下
2. 模板路徑在組件中設定為相對路徑 `../模板/quotation-print-template.html`
3. 如果模板讀取失敗，系統會自動使用備用的內建模板
4. 營業稅固定為5%，已在組件邏輯中處理
5. 模板支援列印媒體查詢，確保列印效果良好
6. 簽名區域已簡化，僅包含客戶簽名欄位

## 技術實現
- 使用 Angular HttpClient 讀取模板文件
- 使用 Promise/async-await 處理異步操作
- 字符串替換方式處理模板變數
- 錯誤處理機制確保系統穩定性
- 支援中文路徑和檔名
