﻿@import "../../../../../styles.scss";

.planreview-box {
  display: flex;
  flex-direction: row;

  @media screen and (max-width: $main-mobileL) {
    flex-direction: column;
  }
}

/* 優化的空狀態設計 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 32px;
  background: linear-gradient(135deg, $secondary-cream-light 0%, $secondary-cream 100%);
  border: 2px dashed rgba(184, 166, 118, 0.3);
  border-radius: 16px;
  text-align: center;
  position: relative;
  overflow: hidden;
  min-height: 200px;
  transition: all $transition-normal;
  width: 100%;

  /* 背景裝飾 */
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, rgba(184, 166, 118, 0.03) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
    z-index: 0;
  }

  /* 內容容器 */
  .no-data-content {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  /* 圖標區域 */
  .no-data-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, $primary-gold-light 0%, $primary-gold-dark 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: $shadow-lg;
    animation: pulse 2s ease-in-out infinite;

    svg {
      width: 40px;
      height: 40px;
      fill: $text-light;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }
  }

  /* 主要標題 */
  .no-data-title {
    font-size: 24px;
    font-weight: 700;
    color: $text-primary;
    margin: 0;
    line-height: 1.3;
    letter-spacing: -0.5px;
  }

  /* 描述文字 */
  .no-data-description {
    font-size: 16px;
    color: $text-secondary;
    line-height: 1.5;
    max-width: 400px;
    margin: 0;
  }

  /* 狀態指示器 */
  .no-data-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(255, 221, 100, 0.2);
    border: 1px solid rgba(255, 221, 100, 0.4);
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #B8860B;

    &::before {
      content: '';
      width: 8px;
      height: 8px;
      background: #FFD700;
      border-radius: 50%;
      animation: blink 1.5s ease-in-out infinite;
    }
  }

  /* 懸停效果 */
  &:hover {
    border-color: rgba(184, 166, 118, 0.5);
    transform: translateY(-2px);
    box-shadow: $shadow-xl;

    .no-data-icon {
      transform: scale(1.05);
    }
  }

  /* 響應式設計 */
  @media screen and (max-width: $main-mobileL) {
    padding: 36px 24px;
    min-height: 180px;

    .no-data-icon {
      width: 64px;
      height: 64px;

      svg {
        width: 32px;
        height: 32px;
      }
    }

    .no-data-title {
      font-size: 20px;
    }

    .no-data-description {
      font-size: 14px;
    }
  }

  @media screen and (max-width: $main-mobileM) {
    padding: 24px 16px;
    min-height: 160px;

    .no-data-icon {
      width: 56px;
      height: 56px;

      svg {
        width: 28px;
        height: 28px;
      }
    }

    .no-data-title {
      font-size: 18px;
    }

    .no-data-description {
      font-size: 13px;
    }
  }
}

/* 動畫效果 */
@keyframes float {

  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }

  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }

  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes blink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0.3;
  }
}

:host ::ng-deep {
  .custom-tabview {
    .p-tabview-nav {
      background: transparent;
      border: none;
      border-bottom: 2px solid #E5E7EB;

      li {
        margin-right: 1.5rem;

        .p-tabview-nav-link {
          background: transparent;
          border: none;
          border-bottom: 2px solid transparent;
          color: #666;
          padding: 1rem 0.5rem;
          font-weight: 500;
          transition: all 0.3s;

          &:hover {
            color: $primary-gold-light;
            border-color: $primary-gold-light;
          }

          &:focus {
            box-shadow: none;
          }
        }

        &.p-highlight {
          .p-tabview-nav-link {
            background: transparent;
            border-bottom: 2px solid $primary-gold-light;
            color: $primary-gold-light;
          }
        }
      }
    }

    .p-tabview-panels {
      padding: 1.5rem 0;
      background: transparent;
      border: none;
    }
  }

  .fit-size {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
