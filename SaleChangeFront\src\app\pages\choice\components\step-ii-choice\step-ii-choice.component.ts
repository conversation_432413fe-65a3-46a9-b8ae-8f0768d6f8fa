import { CommonModule, NgFor } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { RadioButtonModule } from 'primeng/radiobutton';
import {
  BuildingSample,
  GetBuildingSampleSelectionRes,
  GetRegularChangeDetailByItemIdRes, RegularChangeDetail,
  RegularRemark,
  SaveRegularChangeDetailRequest,
  GetListRegularChangeItemRes
} from '../../../../../services/api/models';
import { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';
import { LoadingService } from '../../../../shared/services/loading.service';
import { ButtonModule } from 'primeng/button';
import { Observable, finalize, forkJoin, tap } from 'rxjs';
import html2canvas from 'html2canvas';
import { RegularChangeItemService } from '../../../../../services/api/services';
import { CheckboxModule } from 'primeng/checkbox';
import { ToastMessage } from '../../../../shared/services/message.service';
@Component({
  selector: 'app-step-ii-choice',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    RadioButtonModule,
    FormsModule,
    DropdownModule,
    DialogModule,
    CheckboxModule,

    NgFor,

    BaseFilePipe,
  ],
  templateUrl: './step-ii-choice.component.html',
  styleUrl: './step-ii-choice.component.scss'
})

export class StepIiChoiceComponent implements OnInit {
  @Input() listRegularChangeItem: GetListRegularChangeItemRes[] = []
  @Input() currentTypeUI!: number
  @Input() isDisabledOptionStep2: boolean = false;

  @Output() nextEvent = new EventEmitter()
  @Output() currentTypeUIChange = new EventEmitter();
  @Output() refreshListRegular = new EventEmitter()
  @Output() listRegularChangeItemChange = new EventEmitter()

  buildingSampleSelection!: GetBuildingSampleSelectionRes
  choiceenlargeimg: string | undefined;
  conentenlargeimg: string | undefined;

  currentregularChangeItem!: GetListRegularChangeItemRes
  regularChangeDetail!: GetRegularChangeDetailByItemIdRes
  selectedRegularChangeDetail!: RegularChangeDetail[] //For UIType = 1 && 2
  selectedBuildingSample!: BuildingSample //For UIType = 3
  selectedRemark!: RegularRemark
  visibleimg: boolean = false;

  constructor(
    private _regularChangeService: RegularChangeItemService,
    private _toastService: ToastMessage,
  ) { }

  ngOnInit() {
  }

  enlargeimg(img?: string | any, CDescription?: | any) {
    this.conentenlargeimg = CDescription ? CDescription : ''
    this.visibleimg = true;
    this.choiceenlargeimg = img;
  }
  closeimg() {
    this.visibleimg = false;
  }

  hanldeTitleOption(isSelect: boolean) {
    return !isSelect ? '未選擇' : '已選擇'
  }

  handleStyleOption(isSelect: boolean) {
    if (isSelect) {
      return { 'background': 'linear-gradient(90deg, #ae9b66, #b8a676)', 'color': 'white' }
    }
    return { 'backgroundColor': '#E5E3E1', 'color': '#3A4246B2' }
  }

  gotoPickItem(regularChange: GetListRegularChangeItemRes) {
    this.currentTypeUI = regularChange.CUiType!
    this.currentregularChangeItem = regularChange
    this.getRegularChangeById(regularChange).subscribe()
  }

  next() {
    this.nextEvent.emit()
  }

  save() {
    LoadingService.loading(true);

    // 針對不同的 UI 類型進行不同的檢核
    if (this.currentTypeUI === 3) {
      // Case 3: 建材選樣 - 檢查 selectedBuildingSample
      if (!this.selectedBuildingSample) {
        this._toastService.showErrorMSG("請選擇一個選項")
        LoadingService.loading(false);
        return;
      }
    } else {
      // Case 1 & 2: 一般選項 - 檢查 selectedRegularChangeDetail
      if (this.regularChangeDetail?.CRequireAnswer! == 1) {
        if (this.selectedRegularChangeDetail == null) {
          this._toastService.showErrorMSG("必填數量 " + this.regularChangeDetail?.CRequireAnswer!)
          LoadingService.loading(false);
          return;
        }
      }
      else {
        if (this.regularChangeDetail?.CRequireAnswer! != this.selectedRegularChangeDetail?.length) {
          this._toastService.showErrorMSG("必填數量 " + this.regularChangeDetail?.CRequireAnswer!)
          LoadingService.loading(false);
          return;
        }
      }
    }

    let screenshot = document.getElementById("choice-selecting")
    html2canvas(screenshot!, {
      allowTaint: true, useCORS: true,
      ignoreElements: (element) => {
        if (element.id === "action") {
          return true;
        }
        return false;
      }
    }).then((canvas) => {
      const base64image = canvas.toDataURL("image/png");
      let payload: SaveRegularChangeDetailRequest[] = []
      if (this.currentTypeUI === 3) {
        payload.push({
          CRegularChangeDetailId: this.handleReturnID().CRegularChangeDetailId,
          CRegularChangeItemlId: this.handleReturnID().CRegularChangeItemId,
          CRemark: this.currentTypeUI === 3 ? this.selectedBuildingSample.CRemark : null,
          CResultHtml: base64image,
          RegularRemark: {
            CRegularChangeItemRemarkTypeID: this.currentTypeUI === 3 ? this.selectedRemark.CRegularChangeItemRemarkTypeID! : null,
          }
        })
      } else {
        if (this.regularChangeDetail?.CRequireAnswer == 1) {
          if (this.selectedRegularChangeDetail) {
            payload.push({
              CRegularChangeDetailId: (this.selectedRegularChangeDetail as RegularChangeDetail).CRegularChangeDetailId,
              CRegularChangeItemlId: this.regularChangeDetail.CRegularChangeItemId!,
              CRemark: null,
              CResultHtml: base64image,
              RegularRemark: {
                CRegularChangeItemRemarkTypeID: null,
              }
            })
          }
        }
        else {
          this.selectedRegularChangeDetail.forEach(x => {
            payload.push({
              CRegularChangeDetailId: x.CRegularChangeDetailId,
              CRegularChangeItemlId: this.regularChangeDetail.CRegularChangeItemId!,
              CRemark: null,
              CResultHtml: base64image,
              RegularRemark: {
                CRegularChangeItemRemarkTypeID: null,
              }
            })
          })
        }
      }
      this._regularChangeService.apiRegularChangeItemSaveRegularChangeDetailPost$Json({
        body: payload
      }).pipe(
        tap(res => {
          if (res.StatusCode == 0) {
            LoadingService.loading(false)
            this.reset();
            this.refreshListRegular.emit()
          }
        }),
      ).subscribe();
    });

  }

  reset() {
    this.currentTypeUI = 0;
    this.regularChangeDetail = {}
    this.selectedRegularChangeDetail = []
    this.selectedBuildingSample = {} as BuildingSample
    this.buildingSampleSelection = {} as GetBuildingSampleSelectionRes
    this.selectedRemark = {} as RegularRemark
  }

  handleReturnID() {
    let CRegularChangeItemId = undefined;
    let CRegularChangeDetailId = undefined;

    if (this.currentTypeUI == 3) {
      CRegularChangeItemId = this.buildingSampleSelection.CRegularChangeItemId!
      CRegularChangeDetailId = this.selectedBuildingSample.CRegularChangeDetailId!
    }
    return { CRegularChangeDetailId, CRegularChangeItemId };
  }

  getRegularChangeById(regularChange: GetListRegularChangeItemRes) {
    return this._regularChangeService.apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Json({
      body: regularChange.CRegularChangeItemId!
    }).pipe(
      tap(res => {
        if (res.StatusCode === 0) {
          this.regularChangeDetail = res.Entries!

          if (this.currentTypeUI === 3) {
            // Handle building sample case - convert regularChangeDetails to building samples format
            this.buildingSampleSelection = {
              CDesignFileUrl: res.Entries!.CDesignFileUrl,
              CItemName: res.Entries!.CItemName,
              CRegularChangeItemId: res.Entries!.CRegularChangeItemId,
              // buildingSamples: res.Entries!.regularChangeDetails?.map(detail => ({
              //   CDescription: detail.CDescription,
              //   CInfoPictureFile: detail.CInfoPicture ? [detail.CInfoPicture] : null,
              //   CIsSelect: detail.CIsSelect,
              //   CPictureFile: detail.CPicture ? [detail.CPicture] : null,
              //   CRegularChangeDetailId: detail.CRegularChangeDetailId,
              //   CRemark: null,
              //   CSelectName: detail.CSelectName,
              //   RegularRemark: detail.RegularRemark,
              //   CPrice: detail.CPrice,
              // }))
            }

            // Add "其他（自填）" option to each building sample
            this.buildingSampleSelection.buildingSamples?.forEach(x => {
              x.RegularRemark?.push({
                CIsSelect: null,
                CRegularChangeItemRemarkTypeID: null,
                CTypeName: "其他（自填）"
              })
            });

            // 確保有選中的建材樣本，優先選擇已選擇的，否則選擇第一個
            const preSelectedSample = this.buildingSampleSelection.buildingSamples!.find(x => x.CIsSelect);
            this.selectedBuildingSample = preSelectedSample || this.buildingSampleSelection.buildingSamples![0];

            // 確保 selectedBuildingSample 存在時才設置 selectedRemark
            if (this.selectedBuildingSample) {
              this.selectedRemark = this.selectedBuildingSample.RegularRemark?.find(x => x.CTypeName == this.selectedBuildingSample.CRemark)
                || this.selectedBuildingSample.RegularRemark?.[2]
                || this.selectedBuildingSample.RegularRemark?.[0]
                || {} as RegularRemark;

              this.selectedBuildingSample.CRemark = this.selectedBuildingSample.CRemark || this.selectedRemark.CTypeName || '';
            }
          } else {
            // Handle regular change details case
            let tempData = res.Entries?.regularChangeDetails?.filter(x => x.CIsSelect)
            let tempSelected: any
            tempSelected = regularChange?.CRequireAnswer == 1 ? tempData![0] : tempData
            this.selectedRegularChangeDetail = tempSelected
          }
        }
      })
    )
  }

  changeBuilding(event: any) {
    if (this.selectedRemark.CRegularChangeItemRemarkTypeID != null) {
      this.selectedBuildingSample.CRemark = this.selectedRemark.CTypeName
    }
  }

  changeRemark(event: any) {
    if (event.value.CRegularChangeItemRemarkTypeID !== null) {
      this.selectedBuildingSample.CRemark = event.value.CTypeName
    } else {
      this.selectedBuildingSample.CRemark = ""
    }
  }

  checkAllSelect() {
    // return !this.listRegularChangeItem.every(x => x.CIsSelect)
    return false
  }

  validateTotalAnswer() {
    if (this.regularChangeDetail?.CRequireAnswer) {
      if (this.selectedRegularChangeDetail.length > this.regularChangeDetail?.CRequireAnswer) {
        this.selectedRegularChangeDetail.pop();
        this._toastService.showErrorMSG("必填數量 " + this.regularChangeDetail?.CRequireAnswer!)
        LoadingService.loading(false);
      }
    }
  }

  formatPrice(price: number | undefined): string {
    if (!price) return '';
    return price.toLocaleString('zh-TW');
  }
}

