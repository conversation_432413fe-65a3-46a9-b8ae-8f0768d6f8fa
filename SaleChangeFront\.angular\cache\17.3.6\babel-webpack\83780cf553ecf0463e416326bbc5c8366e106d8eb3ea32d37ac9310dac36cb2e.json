{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MessageService } from 'primeng/api';\nimport { PanelModule } from 'primeng/panel';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ToastModule } from 'primeng/toast';\nimport { DateFormatPipe } from '../../shared/pipes/date-format.pipe';\nimport { LocalStorageService } from '../../shared/services/local-storage.service';\nimport { STORAGE_KEY } from '../../shared/constant/constant';\nimport { ToastMessage } from '../../shared/services/message.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/api/services\";\nimport * as i2 from \"../../shared/services/utility.service\";\nimport * as i3 from \"../../shared/services/message.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"primeng/panel\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/toast\";\nfunction HistoryComponent_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u6211\\u8981\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HistoryComponent_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u8655\\u7406\\u4E2D...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HistoryComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"i\", 23);\n    i0.ɵɵtext(3, \" \\u652F\\u63F4 PDF\\u3001DWG\\u3001DXF\\u3001DWF \\u683C\\u5F0F\\uFF0C\\u6A94\\u6848\\u5927\\u5C0F\\u9650\\u5236 10MB \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 24)(5, \"div\", 25);\n    i0.ɵɵelement(6, \"i\", 26);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"\\u652F\\u63F4\\u591A\\u6A94\\u6848\\u540C\\u6642\\u9078\\u64C7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 25);\n    i0.ɵɵelement(10, \"i\", 26);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"\\u62D6\\u62FD\\u6A94\\u6848\\u5230\\u6B64\\u8655\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction HistoryComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵlistener(\"drop\", function HistoryComponent_div_14_Template_div_drop_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDragDrop($event));\n    })(\"dragover\", function HistoryComponent_div_14_Template_div_dragover_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDragOver($event));\n    })(\"dragleave\", function HistoryComponent_div_14_Template_div_dragleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDragLeave($event));\n    })(\"click\", function HistoryComponent_div_14_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      i0.ɵɵnextContext();\n      const fileInput_r4 = i0.ɵɵreference(16);\n      return i0.ɵɵresetView(fileInput_r4.click());\n    });\n    i0.ɵɵelementStart(1, \"div\", 28);\n    i0.ɵɵelement(2, \"i\", 29);\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4, \"\\u62D6\\u62FD\\u6A94\\u6848\\u5230\\u6B64\\u8655\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31);\n    i0.ɵɵtext(6, \"\\u6216\\u9EDE\\u64CA\\u9078\\u64C7\\u591A\\u500B\\u6A94\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"span\", 33);\n    i0.ɵɵtext(9, \"PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 33);\n    i0.ɵɵtext(11, \"DWG\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 33);\n    i0.ɵɵtext(13, \"DXF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 33);\n    i0.ɵɵtext(15, \"DWF\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"drag-over\", ctx_r2.isDragOver);\n  }\n}\nfunction HistoryComponent_div_17_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 37);\n  }\n}\nfunction HistoryComponent_div_17_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"img\", 39);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_17_ng_container_3_Template_img_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.downloadFileWithoutRedirect(ctx_r2.houseHoldDetaiPicture));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.houseHoldDetaiPicture.CName, \" \");\n  }\n}\nfunction HistoryComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"p-panel\", 35);\n    i0.ɵɵtemplate(2, HistoryComponent_div_17_ng_template_2_Template, 1, 0, \"ng-template\", 36)(3, HistoryComponent_div_17_ng_container_3_Template, 4, 1, \"ng-container\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"toggleable\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isHaveFile);\n  }\n}\nfunction HistoryComponent_div_18_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 37);\n  }\n}\nfunction HistoryComponent_div_18_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"dateFormat\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, file_r6.CChangeDate));\n  }\n}\nfunction HistoryComponent_div_18_div_3_div_2_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 39);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_18_div_3_div_2_img_2_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.downloadFileWithoutRedirect({\n        CFile: item_r8.CFile || \"\",\n        CName: item_r8.CFileName || \"\"\n      }));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HistoryComponent_div_18_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, HistoryComponent_div_18_div_3_div_2_img_2_Template, 1, 0, \"img\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r8.CFileName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.CFile);\n  }\n}\nfunction HistoryComponent_div_18_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, HistoryComponent_div_18_div_3_div_1_Template, 3, 3, \"div\", 44)(2, HistoryComponent_div_18_div_3_div_2_Template, 3, 2, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r6.SpecialChangeFiles.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", file_r6.SpecialChangeFiles);\n  }\n}\nfunction HistoryComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"p-panel\", 41);\n    i0.ɵɵtemplate(2, HistoryComponent_div_18_ng_template_2_Template, 1, 0, \"ng-template\", 36)(3, HistoryComponent_div_18_div_3_Template, 3, 2, \"div\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"toggleable\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.listSpecialChange);\n  }\n}\nfunction HistoryComponent_div_19_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵelement(2, \"i\", 61);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u5F85\\u4E0A\\u50B3\\u6A94\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 62);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 63);\n    i0.ɵɵtext(8, \" \\u8ACB\\u78BA\\u8A8D\\u4EE5\\u4E0B\\u6A94\\u6848\\u5F8C\\u9EDE\\u64CA\\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.uploadedFiles.length);\n  }\n}\nfunction HistoryComponent_div_19_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵelement(2, \"i\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 67)(4, \"div\", 68);\n    i0.ɵɵelement(5, \"img\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 70)(7, \"span\", 71);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 72)(10, \"span\", 73);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 74);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"div\", 75)(15, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_19_div_5_Template_button_click_15_listener() {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r11));\n    });\n    i0.ɵɵelement(16, \"i\", 58);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r12 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", ctx_r2.getFileIcon(file_r12.name), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", file_r12.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(file_r12.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.formatFileSize(file_r12.size));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getFileType(file_r12.name));\n  }\n}\nfunction HistoryComponent_div_19_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HistoryComponent_div_19_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u4E0A\\u50B3\\u4E2D...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HistoryComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"p-panel\", 49);\n    i0.ɵɵtemplate(2, HistoryComponent_div_19_ng_template_2_Template, 9, 1, \"ng-template\", 50);\n    i0.ɵɵelementStart(3, \"div\", 51)(4, \"div\", 52);\n    i0.ɵɵtemplate(5, HistoryComponent_div_19_div_5_Template, 17, 5, \"div\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 54)(7, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_19_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.uploadFiles());\n    });\n    i0.ɵɵelement(8, \"i\", 56);\n    i0.ɵɵtemplate(9, HistoryComponent_div_19_span_9_Template, 2, 0, \"span\", 10)(10, HistoryComponent_div_19_span_10_Template, 2, 0, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_19_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearFiles());\n    });\n    i0.ɵɵelement(12, \"i\", 58);\n    i0.ɵɵtext(13, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"toggleable\", false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.uploadedFiles);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isUploading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isUploading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUploading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isUploading);\n  }\n}\nfunction HistoryComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78);\n    i0.ɵɵelement(2, \"i\", 79);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.uploadError, \" \");\n  }\n}\nexport class HistoryComponent {\n  constructor(_houseService, _specialChangeService, _utilityService, _toastService) {\n    this._houseService = _houseService;\n    this._specialChangeService = _specialChangeService;\n    this._utilityService = _utilityService;\n    this._toastService = _toastService;\n    this.items = [];\n    this.houseHoldDetaiPicture = {\n      CFile: '',\n      CName: ''\n    };\n    this.specialChangeFiles = [];\n    this.dataUser = LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN);\n    this.isHaveFile = false;\n    // 檔案上傳相關屬性\n    this.uploadedFiles = [];\n    this.isUploading = false;\n    this.uploadError = '';\n    this.maxFileSize = 10 * 1024 * 1024; // 10MB\n    this.allowedFileTypes = ['.pdf', '.dwg', '.dxf', '.dwf'];\n    this.allowedMimeTypes = ['application/pdf', 'image/vnd.dwg', 'application/acad', 'application/x-acad'];\n    this.isDragOver = false;\n    this.showUploadArea = false; // 控制上傳區域顯示\n  }\n  ngOnInit() {\n    this.getHouseHoldDetaiPic();\n    this.buildCaseId = this.dataUser.buildCaseId;\n    this.holdDetailId = this.dataUser.holdDetailId;\n    this.getListSpecialChange();\n  }\n  getListSpecialChange() {\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeFilePost$Json({\n      body: this.buildCaseId\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialChange = res.Entries ?? [];\n      }\n    });\n  }\n  getHouseHoldDetaiPic() {\n    this._houseService.apiHouseGetHouseRegularPicturePost$Json({}).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldDetaiPicture = {\n          CFile: res.Entries.CFileURL ? res.Entries.CFileURL : '',\n          CName: res.Entries.CFileName\n        };\n        if (this.houseHoldDetaiPicture.CFile) {\n          this.isHaveFile = true;\n        }\n      }\n    });\n  }\n  getFileNameFromUrl(url) {\n    const parts = url.split('/');\n    const fileName = parts.pop();\n    return fileName;\n  }\n  downloadFileWithoutRedirect(files) {\n    if (files.CFile) {\n      // this._utilityService.downloadFileFullUrl(files)\n      window.open(files.CFile, '_blank');\n    }\n  }\n  // 檔案選擇事件處理\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files) {\n      this.handleFiles(Array.from(input.files));\n    }\n  }\n  // 拖拽事件處理\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n  }\n  onDragDrop(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n    const files = event.dataTransfer?.files;\n    if (files) {\n      this.handleFiles(Array.from(files));\n    }\n  }\n  // 處理檔案\n  handleFiles(files) {\n    this.uploadError = '';\n    for (const file of files) {\n      if (!this.validateFile(file)) {\n        continue;\n      }\n      // 檢查是否已存在相同檔案\n      if (!this.uploadedFiles.some(f => f.name === file.name)) {\n        this.uploadedFiles.push(file);\n      }\n    }\n  }\n  // 檔案驗證\n  validateFile(file) {\n    // 檢查檔案大小\n    if (file.size > this.maxFileSize) {\n      this.uploadError = `檔案 \"${file.name}\" 超過大小限制 (10MB)`;\n      return false;\n    }\n    // 檢查檔案類型\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n    if (!this.allowedFileTypes.includes(fileExtension)) {\n      this.uploadError = `檔案 \"${file.name}\" 格式不支援，只支援 PDF、DWG、DXF、DWF 格式`;\n      return false;\n    }\n    return true;\n  }\n  // 移除檔案\n  removeFile(index) {\n    this.uploadedFiles.splice(index, 1);\n    this.uploadError = '';\n  }\n  // 格式化檔案大小\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n  // 上傳檔案\n  uploadFiles() {\n    if (this.uploadedFiles.length === 0) {\n      this.uploadError = '請選擇要上傳的檔案';\n      return;\n    }\n    this.isUploading = true;\n    this.uploadError = '';\n    // 將所有檔案轉換為 data URL 格式（包含 MIME 類型）\n    const filePromises = this.uploadedFiles.map(file => this.convertFileToBase64(file));\n    Promise.all(filePromises).then(dataUrls => {\n      // 組成 SpecialChangeFile 陣列\n      const specialChangeFiles = dataUrls.map((dataUrl, index) => ({\n        CFileBlood: dataUrl,\n        CFileName: this.uploadedFiles[index].name,\n        CFileType: this.getFileTypeFromExtension(this.uploadedFiles[index].name)\n      }));\n      // 調用 API\n      this._specialChangeService.apiSpecialChangeUploadSpecialChangePost$Json({\n        body: specialChangeFiles\n      }).subscribe({\n        next: response => {\n          this.isUploading = false;\n          this.uploadedFiles = [];\n          this.showUploadArea = false; // 上傳成功後隱藏上傳區域\n          this.getListSpecialChange(); // 重新載入檔案列表\n          // 顯示成功訊息\n          this._toastService.showSucessMSG('檔案上傳成功');\n        },\n        error: error => {\n          this.isUploading = false;\n          this.uploadError = '上傳失敗，請稍後再試';\n          console.error('Upload error:', error);\n          this._toastService.showErrorMSG('檔案上傳失敗：' + (error.error?.Message || '未知錯誤'));\n        }\n      });\n    }).catch(error => {\n      this.isUploading = false;\n      this.uploadError = '檔案處理失敗';\n      console.error('File processing error:', error);\n      this._toastService.showErrorMSG('檔案處理失敗');\n    });\n  }\n  // 將檔案轉換為 base64 純字串（不含 data URL 前綴）\n  convertFileToBase64(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        const result = reader.result;\n        // 只取 base64 部分\n        const base64 = result.split(',')[1];\n        resolve(base64);\n      };\n      reader.onerror = error => reject(error);\n    });\n  }\n  // 根據檔案副檔名判斷檔案類型\n  getFileTypeFromExtension(fileName) {\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n    switch (extension) {\n      case '.pdf':\n        return 1;\n      // 假設 1 代表 PDF\n      case '.dwg':\n        return 2;\n      // 假設 2 代表 DWG\n      case '.dxf':\n        return 3;\n      // 假設 3 代表 DXF\n      case '.dwf':\n        return 4;\n      // 假設 4 代表 DWF\n      default:\n        return 0;\n      // 未知類型\n    }\n  }\n  // 清除所有待上傳檔案\n  clearFiles() {\n    this.uploadedFiles = [];\n    this.uploadError = '';\n    this.showUploadArea = false; // 清除檔案時隱藏上傳區域\n  }\n  // 根據檔案名稱獲取對應圖標\n  getFileIcon(fileName) {\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'pdf':\n        return 'assets/PDF.svg';\n      case 'dwg':\n      case 'dxf':\n      case 'dwf':\n        return 'assets/designFile.svg';\n      default:\n        return 'assets/PDF.svg';\n    }\n  }\n  // 根據檔案名稱獲取檔案類型顯示文字\n  getFileType(fileName) {\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'pdf':\n        return 'PDF檔案';\n      case 'dwg':\n        return 'AutoCAD圖檔';\n      case 'dxf':\n        return 'CAD交換檔';\n      case 'dwf':\n        return 'CAD檢視檔';\n      default:\n        return '未知格式';\n    }\n  }\n  // 切換上傳區域顯示\n  toggleUploadArea() {\n    this.showUploadArea = !this.showUploadArea;\n    if (!this.showUploadArea) {\n      // 隱藏上傳區域時清除檔案和錯誤訊息\n      this.clearFiles();\n    }\n  }\n  static #_ = this.ɵfac = function HistoryComponent_Factory(t) {\n    return new (t || HistoryComponent)(i0.ɵɵdirectiveInject(i1.HouseService), i0.ɵɵdirectiveInject(i1.SpecialChangeService), i0.ɵɵdirectiveInject(i2.UtilityService), i0.ɵɵdirectiveInject(i3.ToastMessage));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HistoryComponent,\n    selectors: [[\"app-history\"]],\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([MessageService, ToastMessage]), i0.ɵɵStandaloneFeature],\n    decls: 26,\n    vars: 9,\n    consts: [[\"fileInput\", \"\"], [1, \"wrapper\"], [1, \"content\"], [1, \"flex\", \"justify-center\"], [1, \"history\"], [1, \"history-header\"], [1, \"title\"], [1, \"upload-button-container\"], [1, \"button2\", \"upload-button\", 3, \"click\", \"disabled\"], [1, \"pi\", \"pi-upload\"], [4, \"ngIf\"], [\"class\", \"upload-instructions\", 4, \"ngIf\"], [\"class\", \"drag-drop-zone\", 3, \"drag-over\", \"drop\", \"dragover\", \"dragleave\", \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".pdf,.dwg,.dxf,.dwf\", \"multiple\", \"\", 1, \"file-input-hidden\", 2, \"display\", \"none\", 3, \"change\"], [\"class\", \"card flex justify-content-center mb-3\", 4, \"ngIf\"], [\"class\", \"card flex justify-content-center w-full\", 4, \"ngIf\"], [\"class\", \"card flex justify-content-center w-full mb-3\", 4, \"ngIf\"], [\"class\", \"error-message-container\", 4, \"ngIf\"], [1, \"my-4\", \"h-32\"], [1, \"button1\", \"!w-48\"], [\"routerLink\", \"/\"], [1, \"upload-instructions\"], [1, \"upload-hint-text-header\"], [1, \"pi\", \"pi-info-circle\"], [1, \"upload-features\"], [1, \"feature-item\"], [1, \"pi\", \"pi-check-circle\"], [1, \"drag-drop-zone\", 3, \"drop\", \"dragover\", \"dragleave\", \"click\"], [1, \"drag-drop-content\"], [1, \"pi\", \"pi-cloud-upload\", \"drag-icon\"], [1, \"drag-text-primary\"], [1, \"drag-text-secondary\"], [1, \"supported-formats\"], [1, \"format-badge\"], [1, \"card\", \"flex\", \"justify-content-center\", \"mb-3\"], [\"header\", \"\\u6A19\\u6E96\\u5716\\u9762\", 1, \"w-full\", 3, \"toggleable\"], [\"pTemplate\", \"headericons\"], [\"src\", \"/assets/Vector.svg\", 1, \"black-arrow\"], [1, \"pafbox\"], [\"src\", \"assets/PDF.svg\", 1, \"cursor-pointer\", 3, \"click\"], [1, \"card\", \"flex\", \"justify-content-center\", \"w-full\"], [\"header\", \"\\u5BA2\\u8B8A\\u5716\\u9762\", 1, \"w-full\", 3, \"toggleable\"], [\"class\", \"pafdatebox\", 4, \"ngFor\", \"ngForOf\"], [1, \"pafdatebox\"], [\"class\", \"pafdate\", 4, \"ngIf\"], [\"class\", \"pafbox\", 4, \"ngFor\", \"ngForOf\"], [1, \"pafdate\"], [\"src\", \"assets/PDF.svg\", \"class\", \"cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [1, \"card\", \"flex\", \"justify-content-center\", \"w-full\", \"mb-3\"], [1, \"w-full\", \"upload-panel\", 3, \"toggleable\"], [\"pTemplate\", \"header\"], [1, \"uploaded-files-container\"], [1, \"uploaded-files\"], [\"class\", \"uploaded-file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"upload-actions\"], [1, \"button1\", \"confirm-upload-btn\", 3, \"click\", \"disabled\"], [1, \"pi\", \"pi-check\"], [1, \"button2\", \"cancel-btn\", 3, \"click\", \"disabled\"], [1, \"pi\", \"pi-times\"], [1, \"upload-panel-header\"], [1, \"upload-panel-title\"], [1, \"pi\", \"pi-cloud-upload\"], [1, \"file-count-badge\"], [1, \"upload-panel-subtitle\"], [1, \"uploaded-file-item\"], [1, \"file-status-indicator\"], [\"title\", \"\\u7B49\\u5F85\\u4E0A\\u50B3\", 1, \"pi\", \"pi-clock\"], [1, \"file-info\"], [1, \"file-icon-wrapper\"], [\"alt\", \"File\", 1, \"file-icon\", 3, \"src\"], [1, \"file-details\"], [1, \"file-name\", 3, \"title\"], [1, \"file-meta\"], [1, \"file-size\"], [1, \"file-type\"], [1, \"file-actions\"], [\"title\", \"\\u79FB\\u9664\\u6A94\\u6848\", 1, \"delete-btn\", 3, \"click\"], [1, \"error-message-container\"], [1, \"error-message\"], [1, \"pi\", \"pi-exclamation-triangle\"]],\n    template: function HistoryComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵelement(1, \"p-toast\");\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n        i0.ɵɵtext(7, \"\\u6D3D\\u8AC7\\u7D00\\u9304\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function HistoryComponent_Template_button_click_9_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggleUploadArea());\n        });\n        i0.ɵɵelement(10, \"i\", 9);\n        i0.ɵɵtemplate(11, HistoryComponent_span_11_Template, 2, 0, \"span\", 10)(12, HistoryComponent_span_12_Template, 2, 0, \"span\", 10);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(13, HistoryComponent_div_13_Template, 13, 0, \"div\", 11)(14, HistoryComponent_div_14_Template, 16, 2, \"div\", 12);\n        i0.ɵɵelementStart(15, \"input\", 13, 0);\n        i0.ɵɵlistener(\"change\", function HistoryComponent_Template_input_change_15_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFileSelected($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(17, HistoryComponent_div_17_Template, 4, 2, \"div\", 14)(18, HistoryComponent_div_18_Template, 4, 2, \"div\", 15)(19, HistoryComponent_div_19_Template, 14, 6, \"div\", 16)(20, HistoryComponent_div_20_Template, 4, 1, \"div\", 17);\n        i0.ɵɵelementStart(21, \"div\", 3)(22, \"div\", 18)(23, \"button\", 19)(24, \"a\", 20);\n        i0.ɵɵtext(25, \"\\u8FD4\\u56DE\\u4E3B\\u9078\\u55AE\");\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"disabled\", ctx.isUploading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isUploading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isUploading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showUploadArea && !ctx.uploadedFiles.length);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showUploadArea && !ctx.uploadedFiles.length);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.isHaveFile);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.listSpecialChange && ctx.listSpecialChange.length > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showUploadArea && ctx.uploadedFiles.length > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.uploadError);\n      }\n    },\n    dependencies: [RouterModule, i4.RouterLink, PanelModule, i5.Panel, i6.PrimeTemplate, PanelMenuModule, CommonModule, i7.NgForOf, i7.NgIf, DateFormatPipe, ToastModule, i8.Toast],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}html[_ngcontent-%COMP%]{line-height:initial;-webkit-text-size-adjust:100%;width:100%;height:100%}body[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:#23181599;font-family:Noto Sans TC!important;box-sizing:border-box;width:100%;height:auto;overflow:auto;overscroll-behavior:none;letter-spacing:.32px}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{-webkit-text-size-adjust:none;text-size-adjust:none}main[_ngcontent-%COMP%]{display:block}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:400}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible;margin:10px 0;border:none;border-top:1px solid #000}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1rem}a[_ngcontent-%COMP%]{background-color:transparent;text-decoration:none;-webkit-tap-highlight-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:500}strong[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:600}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}p[_ngcontent-%COMP%]{font-size:1rem}img[_ngcontent-%COMP%]{border-style:none;max-width:100%}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:Noto Sans TC,\\\\5fae\\\\8edf\\\\6b63\\\\9ed1\\\\9ad4,Arial,sans-serif;font-size:1rem;line-height:1.15;margin:0;color:#2f2f2f;-webkit-tap-highlight-color:transparent;letter-spacing:2px}select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]{padding:0;background-color:transparent;border:none;cursor:pointer}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}div[role=tab][_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]{border-spacing:0px}ol[_ngcontent-%COMP%], ul[_ngcontent-%COMP%]{padding-left:0;margin-top:0}.form-check-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:none}.container[_ngcontent-%COMP%]{width:100%}@media (min-width: 640px){.container[_ngcontent-%COMP%]{max-width:640px}}@media (min-width: 768px){.container[_ngcontent-%COMP%]{max-width:768px}}@media (min-width: 1024px){.container[_ngcontent-%COMP%]{max-width:1024px}}@media (min-width: 1280px){.container[_ngcontent-%COMP%]{max-width:1280px}}@media (min-width: 1536px){.container[_ngcontent-%COMP%]{max-width:1536px}}.visible[_ngcontent-%COMP%]{visibility:visible}.collapse[_ngcontent-%COMP%]{visibility:collapse}.static[_ngcontent-%COMP%]{position:static}.absolute[_ngcontent-%COMP%]{position:absolute}.relative[_ngcontent-%COMP%]{position:relative}.inset-y-0[_ngcontent-%COMP%]{top:0;bottom:0}.bottom-0[_ngcontent-%COMP%]{bottom:0}.left-0[_ngcontent-%COMP%]{left:0}.left-1\\\\/2[_ngcontent-%COMP%]{left:50%}.left-3[_ngcontent-%COMP%]{left:.75rem}.right-0[_ngcontent-%COMP%]{right:0}.top-1[_ngcontent-%COMP%]{top:.25rem}.top-1\\\\/2[_ngcontent-%COMP%]{top:50%}.top-3[_ngcontent-%COMP%]{top:.75rem}.m-0[_ngcontent-%COMP%]{margin:0}.m-2[_ngcontent-%COMP%]{margin:.5rem}.m-auto[_ngcontent-%COMP%]{margin:auto}.mx-3[_ngcontent-%COMP%]{margin-left:.75rem;margin-right:.75rem}.mx-4[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.mx-5[_ngcontent-%COMP%]{margin-left:1.25rem;margin-right:1.25rem}.mx-\\\\__ph-0__[_ngcontent-%COMP%]{margin-left:20%;margin-right:20%}.my-12[_ngcontent-%COMP%]{margin-top:3rem;margin-bottom:3rem}.my-2[_ngcontent-%COMP%]{margin-top:.5rem;margin-bottom:.5rem}.my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.my-6[_ngcontent-%COMP%]{margin-top:1.5rem;margin-bottom:1.5rem}.\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:.75rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ml-1[_ngcontent-%COMP%]{margin-left:.25rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.ml-4[_ngcontent-%COMP%]{margin-left:1rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-4[_ngcontent-%COMP%]{margin-right:1rem}.mr-5[_ngcontent-%COMP%]{margin-right:1.25rem}.mr-8[_ngcontent-%COMP%]{margin-right:2rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.mt-14[_ngcontent-%COMP%]{margin-top:3.5rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:.75rem}.mt-4[_ngcontent-%COMP%]{margin-top:1rem}.mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.mt-6[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-7[_ngcontent-%COMP%]{margin-top:1.75rem}.mt-8[_ngcontent-%COMP%]{margin-top:2rem}.mt-\\\\__ph-0__[_ngcontent-%COMP%]{margin-top:16px}.block[_ngcontent-%COMP%]{display:block}.flex[_ngcontent-%COMP%]{display:flex}.table[_ngcontent-%COMP%]{display:table}.table-row[_ngcontent-%COMP%]{display:table-row}.grid[_ngcontent-%COMP%]{display:grid}.\\\\!h-\\\\__ph-0__[_ngcontent-%COMP%]{height:107px!important}.\\\\!h-full[_ngcontent-%COMP%]{height:100%!important}.h-28[_ngcontent-%COMP%]{height:7rem}.h-32[_ngcontent-%COMP%]{height:8rem}.h-6[_ngcontent-%COMP%]{height:1.5rem}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:100px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:145px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:280px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:31px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:40vh}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:47px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:480px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:4px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:50px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:55px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:665px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:80px}.h-auto[_ngcontent-%COMP%]{height:auto}.h-fit[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content}.h-full[_ngcontent-%COMP%]{height:100%}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:200px}.\\\\!min-h-\\\\__ph-0__[_ngcontent-%COMP%]{min-height:60vh!important}.\\\\!w-40[_ngcontent-%COMP%]{width:10rem!important}.\\\\!w-48[_ngcontent-%COMP%]{width:12rem!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:107px!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:120px!important}.\\\\!w-full[_ngcontent-%COMP%]{width:100%!important}.w-1\\\\/3[_ngcontent-%COMP%]{width:33.333333%}.w-2\\\\/3[_ngcontent-%COMP%]{width:66.666667%}.w-28[_ngcontent-%COMP%]{width:7rem}.w-6[_ngcontent-%COMP%]{width:1.5rem}.w-7\\\\/12[_ngcontent-%COMP%]{width:58.333333%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:100%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:150px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:160px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:309px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:30rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:400px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:45%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:485px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:50%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:550px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:660px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:760px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:88px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:968px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:97%}.w-full[_ngcontent-%COMP%]{width:100%}.\\\\!max-w-full[_ngcontent-%COMP%]{max-width:100%!important}.max-w-4xl[_ngcontent-%COMP%]{max-width:56rem}.max-w-\\\\__ph-0__[_ngcontent-%COMP%]{max-width:1216px}.flex-shrink[_ngcontent-%COMP%]{flex-shrink:1}.grow[_ngcontent-%COMP%]{flex-grow:1}.border-collapse[_ngcontent-%COMP%]{border-collapse:collapse}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.resize-none[_ngcontent-%COMP%]{resize:none}.grid-cols-10[_ngcontent-%COMP%]{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}.grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}.flex-row[_ngcontent-%COMP%]{flex-direction:row}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.items-start[_ngcontent-%COMP%]{align-items:flex-start}.items-center[_ngcontent-%COMP%]{align-items:center}.justify-start[_ngcontent-%COMP%]{justify-content:flex-start}.justify-end[_ngcontent-%COMP%]{justify-content:flex-end}.justify-center[_ngcontent-%COMP%]{justify-content:center}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.gap-x-1[_ngcontent-%COMP%]{column-gap:.25rem}.self-center[_ngcontent-%COMP%]{align-self:center}.overflow-hidden[_ngcontent-%COMP%]{overflow:hidden}.overflow-y-scroll[_ngcontent-%COMP%]{overflow-y:scroll}.rounded[_ngcontent-%COMP%]{border-radius:.25rem}.rounded-3xl[_ngcontent-%COMP%]{border-radius:1.5rem}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.rounded-lg[_ngcontent-%COMP%]{border-radius:.5rem}.rounded-md[_ngcontent-%COMP%]{border-radius:.375rem}.border[_ngcontent-%COMP%]{border-width:1px}.border-2[_ngcontent-%COMP%]{border-width:2px}.border-solid[_ngcontent-%COMP%]{border-style:solid}.border-blue-400[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(96 165 250 / var(--tw-border-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 143 199 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(184 166 118 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea80}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea99}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(217 217 217 / var(--tw-bg-opacity))}.bg-gray-500[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(107 114 128 / var(--tw-bg-opacity))}.bg-white[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.bg-opacity-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: .04}.object-cover[_ngcontent-%COMP%]{object-fit:cover}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-4[_ngcontent-%COMP%]{padding:1rem}.p-\\\\__ph-0__[_ngcontent-%COMP%]{padding:16px}.px-3[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-6[_ngcontent-%COMP%]{padding-left:1.5rem;padding-right:1.5rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.py-2[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.pb-4[_ngcontent-%COMP%]{padding-bottom:1rem}.pb-5[_ngcontent-%COMP%]{padding-bottom:1.25rem}.pb-8[_ngcontent-%COMP%]{padding-bottom:2rem}.pl-4[_ngcontent-%COMP%]{padding-left:1rem}.pl-6[_ngcontent-%COMP%]{padding-left:1.5rem}.pr-1[_ngcontent-%COMP%]{padding-right:.25rem}.pr-3[_ngcontent-%COMP%]{padding-right:.75rem}.pr-4[_ngcontent-%COMP%]{padding-right:1rem}.text-left[_ngcontent-%COMP%]{text-align:left}.\\\\!text-center[_ngcontent-%COMP%]{text-align:center!important}.text-center[_ngcontent-%COMP%]{text-align:center}.text-right[_ngcontent-%COMP%]{text-align:right}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-3xl[_ngcontent-%COMP%]{font-size:1.875rem;line-height:2.25rem}.text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xl[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-light[_ngcontent-%COMP%]{font-weight:300}.font-medium[_ngcontent-%COMP%]{font-weight:500}.font-normal[_ngcontent-%COMP%]{font-weight:400}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.tracking-wider[_ngcontent-%COMP%]{letter-spacing:.05em}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 143 199 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(35 24 21 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(182 152 103 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(184 166 118 / var(--tw-text-opacity))}.text-black[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-blue-400[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.text-gray-500[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-stone-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(87 83 78 / var(--tw-text-opacity))}.text-stone-900[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(28 25 23 / var(--tw-text-opacity))}.text-white[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.shadow[_ngcontent-%COMP%]{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.grayscale[_ngcontent-%COMP%]{--tw-grayscale: grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter[_ngcontent-%COMP%]{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.content[_ngcontent-%COMP%]{width:100%}@media screen and (max-width: 1024px){.content[_ngcontent-%COMP%]{width:100%;padding:0 16px 50px}}.pc[_ngcontent-%COMP%], .inpc[_ngcontent-%COMP%]{display:initial}@media screen and (max-width: 1024px){.inpc[_ngcontent-%COMP%]{display:none}}.flat[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 1024px){.flat[_ngcontent-%COMP%]{display:initial}}.mb[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.mb[_ngcontent-%COMP%]{display:initial}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{position:relative}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background-image:url(/assets/background/background2.png);width:180px;height:150px;background-size:unset;background-position:left;z-index:-1;right:0;bottom:20px}.button1[_ngcontent-%COMP%]{width:200px;height:47px;border:1px solid #CDCDCD;color:#3a4246;border-radius:28px;background-origin:border-box;background-clip:content-box,border-box;transition:all .3s ease}.button1[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#b8a676;transition:color .3s ease}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#ae9b66}@media screen and (max-width: 912px){.button1[_ngcontent-%COMP%]{width:100%}}.button2[_ngcontent-%COMP%]{width:200px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button2[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button2[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button2[_ngcontent-%COMP%]{width:100%}}.button3[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button3[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button3[_ngcontent-%COMP%]{width:100%}}.button4[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:#979797;border-radius:24px;transition:all .3s ease}.button4[_ngcontent-%COMP%]:hover{background:#7e7e7e}.button4[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button4[_ngcontent-%COMP%]{width:100%}}.checkbox-zone[_ngcontent-%COMP%]{padding:2px;border-radius:2px;position:relative;background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 1px 4px #ae9b664d}.avoid-resize-img[_ngcontent-%COMP%]{max-width:none}.fit-size[_ngcontent-%COMP%]{height:inherit;width:inherit;object-fit:cover;object-position:center}.p-calendar[_ngcontent-%COMP%]{width:100%}.p-calendar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#23181599}@media screen and (max-width: 912px){.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]{border:none;width:100%}.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#231815}@media screen and (max-width: 912px){.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]   .p-dropdown-trigger[_ngcontent-%COMP%]{border-radius:0;border-bottom:1px solid rgba(144,150,157,.4)}.p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{box-shadow:none}.p-radiobutton[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]{width:22px;height:22px;border:2px solid #CDCDCD;background-color:#fff;border-radius:50%;transition:all .3s cubic-bezier(.25,.46,.45,.94);position:relative;box-shadow:0 1px 3px #ae9b661a;cursor:pointer}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:hover{border-color:#b8a676;background:radial-gradient(circle at center,#b8a67614,#b8a67605);transform:scale(1.05);box-shadow:0 2px 8px #ae9b6626}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #b8a67640,0 2px 8px #ae9b6633;border-color:#a39460}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:active{transform:scale(.98)}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-color:#ae9b66;box-shadow:0 2px 12px #ae9b6640,inset 0 1px 2px #fff3}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:8px;height:8px;background:radial-gradient(circle,#fff,#ffffffe6);border-radius:50%;transform:translate(-50%,-50%) scale(0);box-shadow:0 1px 2px #0003;animation:_ngcontent-%COMP%_radioAppear .2s ease forwards}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);border-color:#9b8a5a;transform:scale(1.05);box-shadow:0 3px 15px #ae9b6659,inset 0 1px 2px #ffffff40}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #b8a6764d,0 3px 15px #ae9b6659}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-icon[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.2);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{transform:translate(-50%,-50%) scale(0);opacity:.6}to{transform:translate(-50%,-50%) scale(4);opacity:0}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background-color:#fff;color:#846a52;font-size:16px;border:1px solid #E6F0F3;border-radius:6px;padding:16px 32px}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 32px 8px;border-bottom:none!important;border-bottom-right-radius:0;border-bottom-left-radius:0}@media screen and (max-width: 912px){.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(-180deg)!important;transition:transform .3s linear}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(0)!important;transition:transform .3s linear}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]:focus{box-shadow:none}.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 32px 16px}@media screen and (max-width: 912px){.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 16px 16px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{width:24px;height:24px}.p-dialog-mask[_ngcontent-%COMP%]{width:100%;height:100%;background-color:#0006;top:0;z-index:20;pointer-events:all!important}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]{padding:32px 24px 16px;color:#231815;font-size:24px;text-align:center;display:flex;justify-content:center}.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]{padding:32px 24px 0;background:linear-gradient(180deg,#f3f1ea,#fff)}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]{display:none}.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]{padding:24px 24px 40px;display:flex;justify-content:center}.p-checkbox[_ngcontent-%COMP%]{width:18px;height:18px;display:inline-flex;align-items:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]{width:18px;height:18px;border:2px solid #CDCDCD;border-radius:3px;transition:.3s ease;background:#fff;position:relative;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:hover{border-color:#b8a676}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:focus{outline:none;border-color:#b8a676;box-shadow:0 0 0 2px #b8a67633}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]{background:#b8a676!important;border-color:#b8a676!important}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{color:#fff!important;font-size:12px;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:not(.p-highlight)   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(0);opacity:0}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]:before{content:\\\"\\\\e915\\\";font-family:primeicons;font-weight:400;font-style:normal;font-size:12px;display:inline-block}.p-checkbox-label[_ngcontent-%COMP%]{margin-left:8px;cursor:pointer;-webkit-user-select:none;user-select:none}.input[_ngcontent-%COMP%]{width:100%;border:none;padding:12.5px 0;border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px;height:4px}[_ngcontent-%COMP%]::-webkit-scrollbar-button{background:transparent;border-radius:2px}[_ngcontent-%COMP%]::-webkit-scrollbar-track-piece{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:4px;background-color:#90969d33}[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:transparent}.p-radiobutton-label[_ngcontent-%COMP%]{color:#231815!important;font-weight:400;cursor:pointer;transition:all .3s ease}.p-radiobutton-label[_ngcontent-%COMP%]:hover{color:#ae9b66!important}label[for*=requirement_][_ngcontent-%COMP%]{color:#231815!important;font-weight:400}label[for*=requirement_][_ngcontent-%COMP%]:hover{color:#ae9b66!important}input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%], .p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%] + label[_ngcontent-%COMP%]{color:#ae9b66!important;font-weight:600}.history[_ngcontent-%COMP%]{margin-top:20px;width:1216px;z-index:3;min-height:550px;height:550px}.history[_ngcontent-%COMP%]   .history-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:8px}.history[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#222;font-weight:700;font-size:2.2rem;letter-spacing:.1em}.history[_ngcontent-%COMP%]   .upload-hint-text-header[_ngcontent-%COMP%]{font-size:12px;color:#9ca3af;text-align:right;margin-bottom:16px;line-height:1.4}.history[_ngcontent-%COMP%]   .pafdatebox[_ngcontent-%COMP%]{margin-bottom:8px}.history[_ngcontent-%COMP%]   .pafdatebox[_ngcontent-%COMP%]:last-child{margin-bottom:0}.history[_ngcontent-%COMP%]   .pafdate[_ngcontent-%COMP%]{margin-bottom:8px;color:#231815cc;font-weight:700}.history[_ngcontent-%COMP%]   .pafbox[_ngcontent-%COMP%]{padding:12.5px 8px 12.5px 16px;width:100%;color:#000;background-color:#f3f1ea99;display:flex;align-items:center;justify-content:space-between;margin-bottom:8px}.history[_ngcontent-%COMP%]   .pafbox[_ngcontent-%COMP%]:last-child{margin-bottom:0}.history[_ngcontent-%COMP%]   .upload-button-container[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;align-items:center;margin-bottom:1rem}.history[_ngcontent-%COMP%]   .upload-button-container[_ngcontent-%COMP%]   .file-input-hidden[_ngcontent-%COMP%]{display:none}.history[_ngcontent-%COMP%]   .upload-button-container[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;padding:10px 18px;font-size:14px;white-space:nowrap;transition:all .2s ease;box-shadow:0 2px 4px #0000001a;background:#bfa76a;color:#fff;border-radius:12px;font-weight:600;font-size:1.1rem;padding:.7rem 2.2rem;border:none}.history[_ngcontent-%COMP%]   .upload-button-container[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.history[_ngcontent-%COMP%]   .upload-button-container[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:hover:not(:disabled){background:#a68c4a;transform:translateY(-1px);box-shadow:0 4px 12px #00000026}.history[_ngcontent-%COMP%]   .upload-button-container[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(0)}.history[_ngcontent-%COMP%]   .upload-button-container[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;transform:none;box-shadow:0 2px 4px #0000001a}.history[_ngcontent-%COMP%]   .upload-panel[_ngcontent-%COMP%]{background:#fffbe6;border-radius:12px;border:1px solid #e5e1d6}.history[_ngcontent-%COMP%]   .upload-panel[_ngcontent-%COMP%]   .upload-panel-header[_ngcontent-%COMP%]   .upload-panel-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;font-size:16px;font-weight:600;color:#1f2937;margin-bottom:4px}.history[_ngcontent-%COMP%]   .upload-panel[_ngcontent-%COMP%]   .upload-panel-header[_ngcontent-%COMP%]   .upload-panel-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6;font-size:18px}.history[_ngcontent-%COMP%]   .upload-panel[_ngcontent-%COMP%]   .upload-panel-header[_ngcontent-%COMP%]   .upload-panel-title[_ngcontent-%COMP%]   .file-count-badge[_ngcontent-%COMP%]{background:#bfa76a;color:#fff;border-radius:50%;padding:.2rem .7rem;font-size:1rem;margin-left:.7rem;display:inline-block}.history[_ngcontent-%COMP%]   .upload-panel[_ngcontent-%COMP%]   .upload-panel-header[_ngcontent-%COMP%]   .upload-panel-subtitle[_ngcontent-%COMP%]{font-size:13px;color:#6b7280;font-weight:500}.history[_ngcontent-%COMP%]   .uploaded-files-container[_ngcontent-%COMP%]{margin-top:16px}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border:1px solid #e1e5e9;border-radius:12px;margin-bottom:12px;background:linear-gradient(135deg,#fff,#f8fafc);box-shadow:0 2px 8px #0000000f;transition:all .3s ease;position:relative;overflow:hidden}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]:hover{border-color:#c7d2fe;box-shadow:0 4px 16px #0000001f;transform:translateY(-1px)}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:0;top:0;bottom:0;width:4px;background:linear-gradient(135deg,#3b82f6,#1d4ed8)}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-status-indicator[_ngcontent-%COMP%]{margin-right:12px;padding:8px;background:#fef3cd;border-radius:50%;display:flex;align-items:center;justify-content:center}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-status-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#d97706;font-size:14px}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]{display:flex;align-items:center;flex:1;gap:14px}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-icon-wrapper[_ngcontent-%COMP%]{padding:8px;background:#f1f5f9;border-radius:8px;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 4px #0000000d}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-icon-wrapper[_ngcontent-%COMP%]   .file-icon[_ngcontent-%COMP%]{width:32px;height:32px;filter:drop-shadow(0 1px 2px rgba(0,0,0,.1))}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px;min-width:0}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-details[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%]{font-weight:600;color:#1f2937;font-size:15px;line-height:1.4;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:300px}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-details[_ngcontent-%COMP%]   .file-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-details[_ngcontent-%COMP%]   .file-meta[_ngcontent-%COMP%]   .file-size[_ngcontent-%COMP%]{font-size:13px;color:#6b7280;font-weight:500}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-details[_ngcontent-%COMP%]   .file-meta[_ngcontent-%COMP%]   .file-type[_ngcontent-%COMP%]{font-size:12px;color:#3b82f6;background:#eff6ff;padding:2px 8px;border-radius:12px;font-weight:600}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]{background:none;border:none;color:#ef4444;cursor:pointer;padding:10px;border-radius:8px;transition:all .2s ease;display:flex;align-items:center;justify-content:center}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover{background-color:#fef2f2;transform:scale(1.1)}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:active{transform:scale(.95)}.history[_ngcontent-%COMP%]   .uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.history[_ngcontent-%COMP%]   .upload-actions[_ngcontent-%COMP%]{margin-top:20px;padding-top:16px;border-top:1px solid #f3f4f6;display:flex;justify-content:center;gap:16px}.history[_ngcontent-%COMP%]   .upload-actions[_ngcontent-%COMP%]   .confirm-upload-btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;padding:12px 24px;transition:all .2s ease;min-width:120px;justify-content:center;background:#2563eb;color:#fff;border-radius:8px;font-weight:600;font-size:1.1rem;padding:.6rem 2.2rem;border:none;margin-right:1rem}.history[_ngcontent-%COMP%]   .upload-actions[_ngcontent-%COMP%]   .confirm-upload-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.history[_ngcontent-%COMP%]   .upload-actions[_ngcontent-%COMP%]   .confirm-upload-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#1746a2;transform:translateY(-1px);box-shadow:0 4px 12px #00000026}.history[_ngcontent-%COMP%]   .upload-actions[_ngcontent-%COMP%]   .confirm-upload-btn[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(0)}.history[_ngcontent-%COMP%]   .upload-actions[_ngcontent-%COMP%]   .confirm-upload-btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.history[_ngcontent-%COMP%]   .upload-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;padding:12px 24px;font-weight:600;border-radius:8px;transition:all .2s ease;min-width:100px;justify-content:center;background:#fff;color:#bfa76a;border:1.5px solid #bfa76a}.history[_ngcontent-%COMP%]   .upload-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.history[_ngcontent-%COMP%]   .upload-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#f8f7f3;color:#a68c4a;transform:translateY(-1px);box-shadow:0 4px 12px #00000026}.history[_ngcontent-%COMP%]   .upload-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(0)}.history[_ngcontent-%COMP%]   .upload-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.history[_ngcontent-%COMP%]   .error-message-container[_ngcontent-%COMP%]{margin-top:1.2rem;display:flex;justify-content:center;margin-bottom:20px}.history[_ngcontent-%COMP%]   .error-message-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;padding:14px 18px;background:linear-gradient(135deg,#fef2f2,#fde8e8);border:1px solid #fecaca;border-radius:8px;color:#dc2626;font-size:14px;font-weight:500;max-width:500px;box-shadow:0 2px 8px #dc26261a}.history[_ngcontent-%COMP%]   .error-message-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px;flex-shrink:0;color:#ef4444}.history[_ngcontent-%COMP%]   .upload-hint-text-header[_ngcontent-%COMP%]{color:#bfa76a;font-weight:600;margin-bottom:.5rem}.history[_ngcontent-%COMP%]   .upload-instructions[_ngcontent-%COMP%]{background:#f8f7f3;border-radius:10px;padding:1.2rem 1.5rem;margin-bottom:1.2rem;color:#6b5e3c;font-size:1.08rem;border:1px solid #e5e1d6}.history[_ngcontent-%COMP%]   .upload-instructions[_ngcontent-%COMP%]   .upload-hint-text-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:#4b5563;margin-bottom:12px;font-weight:500;text-align:left}.history[_ngcontent-%COMP%]   .upload-instructions[_ngcontent-%COMP%]   .upload-hint-text-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6;font-size:16px}.history[_ngcontent-%COMP%]   .upload-instructions[_ngcontent-%COMP%]   .upload-features[_ngcontent-%COMP%]{display:flex;gap:24px}.history[_ngcontent-%COMP%]   .upload-instructions[_ngcontent-%COMP%]   .upload-features[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-size:13px;color:#6b7280;font-weight:500;margin-right:1.5rem;display:inline-block}.history[_ngcontent-%COMP%]   .upload-instructions[_ngcontent-%COMP%]   .upload-features[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#10b981;font-size:14px}.history[_ngcontent-%COMP%]   .drag-drop-zone[_ngcontent-%COMP%]{border:2px dashed #bfa76a;background:#faf9f6;border-radius:16px;padding:2.5rem 1.5rem;margin-bottom:2rem;text-align:center;transition:border-color .2s,background .2s;cursor:pointer}.history[_ngcontent-%COMP%]   .drag-drop-zone[_ngcontent-%COMP%]:hover{border-color:#3b82f6;background:linear-gradient(135deg,#eff6ff,#dbeafe);transform:translateY(-2px);box-shadow:0 8px 25px #3b82f626}.history[_ngcontent-%COMP%]   .drag-drop-zone.drag-over[_ngcontent-%COMP%]{border-color:#a68c4a;background:#f5f1e6;box-shadow:0 8px 25px #10b98133;transform:scale(1.02)}.history[_ngcontent-%COMP%]   .drag-drop-zone.drag-over[_ngcontent-%COMP%]   .drag-icon[_ngcontent-%COMP%]{color:#10b981;animation:_ngcontent-%COMP%_bounce 1s infinite}.history[_ngcontent-%COMP%]   .drag-drop-zone.drag-over[_ngcontent-%COMP%]   .drag-text-primary[_ngcontent-%COMP%]{color:#065f46}.history[_ngcontent-%COMP%]   .drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .drag-icon[_ngcontent-%COMP%]{font-size:3.5rem;color:#bfa76a;margin-bottom:.7rem;transition:all .3s ease}.history[_ngcontent-%COMP%]   .drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .drag-text-primary[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:600;color:#222;margin-bottom:8px;transition:color .3s ease}.history[_ngcontent-%COMP%]   .drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .drag-text-secondary[_ngcontent-%COMP%]{color:#bfa76a;font-size:1.05rem;margin-bottom:.5rem}.history[_ngcontent-%COMP%]   .drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .supported-formats[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:8px;flex-wrap:wrap}.history[_ngcontent-%COMP%]   .drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .supported-formats[_ngcontent-%COMP%]   .format-badge[_ngcontent-%COMP%]{background:#bfa76a;color:#fff;border-radius:8px;padding:.2rem 1.1rem;margin:0 .2rem;font-size:1rem;font-weight:600;letter-spacing:.05em;display:inline-block;transition:background .2s,box-shadow .2s}.history[_ngcontent-%COMP%]   .drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .supported-formats[_ngcontent-%COMP%]   .format-badge[_ngcontent-%COMP%]:hover{background:#e5d7b0;color:#bfa76a;box-shadow:0 2px 8px #bfa76a33}@keyframes _ngcontent-%COMP%_bounce{0%,20%,50%,80%,to{transform:translateY(0)}40%{transform:translateY(-10px)}60%{transform:translateY(-5px)}}@media not all and (min-width: 1280px){.max-xl\\\\:grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}}@media not all and (min-width: 1024px){.max-lg\\\\:bottom-12[_ngcontent-%COMP%]{bottom:3rem}.max-lg\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-lg\\\\:grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}}@media not all and (min-width: 768px){.max-md\\\\:bottom-14[_ngcontent-%COMP%]{bottom:3.5rem}.max-md\\\\:my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.max-md\\\\:ml-0[_ngcontent-%COMP%]{margin-left:0}.max-md\\\\:mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.max-md\\\\:mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.max-md\\\\:hidden[_ngcontent-%COMP%]{display:none}.max-md\\\\:h-\\\\__ph-0__[_ngcontent-%COMP%]{height:65%}.max-md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:450px!important}.max-md\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:500px}.max-md\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-md\\\\:max-w-full[_ngcontent-%COMP%]{max-width:100%}.max-md\\\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.max-md\\\\:flex-col[_ngcontent-%COMP%]{flex-direction:column}.max-md\\\\:flex-col-reverse[_ngcontent-%COMP%]{flex-direction:column-reverse}.max-md\\\\:flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.max-md\\\\:items-start[_ngcontent-%COMP%]{align-items:flex-start}}@media not all and (min-width: 640px){.max-sm\\\\:bottom-10[_ngcontent-%COMP%]{bottom:2.5rem}.max-sm\\\\:ml-8[_ngcontent-%COMP%]{margin-left:2rem}.max-sm\\\\:block[_ngcontent-%COMP%]{display:block}.max-sm\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:340px!important}.max-sm\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:380px}.max-sm\\\\:\\\\!grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))!important}.max-sm\\\\:grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.max-sm\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.max-sm\\\\:gap-0[_ngcontent-%COMP%]{gap:0px}.max-sm\\\\:px-0[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.max-sm\\\\:pl-0[_ngcontent-%COMP%]{padding-left:0}.max-sm\\\\:text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}}@media (min-width: 640px){.sm\\\\:mx-0[_ngcontent-%COMP%]{margin-left:0;margin-right:0}.sm\\\\:mb-0[_ngcontent-%COMP%]{margin-bottom:0}.sm\\\\:mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.sm\\\\:flex[_ngcontent-%COMP%]{display:flex}.sm\\\\:items-center[_ngcontent-%COMP%]{align-items:center}.sm\\\\:pl-3[_ngcontent-%COMP%]{padding-left:.75rem}}@media (min-width: 768px){.md\\\\:flex[_ngcontent-%COMP%]{display:flex}.md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:634px!important}.md\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.md\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.md\\\\:pb-20[_ngcontent-%COMP%]{padding-bottom:5rem}}@media (min-width: 1024px){.lg\\\\:\\\\!mx-0[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.lg\\\\:\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.lg\\\\:flex[_ngcontent-%COMP%]{display:flex}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:800px!important}.lg\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.lg\\\\:\\\\!px-4[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.lg\\\\:\\\\!px-\\\\__ph-0__[_ngcontent-%COMP%]{padding-left:22px!important;padding-right:22px!important}.lg\\\\:\\\\!py-\\\\__ph-0__[_ngcontent-%COMP%]{padding-top:18px!important;padding-bottom:18px!important}.lg\\\\:text-center[_ngcontent-%COMP%]{text-align:center}}\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MessageService", "PanelModule", "PanelMenuModule", "ToastModule", "DateFormatPipe", "LocalStorageService", "STORAGE_KEY", "ToastMessage", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "HistoryComponent_div_14_Template_div_drop_0_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onDragDrop", "HistoryComponent_div_14_Template_div_dragover_0_listener", "onDragOver", "HistoryComponent_div_14_Template_div_dragleave_0_listener", "onDragLeave", "HistoryComponent_div_14_Template_div_click_0_listener", "fileInput_r4", "ɵɵreference", "click", "ɵɵclassProp", "isDragOver", "ɵɵelementContainerStart", "HistoryComponent_div_17_ng_container_3_Template_img_click_3_listener", "_r5", "downloadFileWithoutRedirect", "houseHoldDetaiPicture", "ɵɵadvance", "ɵɵtextInterpolate1", "CName", "ɵɵtemplate", "HistoryComponent_div_17_ng_template_2_Template", "HistoryComponent_div_17_ng_container_3_Template", "ɵɵproperty", "isHaveFile", "ɵɵtextInterpolate", "ɵɵpipeBind1", "file_r6", "CChangeDate", "HistoryComponent_div_18_div_3_div_2_img_2_Template_img_click_0_listener", "_r7", "item_r8", "$implicit", "CFile", "CFileName", "HistoryComponent_div_18_div_3_div_2_img_2_Template", "HistoryComponent_div_18_div_3_div_1_Template", "HistoryComponent_div_18_div_3_div_2_Template", "SpecialChangeFiles", "length", "HistoryComponent_div_18_ng_template_2_Template", "HistoryComponent_div_18_div_3_Template", "listSpecialChange", "uploadedFiles", "HistoryComponent_div_19_div_5_Template_button_click_15_listener", "i_r11", "_r10", "index", "removeFile", "getFileIcon", "file_r12", "name", "ɵɵsanitizeUrl", "formatFileSize", "size", "getFileType", "HistoryComponent_div_19_ng_template_2_Template", "HistoryComponent_div_19_div_5_Template", "HistoryComponent_div_19_Template_button_click_7_listener", "_r9", "uploadFiles", "HistoryComponent_div_19_span_9_Template", "HistoryComponent_div_19_span_10_Template", "HistoryComponent_div_19_Template_button_click_11_listener", "clearFiles", "isUploading", "uploadError", "HistoryComponent", "constructor", "_houseService", "_specialChangeService", "_utilityService", "_toastService", "items", "specialChangeFiles", "dataUser", "GetLocalStorage", "SAVE_LOGIN", "maxFileSize", "allowedFileTypes", "allowedMimeTypes", "showUploadArea", "ngOnInit", "getHouseHoldDetaiPic", "buildCaseId", "holdDetailId", "getListSpecialChange", "apiSpecialChangeGetSpecialChangeFilePost$Json", "body", "subscribe", "res", "Entries", "StatusCode", "apiHouseGetHouseRegularPicturePost$Json", "CFileURL", "getFileNameFromUrl", "url", "parts", "split", "fileName", "pop", "files", "window", "open", "onFileSelected", "event", "input", "target", "handleFiles", "Array", "from", "preventDefault", "stopPropagation", "dataTransfer", "file", "validateFile", "some", "f", "push", "fileExtension", "toLowerCase", "includes", "splice", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "filePromises", "map", "convertFileToBase64", "Promise", "all", "then", "dataUrls", "dataUrl", "CFileBlood", "CFileType", "getFileTypeFromExtension", "apiSpecialChangeUploadSpecialChangePost$Json", "next", "response", "showSucessMSG", "error", "console", "showErrorMSG", "Message", "catch", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "base64", "onerror", "extension", "substring", "lastIndexOf", "toggleUploadArea", "_", "ɵɵdirectiveInject", "i1", "HouseService", "SpecialChangeService", "i2", "UtilityService", "i3", "_2", "selectors", "standalone", "features", "ɵɵProvidersFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HistoryComponent_Template", "rf", "ctx", "HistoryComponent_Template_button_click_9_listener", "_r1", "HistoryComponent_span_11_Template", "HistoryComponent_span_12_Template", "HistoryComponent_div_13_Template", "HistoryComponent_div_14_Template", "HistoryComponent_Template_input_change_15_listener", "HistoryComponent_div_17_Template", "HistoryComponent_div_18_Template", "HistoryComponent_div_19_Template", "HistoryComponent_div_20_Template", "i4", "RouterLink", "i5", "Panel", "i6", "PrimeTemplate", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i8", "Toast", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\history\\history.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\history\\history.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport { PanelModule } from 'primeng/panel';\r\nimport { PanelMenuModule } from 'primeng/panelmenu';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { HouseService, SpecialChangeService } from '../../../services/api/services';\r\nimport { SpecialChangeFileGroup, SpecialChangeFile } from '../../../services/api/models';\r\nimport { DateFormatPipe } from '../../shared/pipes/date-format.pipe';\r\nimport { LocalStorageService } from '../../shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from '../../shared/constant/constant';\r\nimport { UtilityService } from '../../shared/services/utility.service';\r\nimport { ToastMessage } from '../../shared/services/message.service';\r\n@Component({\r\n  selector: 'app-history',\r\n  standalone: true,\r\n  providers: [\r\n    MessageService,\r\n    ToastMessage,\r\n  ],\r\n  imports: [RouterModule, PanelModule, PanelMenuModule, CommonModule, DateFormatPipe, ToastModule],\r\n  templateUrl: './history.component.html',\r\n  styleUrl: './history.component.scss'\r\n})\r\nexport class HistoryComponent {\r\n  items: MenuItem[] = [];\r\n  houseHoldDetaiPicture: { CFile: string | any; CName: string | any } = { CFile: '', CName: '' }\r\n  specialChangeFiles: SpecialChangeFileGroup[] = []\r\n  dataUser = LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN) as any;\r\n  buildCaseId: any\r\n  holdDetailId: any\r\n  isHaveFile: boolean = false;\r\n\r\n  // 檔案上傳相關屬性\r\n  uploadedFiles: File[] = [];\r\n  isUploading: boolean = false;\r\n  uploadError: string = '';\r\n  maxFileSize: number = 10 * 1024 * 1024; // 10MB\r\n  allowedFileTypes: string[] = ['.pdf', '.dwg', '.dxf', '.dwf'];\r\n  allowedMimeTypes: string[] = ['application/pdf', 'image/vnd.dwg', 'application/acad', 'application/x-acad'];\r\n  isDragOver: boolean = false;\r\n  showUploadArea: boolean = false; // 控制上傳區域顯示\r\n\r\n  constructor(\r\n    private _houseService: HouseService,\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _utilityService: UtilityService, private _toastService: ToastMessage,\r\n  ) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getHouseHoldDetaiPic()\r\n    this.buildCaseId = this.dataUser.buildCaseId\r\n    this.holdDetailId = this.dataUser.holdDetailId\r\n    this.getListSpecialChange()\r\n  }\r\n\r\n  listSpecialChange: SpecialChangeFileGroup[] | any\r\n  getListSpecialChange() {\r\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeFilePost$Json({ body: this.buildCaseId }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChange = res.Entries! ?? []\r\n      }\r\n    })\r\n  }\r\n\r\n  getHouseHoldDetaiPic() {\r\n    this._houseService.apiHouseGetHouseRegularPicturePost$Json({}).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldDetaiPicture = {\r\n          CFile: res.Entries.CFileURL ? res.Entries.CFileURL : '',\r\n          CName: res.Entries.CFileName,\r\n        }\r\n        if (this.houseHoldDetaiPicture.CFile) {\r\n          this.isHaveFile = true;\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  getFileNameFromUrl(url: string) {\r\n    const parts = url.split('/');\r\n    const fileName = parts.pop();\r\n    return fileName;\r\n  }\r\n\r\n  downloadFileWithoutRedirect(files: { CFile: string | any; CName: string | any }) {\r\n    if (files.CFile) {\r\n      // this._utilityService.downloadFileFullUrl(files)\r\n      window.open(files.CFile, '_blank');\r\n    }\r\n  }\r\n\r\n  // 檔案選擇事件處理\r\n  onFileSelected(event: Event) {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files) {\r\n      this.handleFiles(Array.from(input.files));\r\n    }\r\n  }\r\n\r\n  // 拖拽事件處理\r\n  onDragOver(event: DragEvent) {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = true;\r\n  }\r\n\r\n  onDragLeave(event: DragEvent) {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = false;\r\n  }\r\n\r\n  onDragDrop(event: DragEvent) {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = false;\r\n\r\n    const files = event.dataTransfer?.files;\r\n    if (files) {\r\n      this.handleFiles(Array.from(files));\r\n    }\r\n  }\r\n\r\n  // 處理檔案\r\n  private handleFiles(files: File[]) {\r\n    this.uploadError = '';\r\n\r\n    for (const file of files) {\r\n      if (!this.validateFile(file)) {\r\n        continue;\r\n      }\r\n\r\n      // 檢查是否已存在相同檔案\r\n      if (!this.uploadedFiles.some(f => f.name === file.name)) {\r\n        this.uploadedFiles.push(file);\r\n      }\r\n    }\r\n  }\r\n\r\n  // 檔案驗證\r\n  private validateFile(file: File): boolean {\r\n    // 檢查檔案大小\r\n    if (file.size > this.maxFileSize) {\r\n      this.uploadError = `檔案 \"${file.name}\" 超過大小限制 (10MB)`;\r\n      return false;\r\n    }\r\n\r\n    // 檢查檔案類型\r\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\r\n    if (!this.allowedFileTypes.includes(fileExtension)) {\r\n      this.uploadError = `檔案 \"${file.name}\" 格式不支援，只支援 PDF、DWG、DXF、DWF 格式`;\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 移除檔案\r\n  removeFile(index: number) {\r\n    this.uploadedFiles.splice(index, 1);\r\n    this.uploadError = '';\r\n  }\r\n\r\n  // 格式化檔案大小\r\n  formatFileSize(bytes: number): string {\r\n    if (bytes === 0) return '0 Bytes';\r\n\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  }\r\n\r\n  // 上傳檔案\r\n  uploadFiles() {\r\n    if (this.uploadedFiles.length === 0) {\r\n      this.uploadError = '請選擇要上傳的檔案';\r\n      return;\r\n    }\r\n\r\n    this.isUploading = true;\r\n    this.uploadError = '';\r\n\r\n    // 將所有檔案轉換為 data URL 格式（包含 MIME 類型）\r\n    const filePromises = this.uploadedFiles.map(file => this.convertFileToBase64(file));\r\n\r\n    Promise.all(filePromises).then(dataUrls => {\r\n      // 組成 SpecialChangeFile 陣列\r\n      const specialChangeFiles: SpecialChangeFile[] = dataUrls.map((dataUrl, index) => ({\r\n        CFileBlood: dataUrl, // 完整的 data URL 格式，包含 MIME 類型\r\n        CFileName: this.uploadedFiles[index].name,\r\n        CFileType: this.getFileTypeFromExtension(this.uploadedFiles[index].name)\r\n      }));\r\n\r\n      // 調用 API\r\n      this._specialChangeService.apiSpecialChangeUploadSpecialChangePost$Json({ body: specialChangeFiles }).subscribe({\r\n        next: (response) => {\r\n          this.isUploading = false;\r\n          this.uploadedFiles = [];\r\n          this.showUploadArea = false; // 上傳成功後隱藏上傳區域\r\n          this.getListSpecialChange(); // 重新載入檔案列表\r\n\r\n          // 顯示成功訊息\r\n          this._toastService.showSucessMSG('檔案上傳成功');\r\n        },\r\n        error: (error) => {\r\n          this.isUploading = false;\r\n          this.uploadError = '上傳失敗，請稍後再試';\r\n          console.error('Upload error:', error);\r\n          this._toastService.showErrorMSG('檔案上傳失敗：' + (error.error?.Message || '未知錯誤'));\r\n        }\r\n      });\r\n    }).catch(error => {\r\n      this.isUploading = false;\r\n      this.uploadError = '檔案處理失敗';\r\n      console.error('File processing error:', error);\r\n      this._toastService.showErrorMSG('檔案處理失敗');\r\n    });\r\n  }\r\n\r\n  // 將檔案轉換為 base64 純字串（不含 data URL 前綴）\r\n  private convertFileToBase64(file: File): Promise<string> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        const result = reader.result as string;\r\n        // 只取 base64 部分\r\n        const base64 = result.split(',')[1];\r\n        resolve(base64);\r\n      };\r\n      reader.onerror = error => reject(error);\r\n    });\r\n  }\r\n\r\n  // 根據檔案副檔名判斷檔案類型\r\n  private getFileTypeFromExtension(fileName: string): number {\r\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\r\n    switch (extension) {\r\n      case '.pdf':\r\n        return 1; // 假設 1 代表 PDF\r\n      case '.dwg':\r\n        return 2; // 假設 2 代表 DWG\r\n      case '.dxf':\r\n        return 3; // 假設 3 代表 DXF\r\n      case '.dwf':\r\n        return 4; // 假設 4 代表 DWF\r\n      default:\r\n        return 0; // 未知類型\r\n    }\r\n  }\r\n\r\n  // 清除所有待上傳檔案\r\n  clearFiles() {\r\n    this.uploadedFiles = [];\r\n    this.uploadError = '';\r\n    this.showUploadArea = false; // 清除檔案時隱藏上傳區域\r\n  }\r\n\r\n  // 根據檔案名稱獲取對應圖標\r\n  getFileIcon(fileName: string): string {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    switch (extension) {\r\n      case 'pdf':\r\n        return 'assets/PDF.svg';\r\n      case 'dwg':\r\n      case 'dxf':\r\n      case 'dwf':\r\n        return 'assets/designFile.svg';\r\n      default:\r\n        return 'assets/PDF.svg';\r\n    }\r\n  }\r\n\r\n  // 根據檔案名稱獲取檔案類型顯示文字\r\n  getFileType(fileName: string): string {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    switch (extension) {\r\n      case 'pdf':\r\n        return 'PDF檔案';\r\n      case 'dwg':\r\n        return 'AutoCAD圖檔';\r\n      case 'dxf':\r\n        return 'CAD交換檔';\r\n      case 'dwf':\r\n        return 'CAD檢視檔';\r\n      default:\r\n        return '未知格式';\r\n    }\r\n  }\r\n\r\n  // 切換上傳區域顯示\r\n  toggleUploadArea() {\r\n    this.showUploadArea = !this.showUploadArea;\r\n    if (!this.showUploadArea) {\r\n      // 隱藏上傳區域時清除檔案和錯誤訊息\r\n      this.clearFiles();\r\n    }\r\n  }\r\n}\r\n", "<div class=\"wrapper\">\r\n  <!-- Toast 元件用於顯示通知訊息 -->\r\n  <p-toast></p-toast>\r\n\r\n  <div class=\"content\">\r\n    <div class=\"flex justify-center\">\r\n      <div class=\"history\">\r\n        <div class=\"history-header\">\r\n          <div class=\"title\">洽談紀錄</div>\r\n          <!-- 檔案上傳按鈕 - 右上角 -->\r\n          <div class=\"upload-button-container\">\r\n            <button class=\"button2 upload-button\" (click)=\"toggleUploadArea()\" [disabled]=\"isUploading\">\r\n              <i class=\"pi pi-upload\"></i>\r\n              <span *ngIf=\"!isUploading\">我要上傳</span>\r\n              <span *ngIf=\"isUploading\">處理中...</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 上傳說明區塊 -->\r\n        <div class=\"upload-instructions\" *ngIf=\"showUploadArea && !uploadedFiles.length\">\r\n          <div class=\"upload-hint-text-header\">\r\n            <i class=\"pi pi-info-circle\"></i>\r\n            支援 PDF、DWG、DXF、DWF 格式，檔案大小限制 10MB\r\n          </div>\r\n          <div class=\"upload-features\">\r\n            <div class=\"feature-item\">\r\n              <i class=\"pi pi-check-circle\"></i>\r\n              <span>支援多檔案同時選擇</span>\r\n            </div>\r\n            <div class=\"feature-item\">\r\n              <i class=\"pi pi-check-circle\"></i>\r\n              <span>拖拽檔案到此處上傳</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 拖拽上傳區域 -->\r\n        <div class=\"drag-drop-zone\" *ngIf=\"showUploadArea && !uploadedFiles.length\" (drop)=\"onDragDrop($event)\"\r\n          (dragover)=\"onDragOver($event)\" (dragleave)=\"onDragLeave($event)\" [class.drag-over]=\"isDragOver\"\r\n          (click)=\"fileInput.click()\">\r\n          <div class=\"drag-drop-content\">\r\n            <i class=\"pi pi-cloud-upload drag-icon\"></i>\r\n            <div class=\"drag-text-primary\">拖拽檔案到此處</div>\r\n            <div class=\"drag-text-secondary\">或點擊選擇多個檔案</div>\r\n            <div class=\"supported-formats\">\r\n              <span class=\"format-badge\">PDF</span>\r\n              <span class=\"format-badge\">DWG</span>\r\n              <span class=\"format-badge\">DXF</span>\r\n              <span class=\"format-badge\">DWF</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 隱藏的檔案輸入元素 -->\r\n        <input #fileInput type=\"file\" class=\"file-input-hidden\" accept=\".pdf,.dwg,.dxf,.dwf\" multiple\r\n          (change)=\"onFileSelected($event)\" style=\"display: none;\">\r\n        <div class=\"card flex justify-content-center mb-3\" *ngIf=\"isHaveFile\">\r\n          <p-panel header=\"標準圖面\" [toggleable]=\"true\" class=\"w-full\">\r\n            <ng-template pTemplate=\"headericons\">\r\n              <img class=\"black-arrow\" src=\"/assets/Vector.svg\" />\r\n            </ng-template>\r\n            <ng-container *ngIf=\"isHaveFile\">\r\n              <div class=\"pafbox\">{{houseHoldDetaiPicture.CName}}\r\n                <img src=\"assets/PDF.svg\" class=\"cursor-pointer\"\r\n                  (click)=\"downloadFileWithoutRedirect(houseHoldDetaiPicture)\">\r\n              </div>\r\n            </ng-container>\r\n          </p-panel>\r\n        </div>\r\n\r\n        <div class=\"card flex justify-content-center w-full\" *ngIf=\"listSpecialChange && listSpecialChange.length > 0\">\r\n          <p-panel header=\"客變圖面\" [toggleable]=\"true\" class=\"w-full\">\r\n            <ng-template pTemplate=\"headericons\">\r\n              <img class=\"black-arrow\" src=\"/assets/Vector.svg\" />\r\n            </ng-template>\r\n            <div class=\"pafdatebox\" *ngFor=\"let file of listSpecialChange\">\r\n              <div class=\"pafdate\" *ngIf=\"file.SpecialChangeFiles.length\">{{file.CChangeDate | dateFormat}}</div>\r\n              <div class=\"pafbox\" *ngFor=\"let item of file.SpecialChangeFiles\">\r\n                {{item.CFileName}}\r\n                <img src=\"assets/PDF.svg\" class=\"cursor-pointer\" *ngIf=\"item.CFile\" (click)=\"downloadFileWithoutRedirect({\r\n                  CFile: item.CFile || '',\r\n                  CName: item.CFileName || ''\r\n                })\">\r\n              </div>\r\n            </div>\r\n          </p-panel>\r\n        </div>\r\n\r\n        <!-- 上傳檔案列表 (當有檔案待上傳時顯示) -->\r\n        <div class=\"card flex justify-content-center w-full mb-3\" *ngIf=\"showUploadArea && uploadedFiles.length > 0\">\r\n          <p-panel [toggleable]=\"false\" class=\"w-full upload-panel\">\r\n            <ng-template pTemplate=\"header\">\r\n              <div class=\"upload-panel-header\">\r\n                <div class=\"upload-panel-title\">\r\n                  <i class=\"pi pi-cloud-upload\"></i>\r\n                  <span>待上傳檔案</span>\r\n                  <div class=\"file-count-badge\">{{uploadedFiles.length}}</div>\r\n                </div>\r\n                <div class=\"upload-panel-subtitle\">\r\n                  請確認以下檔案後點擊上傳\r\n                </div>\r\n              </div>\r\n            </ng-template>\r\n\r\n            <div class=\"uploaded-files-container\">\r\n              <div class=\"uploaded-files\">\r\n                <div class=\"uploaded-file-item\" *ngFor=\"let file of uploadedFiles; let i = index\">\r\n                  <div class=\"file-status-indicator\">\r\n                    <i class=\"pi pi-clock\" title=\"等待上傳\"></i>\r\n                  </div>\r\n                  <div class=\"file-info\">\r\n                    <div class=\"file-icon-wrapper\">\r\n                      <img [src]=\"getFileIcon(file.name)\" alt=\"File\" class=\"file-icon\">\r\n                    </div>\r\n                    <div class=\"file-details\">\r\n                      <span class=\"file-name\" [title]=\"file.name\">{{file.name}}</span>\r\n                      <div class=\"file-meta\">\r\n                        <span class=\"file-size\">{{formatFileSize(file.size)}}</span>\r\n                        <span class=\"file-type\">{{getFileType(file.name)}}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"file-actions\">\r\n                    <button class=\"delete-btn\" (click)=\"removeFile(i)\" title=\"移除檔案\">\r\n                      <i class=\"pi pi-times\"></i>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 確認上傳按鈕 -->\r\n            <div class=\"upload-actions\">\r\n              <button class=\"button1 confirm-upload-btn\" [disabled]=\"isUploading\" (click)=\"uploadFiles()\">\r\n                <i class=\"pi pi-check\"></i>\r\n                <span *ngIf=\"!isUploading\">確認上傳</span>\r\n                <span *ngIf=\"isUploading\">上傳中...</span>\r\n              </button>\r\n              <button class=\"button2 cancel-btn\" (click)=\"clearFiles()\" [disabled]=\"isUploading\">\r\n                <i class=\"pi pi-times\"></i>\r\n                取消\r\n              </button>\r\n            </div>\r\n          </p-panel>\r\n        </div>\r\n\r\n        <!-- 錯誤訊息 -->\r\n        <div class=\"error-message-container\" *ngIf=\"uploadError\">\r\n          <div class=\"error-message\">\r\n            <i class=\"pi pi-exclamation-triangle\"></i>\r\n            {{uploadError}}\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"flex justify-center\">\r\n          <div class=\"my-4 h-32\"> <button class=\"button1 !w-48\"><a routerLink=\"/\">返回主選單</a></button></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAmBC,cAAc,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAG3C,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,mBAAmB,QAAQ,6CAA6C;AACjF,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,SAASC,YAAY,QAAQ,uCAAuC;;;;;;;;;;;;ICAtDC,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtCH,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAO3CH,EADF,CAAAC,cAAA,cAAiF,cAC1C;IACnCD,EAAA,CAAAI,SAAA,YAAiC;IACjCJ,EAAA,CAAAE,MAAA,gHACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA6B,cACD;IACxBD,EAAA,CAAAI,SAAA,YAAkC;IAClCJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IACjBF,EADiB,CAAAG,YAAA,EAAO,EAClB;IACNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAI,SAAA,aAAkC;IAClCJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,8DAAS;IAGrBF,EAHqB,CAAAG,YAAA,EAAO,EAClB,EACF,EACF;;;;;;IAGNH,EAAA,CAAAC,cAAA,cAE8B;IAA5BD,EAF0E,CAAAK,UAAA,kBAAAC,qDAAAC,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAQF,MAAA,CAAAG,UAAA,CAAAN,MAAA,CAAkB;IAAA,EAAC,sBAAAO,yDAAAP,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CACzFF,MAAA,CAAAK,UAAA,CAAAR,MAAA,CAAkB;IAAA,EAAC,uBAAAS,0DAAAT,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAcF,MAAA,CAAAO,WAAA,CAAAV,MAAA,CAAmB;IAAA,EAAC,mBAAAW,sDAAA;MAAAlB,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAAT,EAAA,CAAAW,aAAA;MAAA,MAAAQ,YAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAY,WAAA,CACxDO,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAC3BrB,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAI,SAAA,YAA4C;IAC5CJ,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE9CH,EADF,CAAAC,cAAA,cAA+B,eACF;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAGpCF,EAHoC,CAAAG,YAAA,EAAO,EACjC,EACF,EACF;;;;IAb8DH,EAAA,CAAAsB,WAAA,cAAAZ,MAAA,CAAAa,UAAA,CAA8B;;;;;IAqB5FvB,EAAA,CAAAI,SAAA,cAAoD;;;;;;IAEtDJ,EAAA,CAAAwB,uBAAA,GAAiC;IAC/BxB,EAAA,CAAAC,cAAA,cAAoB;IAAAD,EAAA,CAAAE,MAAA,GAClB;IAAAF,EAAA,CAAAC,cAAA,cAC+D;IAA7DD,EAAA,CAAAK,UAAA,mBAAAoB,qEAAA;MAAAzB,EAAA,CAAAQ,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAiB,2BAAA,CAAAjB,MAAA,CAAAkB,qBAAA,CAAkD;IAAA,EAAC;IAChE5B,EAFE,CAAAG,YAAA,EAC+D,EAC3D;;;;;IAHcH,EAAA,CAAA6B,SAAA,GAClB;IADkB7B,EAAA,CAAA8B,kBAAA,KAAApB,MAAA,CAAAkB,qBAAA,CAAAG,KAAA,MAClB;;;;;IANN/B,EADF,CAAAC,cAAA,cAAsE,kBACV;IAIxDD,EAHA,CAAAgC,UAAA,IAAAC,8CAAA,0BAAqC,IAAAC,+CAAA,2BAGJ;IAOrClC,EADE,CAAAG,YAAA,EAAU,EACN;;;;IAXmBH,EAAA,CAAA6B,SAAA,EAAmB;IAAnB7B,EAAA,CAAAmC,UAAA,oBAAmB;IAIzBnC,EAAA,CAAA6B,SAAA,GAAgB;IAAhB7B,EAAA,CAAAmC,UAAA,SAAAzB,MAAA,CAAA0B,UAAA,CAAgB;;;;;IAY7BpC,EAAA,CAAAI,SAAA,cAAoD;;;;;IAGpDJ,EAAA,CAAAC,cAAA,cAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAiC;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAvCH,EAAA,CAAA6B,SAAA,EAAiC;IAAjC7B,EAAA,CAAAqC,iBAAA,CAAArC,EAAA,CAAAsC,WAAA,OAAAC,OAAA,CAAAC,WAAA,EAAiC;;;;;;IAG3FxC,EAAA,CAAAC,cAAA,cAGI;IAHgED,EAAA,CAAAK,UAAA,mBAAAoC,wEAAA;MAAAzC,EAAA,CAAAQ,aAAA,CAAAkC,GAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAW,aAAA,GAAAiC,SAAA;MAAA,MAAAlC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAiB,2BAAA,CAA4B;QAAAkB,KAAA,EAAAF,OAAA,CAAAE,KAAA,IACnF,EAAE;QAAAd,KAAA,EAAAY,OAAA,CAAAG,SAAA,IACC;MAAE,CAC7B,CAAC;IAAA,EAAI;IAHH9C,EAAA,CAAAG,YAAA,EAGI;;;;;IALNH,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAgC,UAAA,IAAAe,kDAAA,kBAGI;IACN/C,EAAA,CAAAG,YAAA,EAAM;;;;IALJH,EAAA,CAAA6B,SAAA,EACA;IADA7B,EAAA,CAAA8B,kBAAA,MAAAa,OAAA,CAAAG,SAAA,MACA;IAAkD9C,EAAA,CAAA6B,SAAA,EAAgB;IAAhB7B,EAAA,CAAAmC,UAAA,SAAAQ,OAAA,CAAAE,KAAA,CAAgB;;;;;IAJtE7C,EAAA,CAAAC,cAAA,cAA+D;IAE7DD,EADA,CAAAgC,UAAA,IAAAgB,4CAAA,kBAA4D,IAAAC,4CAAA,kBACK;IAOnEjD,EAAA,CAAAG,YAAA,EAAM;;;;IARkBH,EAAA,CAAA6B,SAAA,EAAoC;IAApC7B,EAAA,CAAAmC,UAAA,SAAAI,OAAA,CAAAW,kBAAA,CAAAC,MAAA,CAAoC;IACrBnD,EAAA,CAAA6B,SAAA,EAA0B;IAA1B7B,EAAA,CAAAmC,UAAA,YAAAI,OAAA,CAAAW,kBAAA,CAA0B;;;;;IANnElD,EADF,CAAAC,cAAA,cAA+G,kBACnD;IAIxDD,EAHA,CAAAgC,UAAA,IAAAoB,8CAAA,0BAAqC,IAAAC,sCAAA,kBAG0B;IAWnErD,EADE,CAAAG,YAAA,EAAU,EACN;;;;IAfmBH,EAAA,CAAA6B,SAAA,EAAmB;IAAnB7B,EAAA,CAAAmC,UAAA,oBAAmB;IAICnC,EAAA,CAAA6B,SAAA,GAAoB;IAApB7B,EAAA,CAAAmC,UAAA,YAAAzB,MAAA,CAAA4C,iBAAA,CAAoB;;;;;IAkBzDtD,EADF,CAAAC,cAAA,cAAiC,cACC;IAC9BD,EAAA,CAAAI,SAAA,YAAkC;IAClCJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClBH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IACxDF,EADwD,CAAAG,YAAA,EAAM,EACxD;IACNH,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAE,MAAA,iFACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAL4BH,EAAA,CAAA6B,SAAA,GAAwB;IAAxB7B,EAAA,CAAAqC,iBAAA,CAAA3B,MAAA,CAAA6C,aAAA,CAAAJ,MAAA,CAAwB;;;;;;IAWtDnD,EADF,CAAAC,cAAA,cAAkF,cAC7C;IACjCD,EAAA,CAAAI,SAAA,YAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAuB,cACU;IAC7BD,EAAA,CAAAI,SAAA,cAAiE;IACnEJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,eACoB;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE9DH,EADF,CAAAC,cAAA,cAAuB,gBACG;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAGxDF,EAHwD,CAAAG,YAAA,EAAO,EACrD,EACF,EACF;IAEJH,EADF,CAAAC,cAAA,eAA0B,kBACwC;IAArCD,EAAA,CAAAK,UAAA,mBAAAmD,gEAAA;MAAA,MAAAC,KAAA,GAAAzD,EAAA,CAAAQ,aAAA,CAAAkD,IAAA,EAAAC,KAAA;MAAA,MAAAjD,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAkD,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IAChDzD,EAAA,CAAAI,SAAA,aAA2B;IAGjCJ,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAfKH,EAAA,CAAA6B,SAAA,GAA8B;IAA9B7B,EAAA,CAAAmC,UAAA,QAAAzB,MAAA,CAAAmD,WAAA,CAAAC,QAAA,CAAAC,IAAA,GAAA/D,EAAA,CAAAgE,aAAA,CAA8B;IAGXhE,EAAA,CAAA6B,SAAA,GAAmB;IAAnB7B,EAAA,CAAAmC,UAAA,UAAA2B,QAAA,CAAAC,IAAA,CAAmB;IAAC/D,EAAA,CAAA6B,SAAA,EAAa;IAAb7B,EAAA,CAAAqC,iBAAA,CAAAyB,QAAA,CAAAC,IAAA,CAAa;IAE/B/D,EAAA,CAAA6B,SAAA,GAA6B;IAA7B7B,EAAA,CAAAqC,iBAAA,CAAA3B,MAAA,CAAAuD,cAAA,CAAAH,QAAA,CAAAI,IAAA,EAA6B;IAC7BlE,EAAA,CAAA6B,SAAA,GAA0B;IAA1B7B,EAAA,CAAAqC,iBAAA,CAAA3B,MAAA,CAAAyD,WAAA,CAAAL,QAAA,CAAAC,IAAA,EAA0B;;;;;IAiB1D/D,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtCH,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IA9C7CH,EADF,CAAAC,cAAA,cAA6G,kBACjD;IACxDD,EAAA,CAAAgC,UAAA,IAAAoC,8CAAA,0BAAgC;IAc9BpE,EADF,CAAAC,cAAA,cAAsC,cACR;IAC1BD,EAAA,CAAAgC,UAAA,IAAAqC,sCAAA,mBAAkF;IAuBtFrE,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,cAA4B,iBACkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAiE,yDAAA;MAAAtE,EAAA,CAAAQ,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8D,WAAA,EAAa;IAAA,EAAC;IACzFxE,EAAA,CAAAI,SAAA,YAA2B;IAE3BJ,EADA,CAAAgC,UAAA,IAAAyC,uCAAA,mBAA2B,KAAAC,wCAAA,mBACD;IAC5B1E,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAmF;IAAhDD,EAAA,CAAAK,UAAA,mBAAAsE,0DAAA;MAAA3E,EAAA,CAAAQ,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAkE,UAAA,EAAY;IAAA,EAAC;IACvD5E,EAAA,CAAAI,SAAA,aAA2B;IAC3BJ,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACE,EACN;;;;IAtDKH,EAAA,CAAA6B,SAAA,EAAoB;IAApB7B,EAAA,CAAAmC,UAAA,qBAAoB;IAgB0BnC,EAAA,CAAA6B,SAAA,GAAkB;IAAlB7B,EAAA,CAAAmC,UAAA,YAAAzB,MAAA,CAAA6C,aAAA,CAAkB;IA2B1BvD,EAAA,CAAA6B,SAAA,GAAwB;IAAxB7B,EAAA,CAAAmC,UAAA,aAAAzB,MAAA,CAAAmE,WAAA,CAAwB;IAE1D7E,EAAA,CAAA6B,SAAA,GAAkB;IAAlB7B,EAAA,CAAAmC,UAAA,UAAAzB,MAAA,CAAAmE,WAAA,CAAkB;IAClB7E,EAAA,CAAA6B,SAAA,EAAiB;IAAjB7B,EAAA,CAAAmC,UAAA,SAAAzB,MAAA,CAAAmE,WAAA,CAAiB;IAEgC7E,EAAA,CAAA6B,SAAA,EAAwB;IAAxB7B,EAAA,CAAAmC,UAAA,aAAAzB,MAAA,CAAAmE,WAAA,CAAwB;;;;;IAUtF7E,EADF,CAAAC,cAAA,cAAyD,cAC5B;IACzBD,EAAA,CAAAI,SAAA,YAA0C;IAC1CJ,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAA8B,kBAAA,MAAApB,MAAA,CAAAoE,WAAA,MACF;;;AD/HV,OAAM,MAAOC,gBAAgB;EAmB3BC,YACUC,aAA2B,EAC3BC,qBAA2C,EAC3CC,eAA+B,EAAUC,aAA2B;IAFpE,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,eAAe,GAAfA,eAAe;IAA0B,KAAAC,aAAa,GAAbA,aAAa;IArBhE,KAAAC,KAAK,GAAe,EAAE;IACtB,KAAAzD,qBAAqB,GAAiD;MAAEiB,KAAK,EAAE,EAAE;MAAEd,KAAK,EAAE;IAAE,CAAE;IAC9F,KAAAuD,kBAAkB,GAA6B,EAAE;IACjD,KAAAC,QAAQ,GAAG1F,mBAAmB,CAAC2F,eAAe,CAAC1F,WAAW,CAAC2F,UAAU,CAAQ;IAG7E,KAAArD,UAAU,GAAY,KAAK;IAE3B;IACA,KAAAmB,aAAa,GAAW,EAAE;IAC1B,KAAAsB,WAAW,GAAY,KAAK;IAC5B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAY,WAAW,GAAW,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACxC,KAAAC,gBAAgB,GAAa,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC7D,KAAAC,gBAAgB,GAAa,CAAC,iBAAiB,EAAE,eAAe,EAAE,kBAAkB,EAAE,oBAAoB,CAAC;IAC3G,KAAArE,UAAU,GAAY,KAAK;IAC3B,KAAAsE,cAAc,GAAY,KAAK,CAAC,CAAC;EAOjC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,WAAW,GAAG,IAAI,CAACT,QAAQ,CAACS,WAAW;IAC5C,IAAI,CAACC,YAAY,GAAG,IAAI,CAACV,QAAQ,CAACU,YAAY;IAC9C,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAGAA,oBAAoBA,CAAA;IAClB,IAAI,CAAChB,qBAAqB,CAACiB,6CAA6C,CAAC;MAAEC,IAAI,EAAE,IAAI,CAACJ;IAAW,CAAE,CAAC,CAACK,SAAS,CAACC,GAAG,IAAG;MACnH,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAClD,iBAAiB,GAAGgD,GAAG,CAACC,OAAQ,IAAI,EAAE;;IAE/C,CAAC,CAAC;EACJ;EAEAR,oBAAoBA,CAAA;IAClB,IAAI,CAACd,aAAa,CAACwB,uCAAuC,CAAC,EAAE,CAAC,CAACJ,SAAS,CAACC,GAAG,IAAG;MAC7E,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC5E,qBAAqB,GAAG;UAC3BiB,KAAK,EAAEyD,GAAG,CAACC,OAAO,CAACG,QAAQ,GAAGJ,GAAG,CAACC,OAAO,CAACG,QAAQ,GAAG,EAAE;UACvD3E,KAAK,EAAEuE,GAAG,CAACC,OAAO,CAACzD;SACpB;QACD,IAAI,IAAI,CAAClB,qBAAqB,CAACiB,KAAK,EAAE;UACpC,IAAI,CAACT,UAAU,GAAG,IAAI;;;IAG5B,CAAC,CAAC;EACJ;EAEAuE,kBAAkBA,CAACC,GAAW;IAC5B,MAAMC,KAAK,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC;IAC5B,MAAMC,QAAQ,GAAGF,KAAK,CAACG,GAAG,EAAE;IAC5B,OAAOD,QAAQ;EACjB;EAEApF,2BAA2BA,CAACsF,KAAmD;IAC7E,IAAIA,KAAK,CAACpE,KAAK,EAAE;MACf;MACAqE,MAAM,CAACC,IAAI,CAACF,KAAK,CAACpE,KAAK,EAAE,QAAQ,CAAC;;EAEtC;EAEA;EACAuE,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACL,KAAK,EAAE;MACf,IAAI,CAACO,WAAW,CAACC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAACL,KAAK,CAAC,CAAC;;EAE7C;EAEA;EACAlG,UAAUA,CAACsG,KAAgB;IACzBA,KAAK,CAACM,cAAc,EAAE;IACtBN,KAAK,CAACO,eAAe,EAAE;IACvB,IAAI,CAACrG,UAAU,GAAG,IAAI;EACxB;EAEAN,WAAWA,CAACoG,KAAgB;IAC1BA,KAAK,CAACM,cAAc,EAAE;IACtBN,KAAK,CAACO,eAAe,EAAE;IACvB,IAAI,CAACrG,UAAU,GAAG,KAAK;EACzB;EAEAV,UAAUA,CAACwG,KAAgB;IACzBA,KAAK,CAACM,cAAc,EAAE;IACtBN,KAAK,CAACO,eAAe,EAAE;IACvB,IAAI,CAACrG,UAAU,GAAG,KAAK;IAEvB,MAAM0F,KAAK,GAAGI,KAAK,CAACQ,YAAY,EAAEZ,KAAK;IACvC,IAAIA,KAAK,EAAE;MACT,IAAI,CAACO,WAAW,CAACC,KAAK,CAACC,IAAI,CAACT,KAAK,CAAC,CAAC;;EAEvC;EAEA;EACQO,WAAWA,CAACP,KAAa;IAC/B,IAAI,CAACnC,WAAW,GAAG,EAAE;IAErB,KAAK,MAAMgD,IAAI,IAAIb,KAAK,EAAE;MACxB,IAAI,CAAC,IAAI,CAACc,YAAY,CAACD,IAAI,CAAC,EAAE;QAC5B;;MAGF;MACA,IAAI,CAAC,IAAI,CAACvE,aAAa,CAACyE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClE,IAAI,KAAK+D,IAAI,CAAC/D,IAAI,CAAC,EAAE;QACvD,IAAI,CAACR,aAAa,CAAC2E,IAAI,CAACJ,IAAI,CAAC;;;EAGnC;EAEA;EACQC,YAAYA,CAACD,IAAU;IAC7B;IACA,IAAIA,IAAI,CAAC5D,IAAI,GAAG,IAAI,CAACwB,WAAW,EAAE;MAChC,IAAI,CAACZ,WAAW,GAAG,OAAOgD,IAAI,CAAC/D,IAAI,iBAAiB;MACpD,OAAO,KAAK;;IAGd;IACA,MAAMoE,aAAa,GAAG,GAAG,GAAGL,IAAI,CAAC/D,IAAI,CAAC+C,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,EAAE,EAAEoB,WAAW,EAAE;IACrE,IAAI,CAAC,IAAI,CAACzC,gBAAgB,CAAC0C,QAAQ,CAACF,aAAa,CAAC,EAAE;MAClD,IAAI,CAACrD,WAAW,GAAG,OAAOgD,IAAI,CAAC/D,IAAI,gCAAgC;MACnE,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEA;EACAH,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACJ,aAAa,CAAC+E,MAAM,CAAC3E,KAAK,EAAE,CAAC,CAAC;IACnC,IAAI,CAACmB,WAAW,GAAG,EAAE;EACvB;EAEA;EACAb,cAAcA,CAACsE,KAAa;IAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IAEjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IAEnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE;EAEA;EACAlE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACjB,aAAa,CAACJ,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAAC2B,WAAW,GAAG,WAAW;MAC9B;;IAGF,IAAI,CAACD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,WAAW,GAAG,EAAE;IAErB;IACA,MAAMmE,YAAY,GAAG,IAAI,CAAC1F,aAAa,CAAC2F,GAAG,CAACpB,IAAI,IAAI,IAAI,CAACqB,mBAAmB,CAACrB,IAAI,CAAC,CAAC;IAEnFsB,OAAO,CAACC,GAAG,CAACJ,YAAY,CAAC,CAACK,IAAI,CAACC,QAAQ,IAAG;MACxC;MACA,MAAMjE,kBAAkB,GAAwBiE,QAAQ,CAACL,GAAG,CAAC,CAACM,OAAO,EAAE7F,KAAK,MAAM;QAChF8F,UAAU,EAAED,OAAO;QACnB1G,SAAS,EAAE,IAAI,CAACS,aAAa,CAACI,KAAK,CAAC,CAACI,IAAI;QACzC2F,SAAS,EAAE,IAAI,CAACC,wBAAwB,CAAC,IAAI,CAACpG,aAAa,CAACI,KAAK,CAAC,CAACI,IAAI;OACxE,CAAC,CAAC;MAEH;MACA,IAAI,CAACmB,qBAAqB,CAAC0E,4CAA4C,CAAC;QAAExD,IAAI,EAAEd;MAAkB,CAAE,CAAC,CAACe,SAAS,CAAC;QAC9GwD,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACjF,WAAW,GAAG,KAAK;UACxB,IAAI,CAACtB,aAAa,GAAG,EAAE;UACvB,IAAI,CAACsC,cAAc,GAAG,KAAK,CAAC,CAAC;UAC7B,IAAI,CAACK,oBAAoB,EAAE,CAAC,CAAC;UAE7B;UACA,IAAI,CAACd,aAAa,CAAC2E,aAAa,CAAC,QAAQ,CAAC;QAC5C,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnF,WAAW,GAAG,KAAK;UACxB,IAAI,CAACC,WAAW,GAAG,YAAY;UAC/BmF,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACrC,IAAI,CAAC5E,aAAa,CAAC8E,YAAY,CAAC,SAAS,IAAIF,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,MAAM,CAAC,CAAC;QAC/E;OACD,CAAC;IACJ,CAAC,CAAC,CAACC,KAAK,CAACJ,KAAK,IAAG;MACf,IAAI,CAACnF,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,WAAW,GAAG,QAAQ;MAC3BmF,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,IAAI,CAAC5E,aAAa,CAAC8E,YAAY,CAAC,QAAQ,CAAC;IAC3C,CAAC,CAAC;EACJ;EAEA;EACQf,mBAAmBA,CAACrB,IAAU;IACpC,OAAO,IAAIsB,OAAO,CAAC,CAACiB,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,aAAa,CAAC3C,IAAI,CAAC;MAC1ByC,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,MAAMC,MAAM,GAAGJ,MAAM,CAACI,MAAgB;QACtC;QACA,MAAMC,MAAM,GAAGD,MAAM,CAAC7D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnCuD,OAAO,CAACO,MAAM,CAAC;MACjB,CAAC;MACDL,MAAM,CAACM,OAAO,GAAGb,KAAK,IAAIM,MAAM,CAACN,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ;EAEA;EACQL,wBAAwBA,CAAC5C,QAAgB;IAC/C,MAAM+D,SAAS,GAAG/D,QAAQ,CAACqB,WAAW,EAAE,CAAC2C,SAAS,CAAChE,QAAQ,CAACiE,WAAW,CAAC,GAAG,CAAC,CAAC;IAC7E,QAAQF,SAAS;MACf,KAAK,MAAM;QACT,OAAO,CAAC;MAAE;MACZ,KAAK,MAAM;QACT,OAAO,CAAC;MAAE;MACZ,KAAK,MAAM;QACT,OAAO,CAAC;MAAE;MACZ,KAAK,MAAM;QACT,OAAO,CAAC;MAAE;MACZ;QACE,OAAO,CAAC;MAAE;;EAEhB;EAEA;EACAlG,UAAUA,CAAA;IACR,IAAI,CAACrB,aAAa,GAAG,EAAE;IACvB,IAAI,CAACuB,WAAW,GAAG,EAAE;IACrB,IAAI,CAACe,cAAc,GAAG,KAAK,CAAC,CAAC;EAC/B;EAEA;EACAhC,WAAWA,CAACkD,QAAgB;IAC1B,MAAM+D,SAAS,GAAG/D,QAAQ,CAACD,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,EAAE,EAAEoB,WAAW,EAAE;IAC1D,QAAQ0C,SAAS;MACf,KAAK,KAAK;QACR,OAAO,gBAAgB;MACzB,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,KAAK;QACR,OAAO,uBAAuB;MAChC;QACE,OAAO,gBAAgB;;EAE7B;EAEA;EACA3G,WAAWA,CAAC4C,QAAgB;IAC1B,MAAM+D,SAAS,GAAG/D,QAAQ,CAACD,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,EAAE,EAAEoB,WAAW,EAAE;IAC1D,QAAQ0C,SAAS;MACf,KAAK,KAAK;QACR,OAAO,OAAO;MAChB,KAAK,KAAK;QACR,OAAO,WAAW;MACpB,KAAK,KAAK;QACR,OAAO,QAAQ;MACjB,KAAK,KAAK;QACR,OAAO,QAAQ;MACjB;QACE,OAAO,MAAM;;EAEnB;EAEA;EACAG,gBAAgBA,CAAA;IACd,IAAI,CAACpF,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,CAAC,IAAI,CAACA,cAAc,EAAE;MACxB;MACA,IAAI,CAACjB,UAAU,EAAE;;EAErB;EAAC,QAAAsG,CAAA,G;qBArRUnG,gBAAgB,EAAA/E,EAAA,CAAAmL,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAArL,EAAA,CAAAmL,iBAAA,CAAAC,EAAA,CAAAE,oBAAA,GAAAtL,EAAA,CAAAmL,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAxL,EAAA,CAAAmL,iBAAA,CAAAM,EAAA,CAAA1L,YAAA;EAAA;EAAA,QAAA2L,EAAA,G;UAAhB3G,gBAAgB;IAAA4G,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA7L,EAAA,CAAA8L,kBAAA,CARhB,CACTtM,cAAc,EACdO,YAAY,CACb,GAAAC,EAAA,CAAA+L,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCpBHrM,EAAA,CAAAC,cAAA,aAAqB;QAEnBD,EAAA,CAAAI,SAAA,cAAmB;QAMXJ,EAJR,CAAAC,cAAA,aAAqB,aACc,aACV,aACS,aACP;QAAAD,EAAA,CAAAE,MAAA,+BAAI;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAG3BH,EADF,CAAAC,cAAA,aAAqC,gBACyD;QAAtDD,EAAA,CAAAK,UAAA,mBAAAkM,kDAAA;UAAAvM,EAAA,CAAAQ,aAAA,CAAAgM,GAAA;UAAA,OAAAxM,EAAA,CAAAY,WAAA,CAAS0L,GAAA,CAAArB,gBAAA,EAAkB;QAAA,EAAC;QAChEjL,EAAA,CAAAI,SAAA,YAA4B;QAE5BJ,EADA,CAAAgC,UAAA,KAAAyK,iCAAA,mBAA2B,KAAAC,iCAAA,mBACD;QAGhC1M,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;QAqBNH,EAlBA,CAAAgC,UAAA,KAAA2K,gCAAA,mBAAiF,KAAAC,gCAAA,mBAoBnD;QAe9B5M,EAAA,CAAAC,cAAA,oBAC2D;QAAzDD,EAAA,CAAAK,UAAA,oBAAAwM,mDAAAtM,MAAA;UAAAP,EAAA,CAAAQ,aAAA,CAAAgM,GAAA;UAAA,OAAAxM,EAAA,CAAAY,WAAA,CAAU0L,GAAA,CAAAlF,cAAA,CAAA7G,MAAA,CAAsB;QAAA,EAAC;QADnCP,EAAA,CAAAG,YAAA,EAC2D;QA4F3DH,EA3FA,CAAAgC,UAAA,KAAA8K,gCAAA,kBAAsE,KAAAC,gCAAA,kBAcyC,KAAAC,gCAAA,mBAmBF,KAAAC,gCAAA,kBA0DpD;QAQDjN,EADxD,CAAAC,cAAA,cAAiC,eACR,kBAA+B,aAAkB;QAAAD,EAAA,CAAAE,MAAA,sCAAK;QAKvFF,EALuF,CAAAG,YAAA,EAAI,EAAS,EAAM,EAC5F,EACF,EACF,EACF,EACF;;;QAtJyEH,EAAA,CAAA6B,SAAA,GAAwB;QAAxB7B,EAAA,CAAAmC,UAAA,aAAAmK,GAAA,CAAAzH,WAAA,CAAwB;QAElF7E,EAAA,CAAA6B,SAAA,GAAkB;QAAlB7B,EAAA,CAAAmC,UAAA,UAAAmK,GAAA,CAAAzH,WAAA,CAAkB;QAClB7E,EAAA,CAAA6B,SAAA,EAAiB;QAAjB7B,EAAA,CAAAmC,UAAA,SAAAmK,GAAA,CAAAzH,WAAA,CAAiB;QAMI7E,EAAA,CAAA6B,SAAA,EAA6C;QAA7C7B,EAAA,CAAAmC,UAAA,SAAAmK,GAAA,CAAAzG,cAAA,KAAAyG,GAAA,CAAA/I,aAAA,CAAAJ,MAAA,CAA6C;QAkBlDnD,EAAA,CAAA6B,SAAA,EAA6C;QAA7C7B,EAAA,CAAAmC,UAAA,SAAAmK,GAAA,CAAAzG,cAAA,KAAAyG,GAAA,CAAA/I,aAAA,CAAAJ,MAAA,CAA6C;QAmBtBnD,EAAA,CAAA6B,SAAA,GAAgB;QAAhB7B,EAAA,CAAAmC,UAAA,SAAAmK,GAAA,CAAAlK,UAAA,CAAgB;QAcdpC,EAAA,CAAA6B,SAAA,EAAuD;QAAvD7B,EAAA,CAAAmC,UAAA,SAAAmK,GAAA,CAAAhJ,iBAAA,IAAAgJ,GAAA,CAAAhJ,iBAAA,CAAAH,MAAA,KAAuD;QAmBlDnD,EAAA,CAAA6B,SAAA,EAAgD;QAAhD7B,EAAA,CAAAmC,UAAA,SAAAmK,GAAA,CAAAzG,cAAA,IAAAyG,GAAA,CAAA/I,aAAA,CAAAJ,MAAA,KAAgD;QA0DrEnD,EAAA,CAAA6B,SAAA,EAAiB;QAAjB7B,EAAA,CAAAmC,UAAA,SAAAmK,GAAA,CAAAxH,WAAA,CAAiB;;;mBD/HnDvF,YAAY,EAAA2N,EAAA,CAAAC,UAAA,EAAE1N,WAAW,EAAA2N,EAAA,CAAAC,KAAA,EAAAC,EAAA,CAAAC,aAAA,EAAE7N,eAAe,EAAEJ,YAAY,EAAAkO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE9N,cAAc,EAAED,WAAW,EAAAgO,EAAA,CAAAC,KAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}