{"ast": null, "code": "import { FormsModule } from '@angular/forms';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { ToastMessage } from '../../shared/services/message.service';\nimport { MessageService } from 'primeng/api';\nimport { ToastModule } from 'primeng/toast';\nimport { HttpClientModule } from '@angular/common/http';\nimport { LocalStorageService } from '../../shared/services/local-storage.service';\nimport { STORAGE_KEY } from '../../shared/constant/constant';\nimport { concatMap, finalize, mergeMap, of, tap } from 'rxjs';\nimport { LoadingService } from '../../shared/services/loading.service';\nimport { RecaptchaFormsModule, RecaptchaModule } from 'ng-recaptcha';\nimport { environment } from \"../../../environments/environment\";\nimport { BaseFilePipe } from '../../shared/pipes/base-file.pipe';\nimport { CommonModule } from '@angular/common';\nimport { EEvent } from '../../shared/services/event.service';\nimport { NgxTurnstileModule } from 'ngx-turnstile';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../shared/helper/validationHelper\";\nimport * as i3 from \"../../shared/services/message.service\";\nimport * as i4 from \"../../../services/api/services\";\nimport * as i5 from \"../../shared/helper/petternHelper\";\nimport * as i6 from \"../../shared/services/event.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/checkbox\";\nimport * as i12 from \"primeng/toast\";\nconst _c0 = [\"turnstileRef\"];\nconst _c1 = () => ({\n  \"width\": \"22rem\"\n});\nconst _c2 = () => ({\n  \"width.px\": 480,\n  \"height.px\": 360\n});\nconst _c3 = () => ({\n  \"width.px\": 360,\n  \"height.px\": 480,\n  \"margin-left.px\": 100\n});\nfunction LoginComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"img\", 9);\n    i0.ɵɵpipe(2, \"addBaseFile\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 2, ctx_r0.selectedBuildCase.CFrontImage), i0.ɵɵsanitizeUrl)(\"ngStyle\", ctx_r0.isHorizontal ? i0.ɵɵpureFunction0(4, _c2) : i0.ɵɵpureFunction0(5, _c3));\n  }\n}\nfunction LoginComponent_Conditional_7_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dropdown\", 28);\n    i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Conditional_7_Conditional_5_Template_p_dropdown_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.changeBuildCase($event));\n    })(\"onChange\", function LoginComponent_Conditional_7_Conditional_5_Template_p_dropdown_onChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onImageChange($event));\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Conditional_7_Conditional_5_Template_p_dropdown_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectedBuildCase, $event) || (ctx_r0.selectedBuildCase = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r0.listBuildCases);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.selectedBuildCase);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.listBuildCases[0].CBuildCaseName);\n  }\n}\nfunction LoginComponent_Conditional_7_Conditional_6_Template(rf, ctx) {}\nfunction LoginComponent_Conditional_7_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dropdown\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Conditional_7_Conditional_12_Template_p_dropdown_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectedHouse, $event) || (ctx_r0.selectedHouse = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Conditional_7_Conditional_12_Template_p_dropdown_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.getFloorFrom1toHighest($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r0.listHouseAndFloors);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.selectedHouse);\n  }\n}\nfunction LoginComponent_Conditional_7_Conditional_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dropdown\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Conditional_7_Conditional_18_Template_p_dropdown_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectedFloor, $event) || (ctx_r0.selectedFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r0.listFloors);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.selectedFloor);\n  }\n}\nfunction LoginComponent_Conditional_7__svg_svg_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 31);\n    i0.ɵɵelement(1, \"path\", 32)(2, \"path\", 33);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Conditional_7__svg_svg_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 31);\n    i0.ɵɵelement(1, \"path\", 34);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"span\", 11);\n    i0.ɵɵtext(2, \" \\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementStart(3, \"span\", 12);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, LoginComponent_Conditional_7_Conditional_5_Template, 1, 3, \"p-dropdown\", 13)(6, LoginComponent_Conditional_7_Conditional_6_Template, 0, 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 10)(8, \"span\", 11);\n    i0.ɵɵtext(9, \" \\u6236\\u5225 \");\n    i0.ɵɵelementStart(10, \"span\", 12);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, LoginComponent_Conditional_7_Conditional_12_Template, 1, 2, \"p-dropdown\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 10)(14, \"span\", 15);\n    i0.ɵɵtext(15, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelementStart(16, \"span\", 12);\n    i0.ɵɵtext(17, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, LoginComponent_Conditional_7_Conditional_18_Template, 1, 2, \"p-dropdown\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 17)(20, \"span\", 11);\n    i0.ɵɵtext(21, \" \\u5BC6\\u78BC \");\n    i0.ɵɵelementStart(22, \"span\", 18);\n    i0.ɵɵtext(23, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 19)(25, \"input\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Conditional_7_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.numberId, $event) || (ctx_r0.numberId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Conditional_7_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.togglePasswordVisibility());\n    });\n    i0.ɵɵtemplate(27, LoginComponent_Conditional_7__svg_svg_27_Template, 3, 0, \"svg\", 22)(28, LoginComponent_Conditional_7__svg_svg_28_Template, 2, 0, \"svg\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 23)(30, \"div\", 24)(31, \"p-checkbox\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Conditional_7_Template_p_checkbox_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.saveLogin, $event) || (ctx_r0.saveLogin = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 26)(33, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Conditional_7_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.login());\n    });\n    i0.ɵɵtext(34, \" \\u767B\\u5165 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵconditional(5, !!ctx_r0.listBuildCases && ctx_r0.listBuildCases.length > 0 ? 5 : 6);\n    i0.ɵɵadvance(7);\n    i0.ɵɵconditional(12, !!ctx_r0.listHouseAndFloors && ctx_r0.listHouseAndFloors.length > 0 ? 12 : -1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵconditional(18, !!ctx_r0.listHouseAndFloors && ctx_r0.listHouseAndFloors.length > 0 ? 18 : -1);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"type\", ctx_r0.passwordFieldType);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.numberId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.passwordFieldType === \"text\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.passwordFieldType === \"password\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"binary\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.saveLogin);\n  }\n}\nfunction LoginComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"span\", 37);\n    i0.ɵɵtext(3, \" \\u8ACB\\u66F4\\u65B0\\u60A8\\u7684\\u806F\\u7D61\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 38)(5, \"span\", 39);\n    i0.ɵɵtext(6, \" \\u806F\\u7D61\\u96FB\\u8A71 \");\n    i0.ɵɵelementStart(7, \"span\", 40);\n    i0.ɵɵtext(8, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"input\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Conditional_8_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.CPhone, $event) || (ctx_r0.CPhone = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function LoginComponent_Conditional_8_Template_input_keydown_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.disableNegative($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 42)(11, \"span\", 39);\n    i0.ɵɵtext(12, \" Email \");\n    i0.ɵɵelementStart(13, \"span\", 40);\n    i0.ɵɵtext(14, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"input\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Conditional_8_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.CMail, $event) || (ctx_r0.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 44)(17, \"span\", 45);\n    i0.ɵɵtext(18, \"*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"\\u7559\\u4E0B\\u60A8\\u7684\\u806F\\u7D61\\u8CC7\\u8A0A\\uFF0C\\u70BA\\u60A8\\u63D0\\u4F9B\\u5C08\\u5C6C\\u7684\\u5F8C\\u7E8C\\u670D\\u52D9\\uFF0C\\u6211\\u5011\\u6703\\u8207\\u60A8\\u806F\\u7E6B\\uFF01\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 46)(22, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Conditional_8_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.loginStep2());\n    });\n    i0.ɵɵtext(23, \" \\u4E0B\\u4E00\\u6B65\\uFF0C\\u9032\\u884C\\u5BA2\\u6236\\u9078\\u6A23 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Conditional_8_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.loginStep2());\n    });\n    i0.ɵɵtext(25, \" \\u5132\\u5B58 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.CPhone);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.CMail);\n  }\n}\nexport class LoginComponent {\n  togglePasswordVisibility() {\n    this.passwordFieldType = this.passwordFieldType === 'password' ? 'text' : 'password';\n  }\n  constructor(_router, valid, _toastService, _houseService, _buildCaseService, pettern, _eventService) {\n    this._router = _router;\n    this.valid = valid;\n    this._toastService = _toastService;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.pettern = pettern;\n    this._eventService = _eventService;\n    // readonly SITE_KEY = environment.SITE_KEY_RECAPTCHA;\n    this.SITE_KEY = environment.SITE_KEY_CLOUDFLARE_RECAPTCHA ? environment.SITE_KEY_CLOUDFLARE_RECAPTCHA : \"\";\n    this.listBuildCases = [];\n    this.listHouseAndFloors = [];\n    this.listFloors = [];\n    this.numberId = \"\";\n    this.saveLogin = false;\n    this.isHorizontal = true;\n    // resolved(captchaResponse: any) {\n    //   console.log({ recaptcha: captchaResponse });\n    // }\n    this.isRedirectToHome = true;\n    this.CPhone = \"\";\n    this.CMail = \"\";\n    this.captchaToken = null;\n    this.passwordFieldType = 'password';\n    let version = LocalStorageService.GetLocalStorage(STORAGE_KEY.VERSION);\n    if (!version || version !== '0.1.1') {\n      LocalStorageService.ClearLocalStorage();\n    } else {\n      LocalStorageService.RemoveLocalStorage(STORAGE_KEY.DATE_END);\n      LocalStorageService.RemoveLocalStorage(STORAGE_KEY.DATE_START);\n      LocalStorageService.RemoveLocalStorage(STORAGE_KEY.TOKEN);\n      LocalStorageService.RemoveLocalStorage(STORAGE_KEY.USER);\n    }\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  onExpired() {\n    console.warn('Turnstile expired – resetting...');\n    this.turnstile.reset();\n  }\n  onError() {\n    console.warn('Turnstile error – resetting...');\n    this.turnstile.reset();\n  }\n  sendCaptchaResponse(captchaResponse) {\n    this.captchaToken = captchaResponse;\n    console.log(`Resolved captcha with response: ${captchaResponse}`);\n  }\n  checkImageOrientation() {\n    if (!this.selectedBuildCase?.CFrontImage) return;\n    const img = new Image();\n    img.src = `${environment.BASE_URL_API}${this.selectedBuildCase?.CFrontImage}`;\n    img.onload = () => {\n      this.isHorizontal = img.width > img.height;\n    };\n    img.onerror = () => console.error(\"Unable to load image from URL\");\n  }\n  onImageChange(e) {\n    this.checkImageOrientation();\n  }\n  getListBuildCase() {\n    LoadingService.loading(true);\n    this._buildCaseService.apiBuildCaseGetBuildCaseListPost$Json().pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.listBuildCases = res.Entries;\n          if (LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN)) {\n            this.saveLogin = LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).isSaveLogin;\n            if (this.saveLogin) {\n              this.selectedBuildCase = this.listBuildCases.filter(x => x.cID === LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).buildCaseId)[0];\n              if (!this.selectedBuildCase) {\n                this.selectedBuildCase = res.Entries[0];\n              }\n            } else {\n              this.selectedBuildCase = res.Entries[0];\n            }\n          } else {\n            this.selectedBuildCase = res.Entries[0];\n          }\n          this.checkImageOrientation();\n        }\n      }\n    }), mergeMap(() => this.selectedBuildCase ? this.fetchBuildCase(this.selectedBuildCase.cID) : \"0\")).subscribe(res => {\n      if (res != \"0\") {\n        if (LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN)) {\n          if (this.saveLogin) {\n            this.selectedHouse = this.listHouseAndFloors.filter(x => x.CHouseHold === LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).holdDetail)[0];\n            this.numberId = LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).secureKey;\n            this.getFloorFrom1toHighest(this.selectedHouse);\n            this.selectedFloor = this.listFloors.filter(x => x === LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).floor)[0];\n          } else {\n            this.reset();\n          }\n        } else {\n          if (!this.selectedFloor) {\n            this.reset();\n          }\n        }\n      } else {\n        LoadingService.loading(false);\n      }\n    });\n  }\n  fetchBuildCase(buildCaseId) {\n    return this.getHouseAndFloorByBuildCase(buildCaseId).pipe(tap(res => {\n      this.listHouseAndFloors = res.Entries;\n      // this.getFloorFrom1toHighest(this.listHouseAndFloors[0])\n    }), finalize(() => LoadingService.loading(false)));\n  }\n  getHouseAndFloorByBuildCase(buildCaseId) {\n    return this._buildCaseService.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json({\n      buildCaseId: buildCaseId\n    });\n  }\n  getFloorFrom1toHighest(data) {\n    this.listFloors = [];\n    data.Floors?.sort((a, b) => b - a).forEach(x => {\n      this.listFloors.push(x + 'F');\n    });\n    this.selectedFloor = this.listFloors[0];\n  }\n  login() {\n    LoadingService.loading(true);\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.valid.errorMessages.forEach(message => {\n        this._toastService.showErrorMSG(message);\n      });\n      LoadingService.loading(false);\n      return;\n    }\n    this._houseService.apiHouseLoginPost$Json({\n      body: {\n        // CaptchaToken: this.captchaToken,\n        BuildCaseId: this.selectedBuildCase.cID,\n        Floor: parseInt(this.selectedFloor.replace('F', '')),\n        HoldDetail: this.selectedHouse.CHouseHold,\n        SecureKey: this.numberId\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.isRedirectToHome = res.Entries?.CIsChange;\n        let saveLoginPayload = {\n          isSaveLogin: this.saveLogin,\n          secureKey: this.numberId,\n          floor: this.selectedFloor,\n          buildCaseId: this.selectedBuildCase.cID,\n          holdDetail: this.selectedHouse.CHouseHold\n        };\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.SAVE_LOGIN, saveLoginPayload);\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.TOKEN, res.Entries.jwtToken);\n      } else {\n        this._toastService.showErrorMSG(res.Message);\n        this.turnstile.reset();\n      }\n      LoadingService.loading(false);\n    }), concatMap(res => res.Entries?.CIsChange ? this.checkDate() : of(null))).subscribe();\n  }\n  checkDate() {\n    return this._houseService.apiHouseGetChangeDatePost$Json({}).pipe(tap(res => {\n      if (res?.StatusCode == 0) {\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.DATE_START, res?.Entries?.CChangeStartDate);\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.DATE_END, res?.Entries?.CChangeEndDate);\n        let payload = {\n          name: this.selectedHouse.CHouseHold + '-' + this.selectedFloor,\n          isFinishBuilding: false,\n          acceptChangeTime: false\n        };\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.USER, payload);\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.VERSION, '0.1.1');\n        this.gotoHome();\n      } else {\n        this._toastService.showErrorMSG('登录失败');\n        LoadingService.loading(false);\n      }\n    }));\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[密碼]', this.numberId);\n    // this.valid.required('[我不是機器人]', this.captchaToken)\n    // this.valid.required('[我不是機器人]', this.recaptcha)\n    // this.valid.CheckTaiwanID(this.numberId, true)\n  }\n  disableNegative(event) {\n    if (!(event.keyCode > 47 && event.keyCode < 58 || event.keyCode > 95 && event.keyCode < 106 || event.keyCode == 8 || event.keyCode == 190 || event.keyCode == 37)) {\n      event.preventDefault();\n    }\n  }\n  validationStep2() {\n    this.valid.clear();\n    this.valid.required('[Email]', this.CMail);\n    this.valid.required('[聯絡電話]', this.CPhone);\n    this.valid.isPhoneNumber('[聯絡電話]', this.CPhone);\n    this.valid.pattern('[Email]', this.CMail, this.pettern.MailPettern);\n  }\n  gotoHome() {\n    this._eventService.push({\n      action: \"FETCH_USER\" /* EEvent.FETCH_USER */,\n      payload: true\n    });\n    setTimeout(() => {\n      this._router.navigate(['home']).then(() => LoadingService.loading(false));\n    }, 200);\n  }\n  loginStep2() {\n    LoadingService.loading(true);\n    this.validationStep2();\n    if (this.valid.errorMessages.length > 0) {\n      this.valid.errorMessages.forEach(message => {\n        this._toastService.showErrorMSG(message);\n      });\n      LoadingService.loading(false);\n      return;\n    }\n    this._houseService.apiHouseHouseLoginStep2Post$Json({\n      body: {\n        CMail: this.CMail,\n        CPhone: this.CPhone\n      }\n    }).pipe(concatMap(() => this.checkDate())).subscribe();\n  }\n  changeBuildCase(buildCase) {\n    this.listFloors = [];\n    this.selectedBuildCase = {};\n    this.selectedHouse = {};\n    this.selectedFloor = {};\n    this.fetchBuildCase(buildCase.cID).subscribe(res => {\n      this.getFloorFrom1toHighest(res.Entries[0]);\n      this.reset();\n    });\n  }\n  reset() {\n    this.selectedHouse = this.listHouseAndFloors[0];\n    this.getFloorFrom1toHighest(this.selectedHouse);\n    this.selectedFloor = this.listFloors[0];\n    this.numberId = \"\";\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n    return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ValidationHelper), i0.ɵɵdirectiveInject(i3.ToastMessage), i0.ɵɵdirectiveInject(i4.HouseService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i5.PetternHelper), i0.ɵɵdirectiveInject(i6.EventService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    viewQuery: function LoginComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.turnstile = _t.first);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([MessageService, ToastMessage]), i0.ɵɵStandaloneFeature],\n    decls: 10,\n    vars: 5,\n    consts: [[1, \"wrapper\"], [1, \"content\"], [1, \"flex\", \"flex-col\", \"bg-white\", \"justify-center\"], [1, \"self-center\", \"mt-5\", \"w-[968px]\", \"h-[665px]\", \"max-md:max-w-full\"], [1, \"block\", \"md:flex\", \"justify-between\"], [\"class\", \"backgorund max-md:hidden\", 4, \"ngIf\"], [1, \"form-login\", \"flex\", \"flex-col\", \"md:flex\", \"w-[485px]\", \"max-md:w-full\", \"max-sm:pl-0\", \"pl-6\"], [\"pRipple\", \"\", \"position\", \"top-right\"], [1, \"backgorund\", \"max-md:hidden\"], [1, \"rounded-md\", 3, \"src\", \"ngStyle\"], [1, \"mt-4\", \"w-full\", \"text-xl\", \"text-stone-600\"], [1, \"font-semibold\"], [1, \"text-[#B8A676]\", \"font-bold\"], [\"optionLabel\", \"CBuildCaseName\", 3, \"options\", \"ngModel\", \"placeholder\"], [\"optionLabel\", \"CHouseHold\", 1, \"w-[45%]\", 3, \"options\", \"ngModel\"], [1, \"font-semibold\", 2, \"width\", \"45%\"], [1, \"w-[45%]\", 3, \"options\", \"ngModel\"], [1, \"flex\", \"flex-col\", \"mt-4\", \"w-full\", \"text-xl\", \"text-stone-600\"], [1, \"text-blue-400\", \"font-bold\"], [1, \"relative\"], [\"autocomplete\", \"off\", \"pInputText\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5BC6\\u78BC\", 1, \"input\", \"mt-2\", \"input-custom\", \"h-[50px]\", 3, \"ngModelChange\", \"type\", \"ngModel\"], [\"type\", \"button\", 1, \"absolute\", \"inset-y-0\", \"right-0\", \"pr-3\", \"flex\", \"items-center\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"class\", \"h-6 w-6 text-gray-500\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", 4, \"ngIf\"], [1, \"mt-4\", \"w-full\", \"text-stone-600\", \"checkbox-zone\"], [1, \"bg-white\", \"p-4\"], [\"label\", \"\\u8A18\\u4F4F\\u767B\\u5165\\u8CC7\\u8A0A\", 1, \"font-semibold\", 3, \"ngModelChange\", \"binary\", \"ngModel\"], [1, \"self-center\", \"mt-3\"], [\"type\", \"submit\", 1, \"button2\", \"!w-40\", 3, \"click\"], [\"optionLabel\", \"CBuildCaseName\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"placeholder\"], [\"optionLabel\", \"CHouseHold\", 1, \"w-[45%]\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [1, \"w-[45%]\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", 1, \"h-6\", \"w-6\", \"text-gray-500\"], [\"d\", \"M15.0007 12C15.0007 13.6569 13.6576 15 12.0007 15C10.3439 15 9.00073 13.6569 9.00073 12C9.00073 10.3431 10.3439 9 12.0007 9C13.6576 9 15.0007 10.3431 15.0007 12Z\", \"stroke\", \"#000000\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M12.0012 5C7.52354 5 3.73326 7.94288 2.45898 12C3.73324 16.0571 7.52354 19 12.0012 19C16.4788 19 20.2691 16.0571 21.5434 12C20.2691 7.94291 16.4788 5 12.0012 5Z\", \"stroke\", \"#000000\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M2.99902 3L20.999 21M9.8433 9.91364C9.32066 10.4536 8.99902 11.1892 8.99902 12C8.99902 13.6569 10.3422 15 11.999 15C12.8215 15 13.5667 14.669 14.1086 14.133M6.49902 6.64715C4.59972 7.90034 3.15305 9.78394 2.45703 12C3.73128 16.0571 7.52159 19 11.9992 19C13.9881 19 15.8414 18.4194 17.3988 17.4184M10.999 5.04939C11.328 5.01673 11.6617 5 11.9992 5C16.4769 5 20.2672 7.94291 21.5414 12C21.2607 12.894 20.8577 13.7338 20.3522 14.5\", \"stroke\", \"#000000\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"h-[480px]\"], [1, \"text-stone-900\", \"font-bold\"], [1, \"title-page\"], [1, \"flex\", \"flex-col\", \"mt-2\", \"w-full\", \"text-xl\", \"text-stone-600\", \"ng-star-inserted\"], [1, \"text-lg\", \"font-bold\", \"w-[150px]\"], [1, \"text-[#B8A676]\"], [\"autocomplete\", \"off\", \"pInputText\", \"\", \"type\", \"text\", 1, \"input\", \"input-custom\", \"h-[50px]\", 3, \"ngModelChange\", \"keydown\", \"ngModel\"], [1, \"flex\", \"flex-col\", \"mt-4\", \"w-full\", \"text-xl\", \"text-stone-600\", \"ng-star-inserted\"], [\"autocomplete\", \"off\", \"pInputText\", \"\", \"type\", \"email\", 1, \"input\", \"input-custom\", \"h-[50px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"my-2\", \"w-full\", \"text-stone-600\", \"ng-star-inserted\", \"sec-announ\", \"h-[55px]\"], [2, \"padding-right\", \"5px\"], [1, \"flex\", \"justify-center\", \"my-6\"], [\"type\", \"submit\", 1, \"button2\", \"btn-web\", 2, \"width\", \"250px\", 3, \"click\"], [\"type\", \"submit\", 1, \"button2\", \"btn-mobile\", 2, \"width\", \"180px\", 3, \"click\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵtemplate(5, LoginComponent_div_5_Template, 3, 6, \"div\", 5);\n        i0.ɵɵelementStart(6, \"div\", 6);\n        i0.ɵɵtemplate(7, LoginComponent_Conditional_7_Template, 35, 9)(8, LoginComponent_Conditional_8_Template, 26, 2);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelement(9, \"p-toast\", 7);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", !!ctx.selectedBuildCase);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(7, ctx.isRedirectToHome ? 7 : 8);\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(4, _c1));\n      }\n    },\n    dependencies: [CommonModule, i7.NgIf, i7.NgStyle, DropdownModule, i8.Dropdown, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, InputTextModule, i10.InputText, CheckboxModule, i11.Checkbox, ToastModule, i12.Toast, HttpClientModule, RecaptchaModule, RecaptchaFormsModule, BaseFilePipe, NgxTurnstileModule],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}html[_ngcontent-%COMP%]{line-height:initial;-webkit-text-size-adjust:100%;width:100%;height:100%}body[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:#23181599;font-family:Noto Sans TC!important;box-sizing:border-box;width:100%;height:auto;overflow:auto;overscroll-behavior:none;letter-spacing:.32px}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{-webkit-text-size-adjust:none;text-size-adjust:none}main[_ngcontent-%COMP%]{display:block}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:400}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible;margin:10px 0;border:none;border-top:1px solid #000}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1rem}a[_ngcontent-%COMP%]{background-color:transparent;text-decoration:none;-webkit-tap-highlight-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:500}strong[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:600}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}p[_ngcontent-%COMP%]{font-size:1rem}img[_ngcontent-%COMP%]{border-style:none;max-width:100%}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:Noto Sans TC,\\\\5fae\\\\8edf\\\\6b63\\\\9ed1\\\\9ad4,Arial,sans-serif;font-size:1rem;line-height:1.15;margin:0;color:#2f2f2f;-webkit-tap-highlight-color:transparent;letter-spacing:2px}select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]{padding:0;background-color:transparent;border:none;cursor:pointer}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}div[role=tab][_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]{border-spacing:0px}ol[_ngcontent-%COMP%], ul[_ngcontent-%COMP%]{padding-left:0;margin-top:0}.form-check-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:none}.container[_ngcontent-%COMP%]{width:100%}@media (min-width: 640px){.container[_ngcontent-%COMP%]{max-width:640px}}@media (min-width: 768px){.container[_ngcontent-%COMP%]{max-width:768px}}@media (min-width: 1024px){.container[_ngcontent-%COMP%]{max-width:1024px}}@media (min-width: 1280px){.container[_ngcontent-%COMP%]{max-width:1280px}}@media (min-width: 1536px){.container[_ngcontent-%COMP%]{max-width:1536px}}.visible[_ngcontent-%COMP%]{visibility:visible}.collapse[_ngcontent-%COMP%]{visibility:collapse}.static[_ngcontent-%COMP%]{position:static}.absolute[_ngcontent-%COMP%]{position:absolute}.relative[_ngcontent-%COMP%]{position:relative}.inset-y-0[_ngcontent-%COMP%]{top:0;bottom:0}.bottom-0[_ngcontent-%COMP%]{bottom:0}.left-0[_ngcontent-%COMP%]{left:0}.left-1\\\\/2[_ngcontent-%COMP%]{left:50%}.left-3[_ngcontent-%COMP%]{left:.75rem}.right-0[_ngcontent-%COMP%]{right:0}.top-1[_ngcontent-%COMP%]{top:.25rem}.top-1\\\\/2[_ngcontent-%COMP%]{top:50%}.top-3[_ngcontent-%COMP%]{top:.75rem}.m-0[_ngcontent-%COMP%]{margin:0}.m-2[_ngcontent-%COMP%]{margin:.5rem}.m-auto[_ngcontent-%COMP%]{margin:auto}.mx-3[_ngcontent-%COMP%]{margin-left:.75rem;margin-right:.75rem}.mx-4[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.mx-5[_ngcontent-%COMP%]{margin-left:1.25rem;margin-right:1.25rem}.mx-\\\\__ph-0__[_ngcontent-%COMP%]{margin-left:20%;margin-right:20%}.my-12[_ngcontent-%COMP%]{margin-top:3rem;margin-bottom:3rem}.my-2[_ngcontent-%COMP%]{margin-top:.5rem;margin-bottom:.5rem}.my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.my-6[_ngcontent-%COMP%]{margin-top:1.5rem;margin-bottom:1.5rem}.\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:.75rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ml-1[_ngcontent-%COMP%]{margin-left:.25rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.ml-4[_ngcontent-%COMP%]{margin-left:1rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-4[_ngcontent-%COMP%]{margin-right:1rem}.mr-5[_ngcontent-%COMP%]{margin-right:1.25rem}.mr-8[_ngcontent-%COMP%]{margin-right:2rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.mt-14[_ngcontent-%COMP%]{margin-top:3.5rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:.75rem}.mt-4[_ngcontent-%COMP%]{margin-top:1rem}.mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.mt-6[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-7[_ngcontent-%COMP%]{margin-top:1.75rem}.mt-8[_ngcontent-%COMP%]{margin-top:2rem}.mt-\\\\__ph-0__[_ngcontent-%COMP%]{margin-top:16px}.block[_ngcontent-%COMP%]{display:block}.flex[_ngcontent-%COMP%]{display:flex}.table[_ngcontent-%COMP%]{display:table}.table-row[_ngcontent-%COMP%]{display:table-row}.grid[_ngcontent-%COMP%]{display:grid}.\\\\!h-\\\\__ph-0__[_ngcontent-%COMP%]{height:107px!important}.\\\\!h-full[_ngcontent-%COMP%]{height:100%!important}.h-28[_ngcontent-%COMP%]{height:7rem}.h-32[_ngcontent-%COMP%]{height:8rem}.h-6[_ngcontent-%COMP%]{height:1.5rem}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:100px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:145px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:280px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:31px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:40vh}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:47px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:480px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:4px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:50px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:55px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:665px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:80px}.h-auto[_ngcontent-%COMP%]{height:auto}.h-fit[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content}.h-full[_ngcontent-%COMP%]{height:100%}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:200px}.\\\\!min-h-\\\\__ph-0__[_ngcontent-%COMP%]{min-height:60vh!important}.\\\\!w-40[_ngcontent-%COMP%]{width:10rem!important}.\\\\!w-48[_ngcontent-%COMP%]{width:12rem!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:107px!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:120px!important}.\\\\!w-full[_ngcontent-%COMP%]{width:100%!important}.w-1\\\\/3[_ngcontent-%COMP%]{width:33.333333%}.w-2\\\\/3[_ngcontent-%COMP%]{width:66.666667%}.w-28[_ngcontent-%COMP%]{width:7rem}.w-6[_ngcontent-%COMP%]{width:1.5rem}.w-7\\\\/12[_ngcontent-%COMP%]{width:58.333333%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:100%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:150px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:160px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:309px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:30rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:400px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:45%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:485px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:50%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:550px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:660px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:760px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:88px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:968px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:97%}.w-full[_ngcontent-%COMP%]{width:100%}.\\\\!max-w-full[_ngcontent-%COMP%]{max-width:100%!important}.max-w-4xl[_ngcontent-%COMP%]{max-width:56rem}.max-w-\\\\__ph-0__[_ngcontent-%COMP%]{max-width:1216px}.flex-shrink[_ngcontent-%COMP%]{flex-shrink:1}.grow[_ngcontent-%COMP%]{flex-grow:1}.border-collapse[_ngcontent-%COMP%]{border-collapse:collapse}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.resize-none[_ngcontent-%COMP%]{resize:none}.grid-cols-10[_ngcontent-%COMP%]{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}.grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}.flex-row[_ngcontent-%COMP%]{flex-direction:row}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.items-start[_ngcontent-%COMP%]{align-items:flex-start}.items-center[_ngcontent-%COMP%]{align-items:center}.justify-start[_ngcontent-%COMP%]{justify-content:flex-start}.justify-end[_ngcontent-%COMP%]{justify-content:flex-end}.justify-center[_ngcontent-%COMP%]{justify-content:center}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.gap-x-1[_ngcontent-%COMP%]{column-gap:.25rem}.self-center[_ngcontent-%COMP%]{align-self:center}.overflow-hidden[_ngcontent-%COMP%]{overflow:hidden}.overflow-y-scroll[_ngcontent-%COMP%]{overflow-y:scroll}.rounded[_ngcontent-%COMP%]{border-radius:.25rem}.rounded-3xl[_ngcontent-%COMP%]{border-radius:1.5rem}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.rounded-lg[_ngcontent-%COMP%]{border-radius:.5rem}.rounded-md[_ngcontent-%COMP%]{border-radius:.375rem}.border[_ngcontent-%COMP%]{border-width:1px}.border-2[_ngcontent-%COMP%]{border-width:2px}.border-solid[_ngcontent-%COMP%]{border-style:solid}.border-blue-400[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(96 165 250 / var(--tw-border-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 143 199 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(184 166 118 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea80}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea99}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(217 217 217 / var(--tw-bg-opacity))}.bg-gray-500[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(107 114 128 / var(--tw-bg-opacity))}.bg-white[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.bg-opacity-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: .04}.object-cover[_ngcontent-%COMP%]{object-fit:cover}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-4[_ngcontent-%COMP%]{padding:1rem}.p-\\\\__ph-0__[_ngcontent-%COMP%]{padding:16px}.px-3[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-6[_ngcontent-%COMP%]{padding-left:1.5rem;padding-right:1.5rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.py-2[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.pb-4[_ngcontent-%COMP%]{padding-bottom:1rem}.pb-5[_ngcontent-%COMP%]{padding-bottom:1.25rem}.pb-8[_ngcontent-%COMP%]{padding-bottom:2rem}.pl-4[_ngcontent-%COMP%]{padding-left:1rem}.pl-6[_ngcontent-%COMP%]{padding-left:1.5rem}.pr-1[_ngcontent-%COMP%]{padding-right:.25rem}.pr-3[_ngcontent-%COMP%]{padding-right:.75rem}.pr-4[_ngcontent-%COMP%]{padding-right:1rem}.text-left[_ngcontent-%COMP%]{text-align:left}.\\\\!text-center[_ngcontent-%COMP%]{text-align:center!important}.text-center[_ngcontent-%COMP%]{text-align:center}.text-right[_ngcontent-%COMP%]{text-align:right}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-3xl[_ngcontent-%COMP%]{font-size:1.875rem;line-height:2.25rem}.text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xl[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-light[_ngcontent-%COMP%]{font-weight:300}.font-medium[_ngcontent-%COMP%]{font-weight:500}.font-normal[_ngcontent-%COMP%]{font-weight:400}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.tracking-wider[_ngcontent-%COMP%]{letter-spacing:.05em}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 143 199 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(35 24 21 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(182 152 103 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(184 166 118 / var(--tw-text-opacity))}.text-black[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-blue-400[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.text-gray-500[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-stone-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(87 83 78 / var(--tw-text-opacity))}.text-stone-900[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(28 25 23 / var(--tw-text-opacity))}.text-white[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.shadow[_ngcontent-%COMP%]{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.grayscale[_ngcontent-%COMP%]{--tw-grayscale: grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter[_ngcontent-%COMP%]{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.content[_ngcontent-%COMP%]{width:100%}@media screen and (max-width: 1024px){.content[_ngcontent-%COMP%]{width:100%;padding:0 16px 50px}}.pc[_ngcontent-%COMP%], .inpc[_ngcontent-%COMP%]{display:initial}@media screen and (max-width: 1024px){.inpc[_ngcontent-%COMP%]{display:none}}.flat[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 1024px){.flat[_ngcontent-%COMP%]{display:initial}}.mb[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.mb[_ngcontent-%COMP%]{display:initial}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{position:relative}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background-image:url(/assets/background/background2.png);width:180px;height:150px;background-size:unset;background-position:left;z-index:-1;right:0;bottom:20px}.button1[_ngcontent-%COMP%]{width:200px;height:47px;border:1px solid #CDCDCD;color:#3a4246;border-radius:28px;background-origin:border-box;background-clip:content-box,border-box;transition:all .3s ease}.button1[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#b8a676;transition:color .3s ease}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#ae9b66}@media screen and (max-width: 912px){.button1[_ngcontent-%COMP%]{width:100%}}.button2[_ngcontent-%COMP%]{width:200px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button2[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button2[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button2[_ngcontent-%COMP%]{width:100%}}.button3[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button3[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button3[_ngcontent-%COMP%]{width:100%}}.button4[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:#979797;border-radius:24px;transition:all .3s ease}.button4[_ngcontent-%COMP%]:hover{background:#7e7e7e}.button4[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button4[_ngcontent-%COMP%]{width:100%}}.checkbox-zone[_ngcontent-%COMP%]{padding:2px;border-radius:2px;position:relative;background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 1px 4px #ae9b664d}.avoid-resize-img[_ngcontent-%COMP%]{max-width:none}.fit-size[_ngcontent-%COMP%]{height:inherit;width:inherit;object-fit:cover;object-position:center}.p-calendar[_ngcontent-%COMP%]{width:100%}.p-calendar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#23181599}@media screen and (max-width: 912px){.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]{border:none;width:100%}.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#231815}@media screen and (max-width: 912px){.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]   .p-dropdown-trigger[_ngcontent-%COMP%]{border-radius:0;border-bottom:1px solid rgba(144,150,157,.4)}.p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{box-shadow:none}.p-radiobutton[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]{width:22px;height:22px;border:2px solid #CDCDCD;background-color:#fff;border-radius:50%;transition:all .3s cubic-bezier(.25,.46,.45,.94);position:relative;box-shadow:0 1px 3px #ae9b661a;cursor:pointer}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:hover{border-color:#b8a676;background:radial-gradient(circle at center,#b8a67614,#b8a67605);transform:scale(1.05);box-shadow:0 2px 8px #ae9b6626}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #b8a67640,0 2px 8px #ae9b6633;border-color:#a39460}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:active{transform:scale(.98)}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-color:#ae9b66;box-shadow:0 2px 12px #ae9b6640,inset 0 1px 2px #fff3}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:8px;height:8px;background:radial-gradient(circle,#fff,#ffffffe6);border-radius:50%;transform:translate(-50%,-50%) scale(0);box-shadow:0 1px 2px #0003;animation:_ngcontent-%COMP%_radioAppear .2s ease forwards}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);border-color:#9b8a5a;transform:scale(1.05);box-shadow:0 3px 15px #ae9b6659,inset 0 1px 2px #ffffff40}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #b8a6764d,0 3px 15px #ae9b6659}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-icon[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.2);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{transform:translate(-50%,-50%) scale(0);opacity:.6}to{transform:translate(-50%,-50%) scale(4);opacity:0}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background-color:#fff;color:#846a52;font-size:16px;border:1px solid #E6F0F3;border-radius:6px;padding:16px 32px}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 32px 8px;border-bottom:none!important;border-bottom-right-radius:0;border-bottom-left-radius:0}@media screen and (max-width: 912px){.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(-180deg)!important;transition:transform .3s linear}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(0)!important;transition:transform .3s linear}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]:focus{box-shadow:none}.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 32px 16px}@media screen and (max-width: 912px){.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 16px 16px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{width:24px;height:24px}.p-dialog-mask[_ngcontent-%COMP%]{width:100%;height:100%;background-color:#0006;top:0;z-index:20;pointer-events:all!important}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]{padding:32px 24px 16px;color:#231815;font-size:24px;text-align:center;display:flex;justify-content:center}.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]{padding:32px 24px 0;background:linear-gradient(180deg,#f3f1ea,#fff)}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]{display:none}.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]{padding:24px 24px 40px;display:flex;justify-content:center}.p-checkbox[_ngcontent-%COMP%]{width:18px;height:18px;display:inline-flex;align-items:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]{width:18px;height:18px;border:2px solid #CDCDCD;border-radius:3px;transition:.3s ease;background:#fff;position:relative;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:hover{border-color:#b8a676}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:focus{outline:none;border-color:#b8a676;box-shadow:0 0 0 2px #b8a67633}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]{background:#b8a676!important;border-color:#b8a676!important}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{color:#fff!important;font-size:12px;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:not(.p-highlight)   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(0);opacity:0}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]:before{content:\\\"\\\\e915\\\";font-family:primeicons;font-weight:400;font-style:normal;font-size:12px;display:inline-block}.p-checkbox-label[_ngcontent-%COMP%]{margin-left:8px;cursor:pointer;-webkit-user-select:none;user-select:none}.input[_ngcontent-%COMP%]{width:100%;border:none;padding:12.5px 0;border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px;height:4px}[_ngcontent-%COMP%]::-webkit-scrollbar-button{background:transparent;border-radius:2px}[_ngcontent-%COMP%]::-webkit-scrollbar-track-piece{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:4px;background-color:#90969d33}[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:transparent}.p-radiobutton-label[_ngcontent-%COMP%]{color:#231815!important;font-weight:400;cursor:pointer;transition:all .3s ease}.p-radiobutton-label[_ngcontent-%COMP%]:hover{color:#ae9b66!important}label[for*=requirement_][_ngcontent-%COMP%]{color:#231815!important;font-weight:400}label[for*=requirement_][_ngcontent-%COMP%]:hover{color:#ae9b66!important}input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%], .p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%] + label[_ngcontent-%COMP%]{color:#ae9b66!important;font-weight:600}@media screen and (max-width: 912px){.content[_ngcontent-%COMP%]{padding-bottom:0}}.sec-announ[_ngcontent-%COMP%]{background-color:#e6f0f3;font-size:16px;padding-left:12px;line-height:55px;display:block}.btn-web[_ngcontent-%COMP%]{display:block}.btn-mobile[_ngcontent-%COMP%]{display:none}.title-page[_ngcontent-%COMP%]{font-size:32px}@media screen and (max-width: 912px){.btn-web[_ngcontent-%COMP%]{display:none}.btn-mobile[_ngcontent-%COMP%]{display:block}.title-page[_ngcontent-%COMP%]{font-size:24px}.sec-announ[_ngcontent-%COMP%]{display:none}}@media not all and (min-width: 1280px){.max-xl\\\\:grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}}@media not all and (min-width: 1024px){.max-lg\\\\:bottom-12[_ngcontent-%COMP%]{bottom:3rem}.max-lg\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-lg\\\\:grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}}@media not all and (min-width: 768px){.max-md\\\\:bottom-14[_ngcontent-%COMP%]{bottom:3.5rem}.max-md\\\\:my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.max-md\\\\:ml-0[_ngcontent-%COMP%]{margin-left:0}.max-md\\\\:mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.max-md\\\\:mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.max-md\\\\:hidden[_ngcontent-%COMP%]{display:none}.max-md\\\\:h-\\\\__ph-0__[_ngcontent-%COMP%]{height:65%}.max-md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:450px!important}.max-md\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:500px}.max-md\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-md\\\\:max-w-full[_ngcontent-%COMP%]{max-width:100%}.max-md\\\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.max-md\\\\:flex-col[_ngcontent-%COMP%]{flex-direction:column}.max-md\\\\:flex-col-reverse[_ngcontent-%COMP%]{flex-direction:column-reverse}.max-md\\\\:flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.max-md\\\\:items-start[_ngcontent-%COMP%]{align-items:flex-start}}@media not all and (min-width: 640px){.max-sm\\\\:bottom-10[_ngcontent-%COMP%]{bottom:2.5rem}.max-sm\\\\:ml-8[_ngcontent-%COMP%]{margin-left:2rem}.max-sm\\\\:block[_ngcontent-%COMP%]{display:block}.max-sm\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:340px!important}.max-sm\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:380px}.max-sm\\\\:\\\\!grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))!important}.max-sm\\\\:grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.max-sm\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.max-sm\\\\:gap-0[_ngcontent-%COMP%]{gap:0px}.max-sm\\\\:px-0[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.max-sm\\\\:pl-0[_ngcontent-%COMP%]{padding-left:0}.max-sm\\\\:text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}}@media (min-width: 640px){.sm\\\\:mx-0[_ngcontent-%COMP%]{margin-left:0;margin-right:0}.sm\\\\:mb-0[_ngcontent-%COMP%]{margin-bottom:0}.sm\\\\:mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.sm\\\\:flex[_ngcontent-%COMP%]{display:flex}.sm\\\\:items-center[_ngcontent-%COMP%]{align-items:center}.sm\\\\:pl-3[_ngcontent-%COMP%]{padding-left:.75rem}}@media (min-width: 768px){.md\\\\:flex[_ngcontent-%COMP%]{display:flex}.md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:634px!important}.md\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.md\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.md\\\\:pb-20[_ngcontent-%COMP%]{padding-bottom:5rem}}@media (min-width: 1024px){.lg\\\\:\\\\!mx-0[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.lg\\\\:\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.lg\\\\:flex[_ngcontent-%COMP%]{display:flex}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:800px!important}.lg\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.lg\\\\:\\\\!px-4[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.lg\\\\:\\\\!px-\\\\__ph-0__[_ngcontent-%COMP%]{padding-left:22px!important;padding-right:22px!important}.lg\\\\:\\\\!py-\\\\__ph-0__[_ngcontent-%COMP%]{padding-top:18px!important;padding-bottom:18px!important}.lg\\\\:text-center[_ngcontent-%COMP%]{text-align:center}}\"]\n  });\n}", "map": {"version": 3, "names": ["FormsModule", "CheckboxModule", "DropdownModule", "InputTextModule", "ToastMessage", "MessageService", "ToastModule", "HttpClientModule", "LocalStorageService", "STORAGE_KEY", "concatMap", "finalize", "mergeMap", "of", "tap", "LoadingService", "RecaptchaFormsModule", "RecaptchaModule", "environment", "BaseFilePipe", "CommonModule", "EEvent", "NgxTurnstileModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpipeBind1", "ctx_r0", "selectedBuildCase", "CFrontImage", "ɵɵsanitizeUrl", "isHorizontal", "ɵɵpureFunction0", "_c2", "_c3", "ɵɵlistener", "LoginComponent_Conditional_7_Conditional_5_Template_p_dropdown_ngModelChange_0_listener", "$event", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "changeBuildCase", "LoginComponent_Conditional_7_Conditional_5_Template_p_dropdown_onChange_0_listener", "onImageChange", "ɵɵtwoWayListener", "ɵɵtwoWayBindingSet", "listBuildCases", "ɵɵtwoWayProperty", "CBuildCaseName", "LoginComponent_Conditional_7_Conditional_12_Template_p_dropdown_ngModelChange_0_listener", "_r4", "selectedHouse", "getFloorFrom1toHighest", "listHouseAndFloors", "LoginComponent_Conditional_7_Conditional_18_Template_p_dropdown_ngModelChange_0_listener", "_r5", "selectedF<PERSON>or", "listFloors", "ɵɵtext", "ɵɵtemplate", "LoginComponent_Conditional_7_Conditional_5_Template", "LoginComponent_Conditional_7_Conditional_6_Template", "LoginComponent_Conditional_7_Conditional_12_Template", "LoginComponent_Conditional_7_Conditional_18_Template", "LoginComponent_Conditional_7_Template_input_ngModelChange_25_listener", "_r2", "numberId", "LoginComponent_Conditional_7_Template_button_click_26_listener", "togglePasswordVisibility", "LoginComponent_Conditional_7__svg_svg_27_Template", "LoginComponent_Conditional_7__svg_svg_28_Template", "LoginComponent_Conditional_7_Template_p_checkbox_ngModelChange_31_listener", "saveLogin", "LoginComponent_Conditional_7_Template_button_click_33_listener", "login", "ɵɵconditional", "length", "passwordFieldType", "LoginComponent_Conditional_8_Template_input_ngModelChange_9_listener", "_r6", "CPhone", "LoginComponent_Conditional_8_Template_input_keydown_9_listener", "disableNegative", "LoginComponent_Conditional_8_Template_input_ngModelChange_15_listener", "CMail", "LoginComponent_Conditional_8_Template_button_click_22_listener", "loginStep2", "LoginComponent_Conditional_8_Template_button_click_24_listener", "LoginComponent", "constructor", "_router", "valid", "_toastService", "_houseService", "_buildCaseService", "pettern", "_eventService", "SITE_KEY", "SITE_KEY_CLOUDFLARE_RECAPTCHA", "isRedirectToHome", "captchaToken", "version", "GetLocalStorage", "VERSION", "ClearLocalStorage", "RemoveLocalStorage", "DATE_END", "DATE_START", "TOKEN", "USER", "ngOnInit", "getListBuildCase", "onExpired", "console", "warn", "turnstile", "reset", "onError", "sendCaptchaResponse", "captchaResponse", "log", "checkImageOrientation", "img", "Image", "src", "BASE_URL_API", "onload", "width", "height", "onerror", "error", "e", "loading", "apiBuildCaseGetBuildCaseListPost$Json", "pipe", "res", "StatusCode", "Entries", "SAVE_LOGIN", "isSaveLogin", "filter", "x", "cID", "buildCaseId", "fetchBuildCase", "subscribe", "CHouseHold", "holdDetail", "<PERSON><PERSON><PERSON>", "floor", "getHouseAndFloorByBuildCase", "apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json", "data", "Floors", "sort", "a", "b", "for<PERSON>ach", "push", "validation", "errorMessages", "message", "showErrorMSG", "apiHouseLoginPost$Json", "body", "BuildCaseId", "Floor", "parseInt", "replace", "HoldDetail", "SecureKey", "CIsChange", "saveLoginPayload", "AddLocalStorage", "jwtToken", "Message", "checkDate", "apiHouseGetChangeDatePost$Json", "CChangeStartDate", "CChangeEndDate", "payload", "name", "isFinishBuilding", "acceptChangeTime", "gotoHome", "clear", "required", "event", "keyCode", "preventDefault", "validationStep2", "isPhoneNumber", "pattern", "MailPettern", "action", "setTimeout", "navigate", "then", "apiHouseHouseLoginStep2Post$Json", "buildCase", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "ValidationHelper", "i3", "i4", "HouseService", "BuildCaseService", "i5", "PetternHelper", "i6", "EventService", "_2", "selectors", "viewQuery", "LoginComponent_Query", "rf", "ctx", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LoginComponent_Template", "LoginComponent_div_5_Template", "LoginComponent_Conditional_7_Template", "LoginComponent_Conditional_8_Template", "ɵɵstyleMap", "_c1", "i7", "NgIf", "NgStyle", "i8", "Dropdown", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i10", "InputText", "i11", "Checkbox", "i12", "Toast", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\login\\login.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { ValidationHelper } from '../../shared/helper/validationHelper';\r\nimport { ToastMessage } from '../../shared/services/message.service';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { LocalStorageService } from '../../shared/services/local-storage.service';\r\nimport { STORAGE_KEY, UserInfo } from '../../shared/constant/constant';\r\nimport { concatMap, filter, finalize, mergeMap, of, tap } from 'rxjs';\r\nimport { BuildCaseGetListReponse, GetHouseAndFloorByBuildCaseIdRes } from '../../../services/api/models';\r\nimport { LoadingService } from '../../shared/services/loading.service';\r\nimport { RecaptchaFormsModule, RecaptchaModule } from 'ng-recaptcha';\r\nimport { environment } from \"../../../environments/environment\"\r\nimport { PetternHelper } from '../../shared/helper/petternHelper';\r\nimport { BaseFilePipe } from '../../shared/pipes/base-file.pipe';\r\nimport { CommonModule } from '@angular/common';\r\nimport { BuildCaseService, HouseService } from '../../../services/api/services';\r\nimport { EEvent, EventService } from '../../shared/services/event.service';\r\nimport { NgxTurnstileComponent, NgxTurnstileModule } from 'ngx-turnstile';\r\n\r\ndeclare const turnstile: any;\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    DropdownModule,\r\n    FormsModule,\r\n    InputTextModule,\r\n    CheckboxModule,\r\n    ToastModule,\r\n    HttpClientModule,\r\n    RecaptchaModule,\r\n    RecaptchaFormsModule,\r\n    BaseFilePipe,\r\n    NgxTurnstileModule\r\n  ],\r\n  providers: [\r\n    MessageService,\r\n    ToastMessage\r\n  ],\r\n  templateUrl: './login.component.html',\r\n  styleUrl: './login.component.scss'\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  @ViewChild('turnstileRef') turnstile!: NgxTurnstileComponent;\r\n\r\n  // readonly SITE_KEY = environment.SITE_KEY_RECAPTCHA;\r\n  readonly SITE_KEY: string = environment.SITE_KEY_CLOUDFLARE_RECAPTCHA ? environment.SITE_KEY_CLOUDFLARE_RECAPTCHA : \"\";\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  listHouseAndFloors: GetHouseAndFloorByBuildCaseIdRes[] = []\r\n  listFloors: string[] = []\r\n  selectedBuildCase!: BuildCaseGetListReponse\r\n  selectedHouse!: GetHouseAndFloorByBuildCaseIdRes;\r\n  selectedFloor!: string;\r\n  numberId: string = \"\"\r\n  saveLogin: boolean = false;\r\n  recaptcha: any\r\n  isHorizontal: boolean = true;\r\n  // resolved(captchaResponse: any) {\r\n  //   console.log({ recaptcha: captchaResponse });\r\n  // }\r\n\r\n  isRedirectToHome: boolean = true;\r\n  CPhone: string = \"\";\r\n  CMail: string = \"\";\r\n  captchaToken: string | null = null;\r\n  passwordFieldType: string = 'password';\r\n\r\n  togglePasswordVisibility() {\r\n    this.passwordFieldType = this.passwordFieldType === 'password' ? 'text' : 'password';\r\n  }\r\n\r\n  constructor(\r\n    private _router: Router,\r\n    private valid: ValidationHelper,\r\n    private _toastService: ToastMessage,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private pettern: PetternHelper,\r\n    private _eventService: EventService,\r\n  ) {\r\n    let version = LocalStorageService.GetLocalStorage(STORAGE_KEY.VERSION)\r\n    if (!version || version !== '0.1.1') {\r\n      LocalStorageService.ClearLocalStorage()\r\n    } else {\r\n      LocalStorageService.RemoveLocalStorage(STORAGE_KEY.DATE_END);\r\n      LocalStorageService.RemoveLocalStorage(STORAGE_KEY.DATE_START);\r\n      LocalStorageService.RemoveLocalStorage(STORAGE_KEY.TOKEN);\r\n      LocalStorageService.RemoveLocalStorage(STORAGE_KEY.USER);\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getListBuildCase();\r\n  }\r\n\r\n  onExpired() {\r\n    console.warn('Turnstile expired – resetting...');\r\n    this.turnstile.reset();\r\n  }\r\n\r\n  onError() {\r\n    console.warn('Turnstile error – resetting...');\r\n    this.turnstile.reset();\r\n  }\r\n\r\n  sendCaptchaResponse(captchaResponse: string | null) {\r\n    this.captchaToken = captchaResponse;\r\n    console.log(`Resolved captcha with response: ${captchaResponse}`);\r\n  }\r\n\r\n  checkImageOrientation() {\r\n    if (!this.selectedBuildCase?.CFrontImage) return\r\n    const img = new Image();\r\n    img.src = `${environment.BASE_URL_API}${this.selectedBuildCase?.CFrontImage}`\r\n    img.onload = () => {\r\n      this.isHorizontal = img.width > img.height;\r\n    };\r\n    img.onerror = () => console.error(\"Unable to load image from URL\");\r\n\r\n  }\r\n\r\n  onImageChange(e: any) {\r\n    this.checkImageOrientation()\r\n  }\r\n  getListBuildCase() {\r\n    LoadingService.loading(true);\r\n    this._buildCaseService.apiBuildCaseGetBuildCaseListPost$Json()\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            if (res.Entries) {\r\n              this.listBuildCases = res.Entries!\r\n              if (LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN)) {\r\n                this.saveLogin = LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).isSaveLogin;\r\n                if (this.saveLogin) {\r\n                  this.selectedBuildCase = this.listBuildCases.filter(x => x.cID === LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).buildCaseId)[0]\r\n                  if (!this.selectedBuildCase) {\r\n                    this.selectedBuildCase = res.Entries![0]\r\n                  }\r\n                } else {\r\n                  this.selectedBuildCase = res.Entries![0]\r\n                }\r\n              }\r\n              else {\r\n                this.selectedBuildCase = res.Entries![0]\r\n              }\r\n              this.checkImageOrientation()\r\n            }\r\n          }\r\n        }),\r\n        mergeMap(() => this.selectedBuildCase ? this.fetchBuildCase(this.selectedBuildCase.cID!) : \"0\")\r\n      ).subscribe((res) => {\r\n        if (res != \"0\") {\r\n          if (LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN)) {\r\n            if (this.saveLogin) {\r\n              this.selectedHouse = this.listHouseAndFloors.filter(x => x.CHouseHold === LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).holdDetail)[0]\r\n              this.numberId = LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).secureKey\r\n              this.getFloorFrom1toHighest(this.selectedHouse)\r\n              this.selectedFloor = this.listFloors.filter(x => x === LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN).floor)[0]\r\n            } else {\r\n              this.reset()\r\n            }\r\n          } else {\r\n            if (!this.selectedFloor) {\r\n              this.reset();\r\n            }\r\n          }\r\n        }\r\n        else {\r\n          LoadingService.loading(false);\r\n        }\r\n      })\r\n  }\r\n\r\n  fetchBuildCase(buildCaseId: number) {\r\n    return this.getHouseAndFloorByBuildCase(buildCaseId)\r\n      .pipe(\r\n        tap((res) => {\r\n          this.listHouseAndFloors = res.Entries!\r\n          // this.getFloorFrom1toHighest(this.listHouseAndFloors[0])\r\n        }),\r\n        finalize(() => LoadingService.loading(false))\r\n      )\r\n  }\r\n\r\n  getHouseAndFloorByBuildCase(buildCaseId: number) {\r\n    return this._buildCaseService.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json({\r\n      buildCaseId: buildCaseId\r\n    })\r\n  }\r\n\r\n  getFloorFrom1toHighest(data: GetHouseAndFloorByBuildCaseIdRes) {\r\n    this.listFloors = []\r\n    data.Floors?.sort((a, b) => b - a).forEach(x => {\r\n      this.listFloors.push(x + 'F')\r\n    })\r\n    this.selectedFloor = this.listFloors[0];\r\n  }\r\n\r\n  login() {\r\n    LoadingService.loading(true);\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.valid.errorMessages.forEach(message => {\r\n        this._toastService.showErrorMSG(message);\r\n      })\r\n      LoadingService.loading(false);\r\n      return;\r\n    }\r\n\r\n    this._houseService.apiHouseLoginPost$Json({\r\n      body: {\r\n        // CaptchaToken: this.captchaToken,\r\n        BuildCaseId: this.selectedBuildCase.cID!,\r\n        Floor: parseInt(this.selectedFloor.replace('F', '')),\r\n        HoldDetail: this.selectedHouse.CHouseHold!,\r\n        SecureKey: this.numberId\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.isRedirectToHome = res.Entries?.CIsChange!\r\n          let saveLoginPayload = {\r\n            isSaveLogin: this.saveLogin,\r\n            secureKey: this.numberId,\r\n            floor: this.selectedFloor,\r\n            buildCaseId: this.selectedBuildCase.cID!,\r\n            holdDetail: this.selectedHouse.CHouseHold!\r\n          }\r\n          LocalStorageService.AddLocalStorage(STORAGE_KEY.SAVE_LOGIN, saveLoginPayload)\r\n          LocalStorageService.AddLocalStorage(STORAGE_KEY.TOKEN, res.Entries!.jwtToken!)\r\n        } else {\r\n          this._toastService.showErrorMSG(res.Message!);\r\n          this.turnstile.reset();\r\n        }\r\n        LoadingService.loading(false);\r\n      }),\r\n      concatMap((res) => res.Entries?.CIsChange! ? this.checkDate() : of(null)),\r\n    ).subscribe()\r\n  }\r\n\r\n  checkDate() {\r\n    return this._houseService.apiHouseGetChangeDatePost$Json({}).pipe(\r\n      tap(res => {\r\n        if (res?.StatusCode! == 0) {\r\n          LocalStorageService.AddLocalStorage(STORAGE_KEY.DATE_START, res?.Entries?.CChangeStartDate!)\r\n          LocalStorageService.AddLocalStorage(STORAGE_KEY.DATE_END, res?.Entries?.CChangeEndDate!)\r\n          let payload: UserInfo = {\r\n            name: this.selectedHouse.CHouseHold! + '-' + this.selectedFloor,\r\n            isFinishBuilding: false,\r\n            acceptChangeTime: false\r\n          }\r\n          LocalStorageService.AddLocalStorage(STORAGE_KEY.USER, payload)\r\n          LocalStorageService.AddLocalStorage(STORAGE_KEY.VERSION, '0.1.1')\r\n          this.gotoHome();\r\n        } else {\r\n          this._toastService.showErrorMSG('登录失败');\r\n          LoadingService.loading(false);\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[密碼]', this.numberId)\r\n    // this.valid.required('[我不是機器人]', this.captchaToken)\r\n    // this.valid.required('[我不是機器人]', this.recaptcha)\r\n    // this.valid.CheckTaiwanID(this.numberId, true)\r\n  }\r\n\r\n  disableNegative(event: KeyboardEvent) {\r\n    if (!((event.keyCode > 47 && event.keyCode < 58) || (event.keyCode > 95 && event.keyCode < 106) || event.keyCode == 8 || event.keyCode == 190 || event.keyCode == 37)) {\r\n      event.preventDefault();\r\n    }\r\n  }\r\n\r\n  validationStep2() {\r\n    this.valid.clear();\r\n    this.valid.required('[Email]', this.CMail)\r\n    this.valid.required('[聯絡電話]', this.CPhone)\r\n    this.valid.isPhoneNumber('[聯絡電話]', this.CPhone)\r\n    this.valid.pattern('[Email]', this.CMail, this.pettern.MailPettern)\r\n  }\r\n\r\n  gotoHome() {\r\n    this._eventService.push({\r\n      action: EEvent.FETCH_USER,\r\n      payload: true\r\n    })\r\n    setTimeout(() => {\r\n      this._router.navigate(['home']).then(() => LoadingService.loading(false));\r\n    }, 200);\r\n  }\r\n\r\n  loginStep2() {\r\n    LoadingService.loading(true);\r\n    this.validationStep2()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.valid.errorMessages.forEach(message => {\r\n        this._toastService.showErrorMSG(message);\r\n      })\r\n      LoadingService.loading(false);\r\n      return;\r\n    }\r\n\r\n    this._houseService.apiHouseHouseLoginStep2Post$Json({\r\n      body: {\r\n        CMail: this.CMail,\r\n        CPhone: this.CPhone\r\n      }\r\n    }).pipe(\r\n      concatMap(() => this.checkDate())\r\n    ).subscribe()\r\n  }\r\n\r\n  changeBuildCase(buildCase: any) {\r\n    this.listFloors = []\r\n    this.selectedBuildCase = {} as BuildCaseGetListReponse\r\n    this.selectedHouse = {} as GetHouseAndFloorByBuildCaseIdRes\r\n    this.selectedFloor = {} as any\r\n    this.fetchBuildCase(buildCase.cID).subscribe(res => {\r\n      this.getFloorFrom1toHighest(res.Entries![0])\r\n      this.reset();\r\n    })\r\n  }\r\n\r\n  reset() {\r\n    this.selectedHouse = this.listHouseAndFloors[0]\r\n    this.getFloorFrom1toHighest(this.selectedHouse)\r\n    this.selectedFloor = this.listFloors[0]\r\n    this.numberId = \"\"\r\n  }\r\n}\r\n", "<div class=\"wrapper\">\r\n  <div class=\"content\">\r\n    <div class=\"flex flex-col bg-white justify-center\">\r\n      <div class=\"self-center mt-5 w-[968px] h-[665px] max-md:max-w-full\">\r\n        <div class=\"block md:flex justify-between\">\r\n          <div class=\"backgorund max-md:hidden\" *ngIf=\"!!selectedBuildCase\">\r\n            <img [src]=\"selectedBuildCase.CFrontImage! | addBaseFile\"\r\n              [ngStyle]=\"isHorizontal ? {'width.px': 480, 'height.px': 360} : {'width.px': 360, 'height.px': 480,  'margin-left.px': 100}\"\r\n              class=\"rounded-md\" />\r\n          </div>\r\n          <div class=\"form-login flex flex-col md:flex w-[485px] max-md:w-full max-sm:pl-0 pl-6\">\r\n            @if(isRedirectToHome){\r\n\r\n            <!-- <div class=\"text-3xl text-stone-900 font-bold\">\r\n              <span>\r\n                客戶選樣系統\r\n              </span>\r\n            </div> -->\r\n            <div class=\"mt-4 w-full text-xl text-stone-600\">\r\n              <span class=\"font-semibold\">\r\n                建案名稱 <span class=\"text-[#B8A676] font-bold\">*</span>\r\n              </span>\r\n              @if (!!listBuildCases && listBuildCases.length! > 0) {\r\n              <p-dropdown [options]=\"listBuildCases\" (ngModelChange)=\"changeBuildCase($event)\"\r\n                (onChange)=\"onImageChange($event)\" [(ngModel)]=\"selectedBuildCase\" optionLabel=\"CBuildCaseName\"\r\n                [placeholder]=\"listBuildCases[0].CBuildCaseName!\"></p-dropdown>\r\n              }@else {\r\n\r\n              }\r\n            </div>\r\n            <div class=\"mt-4 w-full text-xl text-stone-600\">\r\n              <span class=\"font-semibold\">\r\n                戶別 <span class=\"text-[#B8A676] font-bold\">*</span>\r\n              </span>\r\n              @if (!!listHouseAndFloors && listHouseAndFloors.length! > 0) {\r\n              <p-dropdown class=\"w-[45%]\" [options]=\"listHouseAndFloors\" [(ngModel)]=\"selectedHouse\"\r\n                optionLabel=\"CHouseHold\" (ngModelChange)=\"getFloorFrom1toHighest($event)\"></p-dropdown>\r\n              }\r\n            </div>\r\n            <div class=\"mt-4 w-full text-xl text-stone-600\">\r\n              <span class=\"font-semibold\" style=\"width: 45%;\">\r\n                樓層 <span class=\"text-[#B8A676] font-bold\">*</span>\r\n              </span>\r\n              @if (!!listHouseAndFloors && listHouseAndFloors.length! > 0) {\r\n              <p-dropdown class=\"w-[45%]\" [options]=\"listFloors\" [(ngModel)]=\"selectedFloor\"></p-dropdown>\r\n              }\r\n            </div>\r\n\r\n\r\n            <!-- <div class=\"flex flex-col mt-4 w-full text-xl text-stone-600\">\r\n              <span class=\"font-semibold\">\r\n                密碼 <span class=\"text-blue-400 font-bold\">*</span>\r\n              </span>\r\n              <input class=\"input mt-2 input-custom h-[50px]\" autocomplete=\"off\" pInputText type=\"password\"\r\n                [(ngModel)]=\"numberId\" placeholder=\"請輸入密碼\">\r\n            </div> -->\r\n\r\n            <div class=\"flex flex-col mt-4 w-full text-xl text-stone-600\">\r\n              <span class=\"font-semibold\">\r\n                密碼 <span class=\"text-blue-400 font-bold\">*</span>\r\n              </span>\r\n              <div class=\"relative\">\r\n                <input class=\"input mt-2 input-custom h-[50px]\" autocomplete=\"off\" pInputText [type]=\"passwordFieldType\"\r\n                  [(ngModel)]=\"numberId\" placeholder=\"請輸入密碼\">\r\n                <button type=\"button\" class=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\r\n                  (click)=\"togglePasswordVisibility()\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" *ngIf=\"passwordFieldType === 'text'\"\r\n                    class=\"h-6 w-6 text-gray-500\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                    <path\r\n                      d=\"M15.0007 12C15.0007 13.6569 13.6576 15 12.0007 15C10.3439 15 9.00073 13.6569 9.00073 12C9.00073 10.3431 10.3439 9 12.0007 9C13.6576 9 15.0007 10.3431 15.0007 12Z\"\r\n                      stroke=\"#000000\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\r\n                    <path\r\n                      d=\"M12.0012 5C7.52354 5 3.73326 7.94288 2.45898 12C3.73324 16.0571 7.52354 19 12.0012 19C16.4788 19 20.2691 16.0571 21.5434 12C20.2691 7.94291 16.4788 5 12.0012 5Z\"\r\n                      stroke=\"#000000\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\r\n                  </svg>\r\n\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" *ngIf=\"passwordFieldType === 'password'\"\r\n                    class=\"h-6 w-6 text-gray-500\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                    <path\r\n                      d=\"M2.99902 3L20.999 21M9.8433 9.91364C9.32066 10.4536 8.99902 11.1892 8.99902 12C8.99902 13.6569 10.3422 15 11.999 15C12.8215 15 13.5667 14.669 14.1086 14.133M6.49902 6.64715C4.59972 7.90034 3.15305 9.78394 2.45703 12C3.73128 16.0571 7.52159 19 11.9992 19C13.9881 19 15.8414 18.4194 17.3988 17.4184M10.999 5.04939C11.328 5.01673 11.6617 5 11.9992 5C16.4769 5 20.2672 7.94291 21.5414 12C21.2607 12.894 20.8577 13.7338 20.3522 14.5\"\r\n                      stroke=\"#000000\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n\r\n            <div class=\"mt-4 w-full text-stone-600 checkbox-zone\">\r\n              <div class=\"bg-white p-4\">\r\n                <p-checkbox class=\"font-semibold\" [binary]=\"true\" label=\"記住登入資訊\" [(ngModel)]=\"saveLogin\"></p-checkbox>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- <re-captcha [(ngModel)]=\"recaptcha\" class=\"mt-4 w-full\" (resolved)=\"resolved($event)\" [siteKey]=\"SITE_KEY\">\r\n              </re-captcha> -->\r\n            <!-- <div class=\"mt-4 w-full h-[100px]\">\r\n              <ngx-turnstile\r\n                #turnstileRef\r\n                [siteKey]=\"SITE_KEY\" (resolved)=\"sendCaptchaResponse($event)\" theme=\"auto\"\r\n                [tabIndex]=\"0\"\r\n                (expired)=\"onExpired()\"\r\n                (error)=\"onError()\">\r\n              </ngx-turnstile>\r\n            </div> -->\r\n            <div class=\"self-center mt-3\">\r\n              <button class=\"button2 !w-40\" type=\"submit\" (click)=\"login()\">\r\n                登入\r\n              </button>\r\n            </div>\r\n            }@else {\r\n            <div class=\"h-[480px]\">\r\n              <div class=\"text-stone-900 font-bold\">\r\n                <span class=\"title-page\">\r\n                  請更新您的聯絡資料\r\n                </span>\r\n              </div>\r\n              <div class=\"flex flex-col mt-2 w-full text-xl text-stone-600 ng-star-inserted\">\r\n                <span class=\"text-lg font-bold w-[150px]\">\r\n                  聯絡電話 <span class=\"text-[#B8A676]\">*</span>\r\n                </span>\r\n                <!-- <input class=\"ml-2 w-full !max-w-full\" pInputText type=\"text\" [(ngModel)]=\"CPhone\"\r\n                  (keydown)=\"disableNegative($event)\"> -->\r\n                <input class=\"input input-custom h-[50px]\" autocomplete=\"off\" pInputText type=\"text\"\r\n                  [(ngModel)]=\"CPhone\" (keydown)=\"disableNegative($event)\">\r\n              </div>\r\n              <div class=\"flex flex-col mt-4 w-full text-xl text-stone-600 ng-star-inserted\">\r\n                <span class=\"text-lg font-bold w-[150px]\">\r\n                  Email <span class=\"text-[#B8A676]\">*</span>\r\n                </span>\r\n                <!-- <input class=\"ml-2 w-full !max-w-full\" pInputText type=\"email\" [(ngModel)]=\"CMail\"> -->\r\n                <input class=\"input input-custom h-[50px]\" autocomplete=\"off\" pInputText type=\"email\"\r\n                  [(ngModel)]=\"CMail\">\r\n              </div>\r\n              <div class=\"my-2 w-full text-stone-600 ng-star-inserted sec-announ h-[55px]\">\r\n                <span style=\"padding-right: 5px;\">*</span>\r\n                <span>留下您的聯絡資訊，為您提供專屬的後續服務，我們會與您聯繫！</span>\r\n              </div>\r\n              <div class=\"flex justify-center my-6\">\r\n                <button class=\"button2 btn-web\" type=\"submit\" (click)=\"loginStep2()\" style=\"width: 250px;\">\r\n                  下一步，進行客戶選樣\r\n                </button>\r\n                <button class=\"button2 btn-mobile\" type=\"submit\" (click)=\"loginStep2()\" style=\"width: 180px;\">\r\n                  儲存\r\n                </button>\r\n              </div>\r\n            </div>\r\n            }\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <p-toast pRipple position=\"top-right\" [style]=\"{'width': '22rem'}\"></p-toast>\r\n\r\n  <!-- <script src=\"https://challenges.cloudflare.com/turnstile/v0/api.js\" async defer></script> -->"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AAEnD,SAASC,YAAY,QAAQ,uCAAuC;AACpE,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvD,SAASC,mBAAmB,QAAQ,6CAA6C;AACjF,SAASC,WAAW,QAAkB,gCAAgC;AACtE,SAASC,SAAS,EAAUC,QAAQ,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AAErE,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,oBAAoB,EAAEC,eAAe,QAAQ,cAAc;AACpE,SAASC,WAAW,QAAQ,mCAAmC;AAE/D,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,MAAM,QAAsB,qCAAqC;AAC1E,SAAgCC,kBAAkB,QAAQ,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IClB/DC,EAAA,CAAAC,cAAA,aAAkE;IAChED,EAAA,CAAAE,SAAA,aAEuB;;IACzBF,EAAA,CAAAG,YAAA,EAAM;;;;IAHCH,EAAA,CAAAI,SAAA,EAAoD;IACvDJ,EADG,CAAAK,UAAA,QAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAC,iBAAA,CAAAC,WAAA,GAAAT,EAAA,CAAAU,aAAA,CAAoD,YAAAH,MAAA,CAAAI,YAAA,GAAAX,EAAA,CAAAY,eAAA,IAAAC,GAAA,IAAAb,EAAA,CAAAY,eAAA,IAAAE,GAAA,EACqE;;;;;;IAgB5Hd,EAAA,CAAAC,cAAA,qBAEoD;IADlDD,EADqC,CAAAe,UAAA,2BAAAC,wFAAAC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAZ,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAiBd,MAAA,CAAAe,eAAA,CAAAL,MAAA,CAAuB;IAAA,EAAC,sBAAAM,mFAAAN,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAZ,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAClEd,MAAA,CAAAiB,aAAA,CAAAP,MAAA,CAAqB;IAAA,EAAC;IAACjB,EAAA,CAAAyB,gBAAA,2BAAAT,wFAAAC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAZ,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAA0B,kBAAA,CAAAnB,MAAA,CAAAC,iBAAA,EAAAS,MAAA,MAAAV,MAAA,CAAAC,iBAAA,GAAAS,MAAA;MAAA,OAAAjB,EAAA,CAAAqB,WAAA,CAAAJ,MAAA;IAAA,EAA+B;IAChBjB,EAAA,CAAAG,YAAA,EAAa;;;;IAFrDH,EAAA,CAAAK,UAAA,YAAAE,MAAA,CAAAoB,cAAA,CAA0B;IACD3B,EAAA,CAAA4B,gBAAA,YAAArB,MAAA,CAAAC,iBAAA,CAA+B;IAClER,EAAA,CAAAK,UAAA,gBAAAE,MAAA,CAAAoB,cAAA,IAAAE,cAAA,CAAiD;;;;;;;IAUnD7B,EAAA,CAAAC,cAAA,qBAC4E;IADjBD,EAAA,CAAAyB,gBAAA,2BAAAK,yFAAAb,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAa,GAAA;MAAA,MAAAxB,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAA0B,kBAAA,CAAAnB,MAAA,CAAAyB,aAAA,EAAAf,MAAA,MAAAV,MAAA,CAAAyB,aAAA,GAAAf,MAAA;MAAA,OAAAjB,EAAA,CAAAqB,WAAA,CAAAJ,MAAA;IAAA,EAA2B;IAC3DjB,EAAA,CAAAe,UAAA,2BAAAe,yFAAAb,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAa,GAAA;MAAA,MAAAxB,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAiBd,MAAA,CAAA0B,sBAAA,CAAAhB,MAAA,CAA8B;IAAA,EAAC;IAACjB,EAAA,CAAAG,YAAA,EAAa;;;;IAD7DH,EAAA,CAAAK,UAAA,YAAAE,MAAA,CAAA2B,kBAAA,CAA8B;IAAClC,EAAA,CAAA4B,gBAAA,YAAArB,MAAA,CAAAyB,aAAA,CAA2B;;;;;;IAStFhC,EAAA,CAAAC,cAAA,qBAA+E;IAA5BD,EAAA,CAAAyB,gBAAA,2BAAAU,yFAAAlB,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAkB,GAAA;MAAA,MAAA7B,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAA0B,kBAAA,CAAAnB,MAAA,CAAA8B,aAAA,EAAApB,MAAA,MAAAV,MAAA,CAAA8B,aAAA,GAAApB,MAAA;MAAA,OAAAjB,EAAA,CAAAqB,WAAA,CAAAJ,MAAA;IAAA,EAA2B;IAACjB,EAAA,CAAAG,YAAA,EAAa;;;;IAAhEH,EAAA,CAAAK,UAAA,YAAAE,MAAA,CAAA+B,UAAA,CAAsB;IAACtC,EAAA,CAAA4B,gBAAA,YAAArB,MAAA,CAAA8B,aAAA,CAA2B;;;;;;IAsB1ErC,EAAA,CAAAC,cAAA,cACgE;IAI9DD,EAHA,CAAAE,SAAA,eAEqF,eAGA;IACvFF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAENH,EAAA,CAAAC,cAAA,cACgE;IAC9DD,EAAA,CAAAE,SAAA,eAEqF;IACvFF,EAAA,CAAAG,YAAA,EAAM;;;;;;IA9DVH,EADF,CAAAC,cAAA,cAAgD,eAClB;IAC1BD,EAAA,CAAAuC,MAAA,iCAAK;IAAAvC,EAAA,CAAAC,cAAA,eAAuC;IAAAD,EAAA,CAAAuC,MAAA,QAAC;IAC/CvC,EAD+C,CAAAG,YAAA,EAAO,EAC/C;IAKNH,EAJD,CAAAwC,UAAA,IAAAC,mDAAA,yBAAsD,IAAAC,mDAAA,OAI9C;IAGV1C,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAgD,eAClB;IAC1BD,EAAA,CAAAuC,MAAA,qBAAG;IAAAvC,EAAA,CAAAC,cAAA,gBAAuC;IAAAD,EAAA,CAAAuC,MAAA,SAAC;IAC7CvC,EAD6C,CAAAG,YAAA,EAAO,EAC7C;IACPH,EAAA,CAAAwC,UAAA,KAAAG,oDAAA,yBAA8D;IAIhE3C,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAgD,gBACE;IAC9CD,EAAA,CAAAuC,MAAA,sBAAG;IAAAvC,EAAA,CAAAC,cAAA,gBAAuC;IAAAD,EAAA,CAAAuC,MAAA,SAAC;IAC7CvC,EAD6C,CAAAG,YAAA,EAAO,EAC7C;IACPH,EAAA,CAAAwC,UAAA,KAAAI,oDAAA,yBAA8D;IAGhE5C,EAAA,CAAAG,YAAA,EAAM;IAYJH,EADF,CAAAC,cAAA,eAA8D,gBAChC;IAC1BD,EAAA,CAAAuC,MAAA,sBAAG;IAAAvC,EAAA,CAAAC,cAAA,gBAAsC;IAAAD,EAAA,CAAAuC,MAAA,SAAC;IAC5CvC,EAD4C,CAAAG,YAAA,EAAO,EAC5C;IAELH,EADF,CAAAC,cAAA,eAAsB,iBAEyB;IAA3CD,EAAA,CAAAyB,gBAAA,2BAAAoB,sEAAA5B,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA4B,GAAA;MAAA,MAAAvC,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAA0B,kBAAA,CAAAnB,MAAA,CAAAwC,QAAA,EAAA9B,MAAA,MAAAV,MAAA,CAAAwC,QAAA,GAAA9B,MAAA;MAAA,OAAAjB,EAAA,CAAAqB,WAAA,CAAAJ,MAAA;IAAA,EAAsB;IADxBjB,EAAA,CAAAG,YAAA,EAC6C;IAC7CH,EAAA,CAAAC,cAAA,kBACuC;IAArCD,EAAA,CAAAe,UAAA,mBAAAiC,+DAAA;MAAAhD,EAAA,CAAAkB,aAAA,CAAA4B,GAAA;MAAA,MAAAvC,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASd,MAAA,CAAA0C,wBAAA,EAA0B;IAAA,EAAC;IAWpCjD,EAVA,CAAAwC,UAAA,KAAAU,iDAAA,kBACgE,KAAAC,iDAAA,kBAUA;IAOtEnD,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAAsD,eAC1B,sBACiE;IAAxBD,EAAA,CAAAyB,gBAAA,2BAAA2B,2EAAAnC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA4B,GAAA;MAAA,MAAAvC,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAA0B,kBAAA,CAAAnB,MAAA,CAAA8C,SAAA,EAAApC,MAAA,MAAAV,MAAA,CAAA8C,SAAA,GAAApC,MAAA;MAAA,OAAAjB,EAAA,CAAAqB,WAAA,CAAAJ,MAAA;IAAA,EAAuB;IAE5FjB,EAF6F,CAAAG,YAAA,EAAa,EAClG,EACF;IAcJH,EADF,CAAAC,cAAA,eAA8B,kBACkC;IAAlBD,EAAA,CAAAe,UAAA,mBAAAuC,+DAAA;MAAAtD,EAAA,CAAAkB,aAAA,CAAA4B,GAAA;MAAA,MAAAvC,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASd,MAAA,CAAAgD,KAAA,EAAO;IAAA,EAAC;IAC3DvD,EAAA,CAAAuC,MAAA,sBACF;IACFvC,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAtFJH,EAAA,CAAAI,SAAA,GAMC;IANDJ,EAAA,CAAAwD,aAAA,MAAAjD,MAAA,CAAAoB,cAAA,IAAApB,MAAA,CAAAoB,cAAA,CAAA8B,MAAA,aAMC;IAMDzD,EAAA,CAAAI,SAAA,GAGC;IAHDJ,EAAA,CAAAwD,aAAA,OAAAjD,MAAA,CAAA2B,kBAAA,IAAA3B,MAAA,CAAA2B,kBAAA,CAAAuB,MAAA,eAGC;IAMDzD,EAAA,CAAAI,SAAA,GAEC;IAFDJ,EAAA,CAAAwD,aAAA,OAAAjD,MAAA,CAAA2B,kBAAA,IAAA3B,MAAA,CAAA2B,kBAAA,CAAAuB,MAAA,eAEC;IAiB+EzD,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,UAAA,SAAAE,MAAA,CAAAmD,iBAAA,CAA0B;IACtG1D,EAAA,CAAA4B,gBAAA,YAAArB,MAAA,CAAAwC,QAAA,CAAsB;IAGmB/C,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,UAAA,SAAAE,MAAA,CAAAmD,iBAAA,YAAkC;IAUlC1D,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAK,UAAA,SAAAE,MAAA,CAAAmD,iBAAA,gBAAsC;IAa/C1D,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,UAAA,gBAAe;IAAgBL,EAAA,CAAA4B,gBAAA,YAAArB,MAAA,CAAA8C,SAAA,CAAuB;;;;;;IAuBxFrD,EAFJ,CAAAC,cAAA,cAAuB,cACiB,eACX;IACvBD,EAAA,CAAAuC,MAAA,+DACF;IACFvC,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,cAA+E,eACnC;IACxCD,EAAA,CAAAuC,MAAA,iCAAK;IAAAvC,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAuC,MAAA,QAAC;IACrCvC,EADqC,CAAAG,YAAA,EAAO,EACrC;IAGPH,EAAA,CAAAC,cAAA,gBAC2D;IAAzDD,EAAA,CAAAyB,gBAAA,2BAAAkC,qEAAA1C,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA0C,GAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAA0B,kBAAA,CAAAnB,MAAA,CAAAsD,MAAA,EAAA5C,MAAA,MAAAV,MAAA,CAAAsD,MAAA,GAAA5C,MAAA;MAAA,OAAAjB,EAAA,CAAAqB,WAAA,CAAAJ,MAAA;IAAA,EAAoB;IAACjB,EAAA,CAAAe,UAAA,qBAAA+C,+DAAA7C,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA0C,GAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAWd,MAAA,CAAAwD,eAAA,CAAA9C,MAAA,CAAuB;IAAA,EAAC;IAC5DjB,EAFE,CAAAG,YAAA,EAC2D,EACvD;IAEJH,EADF,CAAAC,cAAA,eAA+E,gBACnC;IACxCD,EAAA,CAAAuC,MAAA,eAAM;IAAAvC,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAuC,MAAA,SAAC;IACtCvC,EADsC,CAAAG,YAAA,EAAO,EACtC;IAEPH,EAAA,CAAAC,cAAA,iBACsB;IAApBD,EAAA,CAAAyB,gBAAA,2BAAAuC,sEAAA/C,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA0C,GAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAA0B,kBAAA,CAAAnB,MAAA,CAAA0D,KAAA,EAAAhD,MAAA,MAAAV,MAAA,CAAA0D,KAAA,GAAAhD,MAAA;MAAA,OAAAjB,EAAA,CAAAqB,WAAA,CAAAJ,MAAA;IAAA,EAAmB;IACvBjB,EAFE,CAAAG,YAAA,EACsB,EAClB;IAEJH,EADF,CAAAC,cAAA,eAA6E,gBACzC;IAAAD,EAAA,CAAAuC,MAAA,SAAC;IAAAvC,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAuC,MAAA,sLAA6B;IACrCvC,EADqC,CAAAG,YAAA,EAAO,EACtC;IAEJH,EADF,CAAAC,cAAA,eAAsC,kBACuD;IAA7CD,EAAA,CAAAe,UAAA,mBAAAmD,+DAAA;MAAAlE,EAAA,CAAAkB,aAAA,CAAA0C,GAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASd,MAAA,CAAA4D,UAAA,EAAY;IAAA,EAAC;IAClEnE,EAAA,CAAAuC,MAAA,sEACF;IAAAvC,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA8F;IAA7CD,EAAA,CAAAe,UAAA,mBAAAqD,+DAAA;MAAApE,EAAA,CAAAkB,aAAA,CAAA0C,GAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASd,MAAA,CAAA4D,UAAA,EAAY;IAAA,EAAC;IACrEnE,EAAA,CAAAuC,MAAA,sBACF;IAEJvC,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAtBAH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA4B,gBAAA,YAAArB,MAAA,CAAAsD,MAAA,CAAoB;IAQpB7D,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAA4B,gBAAA,YAAArB,MAAA,CAAA0D,KAAA,CAAmB;;;ADjFrC,OAAM,MAAOI,cAAc;EAyBzBpB,wBAAwBA,CAAA;IACtB,IAAI,CAACS,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,KAAK,UAAU,GAAG,MAAM,GAAG,UAAU;EACtF;EAEAY,YACUC,OAAe,EACfC,KAAuB,EACvBC,aAA2B,EAC3BC,aAA2B,EAC3BC,iBAAmC,EACnCC,OAAsB,EACtBC,aAA2B;IAN3B,KAAAN,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,aAAa,GAAbA,aAAa;IAjCvB;IACS,KAAAC,QAAQ,GAAWnF,WAAW,CAACoF,6BAA6B,GAAGpF,WAAW,CAACoF,6BAA6B,GAAG,EAAE;IACtH,KAAApD,cAAc,GAA8B,EAAE;IAC9C,KAAAO,kBAAkB,GAAuC,EAAE;IAC3D,KAAAI,UAAU,GAAa,EAAE;IAIzB,KAAAS,QAAQ,GAAW,EAAE;IACrB,KAAAM,SAAS,GAAY,KAAK;IAE1B,KAAA1C,YAAY,GAAY,IAAI;IAC5B;IACA;IACA;IAEA,KAAAqE,gBAAgB,GAAY,IAAI;IAChC,KAAAnB,MAAM,GAAW,EAAE;IACnB,KAAAI,KAAK,GAAW,EAAE;IAClB,KAAAgB,YAAY,GAAkB,IAAI;IAClC,KAAAvB,iBAAiB,GAAW,UAAU;IAepC,IAAIwB,OAAO,GAAGjG,mBAAmB,CAACkG,eAAe,CAACjG,WAAW,CAACkG,OAAO,CAAC;IACtE,IAAI,CAACF,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;MACnCjG,mBAAmB,CAACoG,iBAAiB,EAAE;KACxC,MAAM;MACLpG,mBAAmB,CAACqG,kBAAkB,CAACpG,WAAW,CAACqG,QAAQ,CAAC;MAC5DtG,mBAAmB,CAACqG,kBAAkB,CAACpG,WAAW,CAACsG,UAAU,CAAC;MAC9DvG,mBAAmB,CAACqG,kBAAkB,CAACpG,WAAW,CAACuG,KAAK,CAAC;MACzDxG,mBAAmB,CAACqG,kBAAkB,CAACpG,WAAW,CAACwG,IAAI,CAAC;;EAE5D;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAC,SAASA,CAAA;IACPC,OAAO,CAACC,IAAI,CAAC,kCAAkC,CAAC;IAChD,IAAI,CAACC,SAAS,CAACC,KAAK,EAAE;EACxB;EAEAC,OAAOA,CAAA;IACLJ,OAAO,CAACC,IAAI,CAAC,gCAAgC,CAAC;IAC9C,IAAI,CAACC,SAAS,CAACC,KAAK,EAAE;EACxB;EAEAE,mBAAmBA,CAACC,eAA8B;IAChD,IAAI,CAACnB,YAAY,GAAGmB,eAAe;IACnCN,OAAO,CAACO,GAAG,CAAC,mCAAmCD,eAAe,EAAE,CAAC;EACnE;EAEAE,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC9F,iBAAiB,EAAEC,WAAW,EAAE;IAC1C,MAAM8F,GAAG,GAAG,IAAIC,KAAK,EAAE;IACvBD,GAAG,CAACE,GAAG,GAAG,GAAG9G,WAAW,CAAC+G,YAAY,GAAG,IAAI,CAAClG,iBAAiB,EAAEC,WAAW,EAAE;IAC7E8F,GAAG,CAACI,MAAM,GAAG,MAAK;MAChB,IAAI,CAAChG,YAAY,GAAG4F,GAAG,CAACK,KAAK,GAAGL,GAAG,CAACM,MAAM;IAC5C,CAAC;IACDN,GAAG,CAACO,OAAO,GAAG,MAAMhB,OAAO,CAACiB,KAAK,CAAC,+BAA+B,CAAC;EAEpE;EAEAvF,aAAaA,CAACwF,CAAM;IAClB,IAAI,CAACV,qBAAqB,EAAE;EAC9B;EACAV,gBAAgBA,CAAA;IACdpG,cAAc,CAACyH,OAAO,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACtC,iBAAiB,CAACuC,qCAAqC,EAAE,CAC3DC,IAAI,CACH5H,GAAG,CAAC6H,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAO,EAAE;UACf,IAAI,CAAC3F,cAAc,GAAGyF,GAAG,CAACE,OAAQ;UAClC,IAAIrI,mBAAmB,CAACkG,eAAe,CAACjG,WAAW,CAACqI,UAAU,CAAC,EAAE;YAC/D,IAAI,CAAClE,SAAS,GAAGpE,mBAAmB,CAACkG,eAAe,CAACjG,WAAW,CAACqI,UAAU,CAAC,CAACC,WAAW;YACxF,IAAI,IAAI,CAACnE,SAAS,EAAE;cAClB,IAAI,CAAC7C,iBAAiB,GAAG,IAAI,CAACmB,cAAc,CAAC8F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAK1I,mBAAmB,CAACkG,eAAe,CAACjG,WAAW,CAACqI,UAAU,CAAC,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC;cAC9I,IAAI,CAAC,IAAI,CAACpH,iBAAiB,EAAE;gBAC3B,IAAI,CAACA,iBAAiB,GAAG4G,GAAG,CAACE,OAAQ,CAAC,CAAC,CAAC;;aAE3C,MAAM;cACL,IAAI,CAAC9G,iBAAiB,GAAG4G,GAAG,CAACE,OAAQ,CAAC,CAAC,CAAC;;WAE3C,MACI;YACH,IAAI,CAAC9G,iBAAiB,GAAG4G,GAAG,CAACE,OAAQ,CAAC,CAAC,CAAC;;UAE1C,IAAI,CAAChB,qBAAqB,EAAE;;;IAGlC,CAAC,CAAC,EACFjH,QAAQ,CAAC,MAAM,IAAI,CAACmB,iBAAiB,GAAG,IAAI,CAACqH,cAAc,CAAC,IAAI,CAACrH,iBAAiB,CAACmH,GAAI,CAAC,GAAG,GAAG,CAAC,CAChG,CAACG,SAAS,CAAEV,GAAG,IAAI;MAClB,IAAIA,GAAG,IAAI,GAAG,EAAE;QACd,IAAInI,mBAAmB,CAACkG,eAAe,CAACjG,WAAW,CAACqI,UAAU,CAAC,EAAE;UAC/D,IAAI,IAAI,CAAClE,SAAS,EAAE;YAClB,IAAI,CAACrB,aAAa,GAAG,IAAI,CAACE,kBAAkB,CAACuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACK,UAAU,KAAK9I,mBAAmB,CAACkG,eAAe,CAACjG,WAAW,CAACqI,UAAU,CAAC,CAACS,UAAU,CAAC,CAAC,CAAC,CAAC;YACpJ,IAAI,CAACjF,QAAQ,GAAG9D,mBAAmB,CAACkG,eAAe,CAACjG,WAAW,CAACqI,UAAU,CAAC,CAACU,SAAS;YACrF,IAAI,CAAChG,sBAAsB,CAAC,IAAI,CAACD,aAAa,CAAC;YAC/C,IAAI,CAACK,aAAa,GAAG,IAAI,CAACC,UAAU,CAACmF,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKzI,mBAAmB,CAACkG,eAAe,CAACjG,WAAW,CAACqI,UAAU,CAAC,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC;WAC7H,MAAM;YACL,IAAI,CAACjC,KAAK,EAAE;;SAEf,MAAM;UACL,IAAI,CAAC,IAAI,CAAC5D,aAAa,EAAE;YACvB,IAAI,CAAC4D,KAAK,EAAE;;;OAGjB,MACI;QACHzG,cAAc,CAACyH,OAAO,CAAC,KAAK,CAAC;;IAEjC,CAAC,CAAC;EACN;EAEAY,cAAcA,CAACD,WAAmB;IAChC,OAAO,IAAI,CAACO,2BAA2B,CAACP,WAAW,CAAC,CACjDT,IAAI,CACH5H,GAAG,CAAE6H,GAAG,IAAI;MACV,IAAI,CAAClF,kBAAkB,GAAGkF,GAAG,CAACE,OAAQ;MACtC;IACF,CAAC,CAAC,EACFlI,QAAQ,CAAC,MAAMI,cAAc,CAACyH,OAAO,CAAC,KAAK,CAAC,CAAC,CAC9C;EACL;EAEAkB,2BAA2BA,CAACP,WAAmB;IAC7C,OAAO,IAAI,CAACjD,iBAAiB,CAACyD,kDAAkD,CAAC;MAC/ER,WAAW,EAAEA;KACd,CAAC;EACJ;EAEA3F,sBAAsBA,CAACoG,IAAsC;IAC3D,IAAI,CAAC/F,UAAU,GAAG,EAAE;IACpB+F,IAAI,CAACC,MAAM,EAAEC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAACE,OAAO,CAAChB,CAAC,IAAG;MAC7C,IAAI,CAACpF,UAAU,CAACqG,IAAI,CAACjB,CAAC,GAAG,GAAG,CAAC;IAC/B,CAAC,CAAC;IACF,IAAI,CAACrF,aAAa,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;EACzC;EAEAiB,KAAKA,CAAA;IACH/D,cAAc,CAACyH,OAAO,CAAC,IAAI,CAAC;IAC5B,IAAI,CAAC2B,UAAU,EAAE;IACjB,IAAI,IAAI,CAACpE,KAAK,CAACqE,aAAa,CAACpF,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACe,KAAK,CAACqE,aAAa,CAACH,OAAO,CAACI,OAAO,IAAG;QACzC,IAAI,CAACrE,aAAa,CAACsE,YAAY,CAACD,OAAO,CAAC;MAC1C,CAAC,CAAC;MACFtJ,cAAc,CAACyH,OAAO,CAAC,KAAK,CAAC;MAC7B;;IAGF,IAAI,CAACvC,aAAa,CAACsE,sBAAsB,CAAC;MACxCC,IAAI,EAAE;QACJ;QACAC,WAAW,EAAE,IAAI,CAAC1I,iBAAiB,CAACmH,GAAI;QACxCwB,KAAK,EAAEC,QAAQ,CAAC,IAAI,CAAC/G,aAAa,CAACgH,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QACpDC,UAAU,EAAE,IAAI,CAACtH,aAAa,CAAC+F,UAAW;QAC1CwB,SAAS,EAAE,IAAI,CAACxG;;KAEnB,CAAC,CAACoE,IAAI,CACL5H,GAAG,CAAC6H,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACrC,gBAAgB,GAAGoC,GAAG,CAACE,OAAO,EAAEkC,SAAU;QAC/C,IAAIC,gBAAgB,GAAG;UACrBjC,WAAW,EAAE,IAAI,CAACnE,SAAS;UAC3B4E,SAAS,EAAE,IAAI,CAAClF,QAAQ;UACxBmF,KAAK,EAAE,IAAI,CAAC7F,aAAa;UACzBuF,WAAW,EAAE,IAAI,CAACpH,iBAAiB,CAACmH,GAAI;UACxCK,UAAU,EAAE,IAAI,CAAChG,aAAa,CAAC+F;SAChC;QACD9I,mBAAmB,CAACyK,eAAe,CAACxK,WAAW,CAACqI,UAAU,EAAEkC,gBAAgB,CAAC;QAC7ExK,mBAAmB,CAACyK,eAAe,CAACxK,WAAW,CAACuG,KAAK,EAAE2B,GAAG,CAACE,OAAQ,CAACqC,QAAS,CAAC;OAC/E,MAAM;QACL,IAAI,CAAClF,aAAa,CAACsE,YAAY,CAAC3B,GAAG,CAACwC,OAAQ,CAAC;QAC7C,IAAI,CAAC5D,SAAS,CAACC,KAAK,EAAE;;MAExBzG,cAAc,CAACyH,OAAO,CAAC,KAAK,CAAC;IAC/B,CAAC,CAAC,EACF9H,SAAS,CAAEiI,GAAG,IAAKA,GAAG,CAACE,OAAO,EAAEkC,SAAU,GAAG,IAAI,CAACK,SAAS,EAAE,GAAGvK,EAAE,CAAC,IAAI,CAAC,CAAC,CAC1E,CAACwI,SAAS,EAAE;EACf;EAEA+B,SAASA,CAAA;IACP,OAAO,IAAI,CAACnF,aAAa,CAACoF,8BAA8B,CAAC,EAAE,CAAC,CAAC3C,IAAI,CAC/D5H,GAAG,CAAC6H,GAAG,IAAG;MACR,IAAIA,GAAG,EAAEC,UAAW,IAAI,CAAC,EAAE;QACzBpI,mBAAmB,CAACyK,eAAe,CAACxK,WAAW,CAACsG,UAAU,EAAE4B,GAAG,EAAEE,OAAO,EAAEyC,gBAAiB,CAAC;QAC5F9K,mBAAmB,CAACyK,eAAe,CAACxK,WAAW,CAACqG,QAAQ,EAAE6B,GAAG,EAAEE,OAAO,EAAE0C,cAAe,CAAC;QACxF,IAAIC,OAAO,GAAa;UACtBC,IAAI,EAAE,IAAI,CAAClI,aAAa,CAAC+F,UAAW,GAAG,GAAG,GAAG,IAAI,CAAC1F,aAAa;UAC/D8H,gBAAgB,EAAE,KAAK;UACvBC,gBAAgB,EAAE;SACnB;QACDnL,mBAAmB,CAACyK,eAAe,CAACxK,WAAW,CAACwG,IAAI,EAAEuE,OAAO,CAAC;QAC9DhL,mBAAmB,CAACyK,eAAe,CAACxK,WAAW,CAACkG,OAAO,EAAE,OAAO,CAAC;QACjE,IAAI,CAACiF,QAAQ,EAAE;OAChB,MAAM;QACL,IAAI,CAAC5F,aAAa,CAACsE,YAAY,CAAC,MAAM,CAAC;QACvCvJ,cAAc,CAACyH,OAAO,CAAC,KAAK,CAAC;;IAEjC,CAAC,CAAC,CACH;EACH;EAEA2B,UAAUA,CAAA;IACR,IAAI,CAACpE,KAAK,CAAC8F,KAAK,EAAE;IAClB,IAAI,CAAC9F,KAAK,CAAC+F,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxH,QAAQ,CAAC;IAC1C;IACA;IACA;EACF;EAEAgB,eAAeA,CAACyG,KAAoB;IAClC,IAAI,EAAGA,KAAK,CAACC,OAAO,GAAG,EAAE,IAAID,KAAK,CAACC,OAAO,GAAG,EAAE,IAAMD,KAAK,CAACC,OAAO,GAAG,EAAE,IAAID,KAAK,CAACC,OAAO,GAAG,GAAI,IAAID,KAAK,CAACC,OAAO,IAAI,CAAC,IAAID,KAAK,CAACC,OAAO,IAAI,GAAG,IAAID,KAAK,CAACC,OAAO,IAAI,EAAE,CAAC,EAAE;MACrKD,KAAK,CAACE,cAAc,EAAE;;EAE1B;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACnG,KAAK,CAAC8F,KAAK,EAAE;IAClB,IAAI,CAAC9F,KAAK,CAAC+F,QAAQ,CAAC,SAAS,EAAE,IAAI,CAACtG,KAAK,CAAC;IAC1C,IAAI,CAACO,KAAK,CAAC+F,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC1G,MAAM,CAAC;IAC1C,IAAI,CAACW,KAAK,CAACoG,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC/G,MAAM,CAAC;IAC/C,IAAI,CAACW,KAAK,CAACqG,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC5G,KAAK,EAAE,IAAI,CAACW,OAAO,CAACkG,WAAW,CAAC;EACrE;EAEAT,QAAQA,CAAA;IACN,IAAI,CAACxF,aAAa,CAAC8D,IAAI,CAAC;MACtBoC,MAAM;MACNd,OAAO,EAAE;KACV,CAAC;IACFe,UAAU,CAAC,MAAK;MACd,IAAI,CAACzG,OAAO,CAAC0G,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM1L,cAAc,CAACyH,OAAO,CAAC,KAAK,CAAC,CAAC;IAC3E,CAAC,EAAE,GAAG,CAAC;EACT;EAEA9C,UAAUA,CAAA;IACR3E,cAAc,CAACyH,OAAO,CAAC,IAAI,CAAC;IAC5B,IAAI,CAAC0D,eAAe,EAAE;IACtB,IAAI,IAAI,CAACnG,KAAK,CAACqE,aAAa,CAACpF,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACe,KAAK,CAACqE,aAAa,CAACH,OAAO,CAACI,OAAO,IAAG;QACzC,IAAI,CAACrE,aAAa,CAACsE,YAAY,CAACD,OAAO,CAAC;MAC1C,CAAC,CAAC;MACFtJ,cAAc,CAACyH,OAAO,CAAC,KAAK,CAAC;MAC7B;;IAGF,IAAI,CAACvC,aAAa,CAACyG,gCAAgC,CAAC;MAClDlC,IAAI,EAAE;QACJhF,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBJ,MAAM,EAAE,IAAI,CAACA;;KAEhB,CAAC,CAACsD,IAAI,CACLhI,SAAS,CAAC,MAAM,IAAI,CAAC0K,SAAS,EAAE,CAAC,CAClC,CAAC/B,SAAS,EAAE;EACf;EAEAxG,eAAeA,CAAC8J,SAAc;IAC5B,IAAI,CAAC9I,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC9B,iBAAiB,GAAG,EAA6B;IACtD,IAAI,CAACwB,aAAa,GAAG,EAAsC;IAC3D,IAAI,CAACK,aAAa,GAAG,EAAS;IAC9B,IAAI,CAACwF,cAAc,CAACuD,SAAS,CAACzD,GAAG,CAAC,CAACG,SAAS,CAACV,GAAG,IAAG;MACjD,IAAI,CAACnF,sBAAsB,CAACmF,GAAG,CAACE,OAAQ,CAAC,CAAC,CAAC,CAAC;MAC5C,IAAI,CAACrB,KAAK,EAAE;IACd,CAAC,CAAC;EACJ;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACjE,aAAa,GAAG,IAAI,CAACE,kBAAkB,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACD,sBAAsB,CAAC,IAAI,CAACD,aAAa,CAAC;IAC/C,IAAI,CAACK,aAAa,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IACvC,IAAI,CAACS,QAAQ,GAAG,EAAE;EACpB;EAAC,QAAAsI,CAAA,G;qBAlSUhH,cAAc,EAAArE,EAAA,CAAAsL,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxL,EAAA,CAAAsL,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA1L,EAAA,CAAAsL,iBAAA,CAAAK,EAAA,CAAA9M,YAAA,GAAAmB,EAAA,CAAAsL,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAA7L,EAAA,CAAAsL,iBAAA,CAAAM,EAAA,CAAAE,gBAAA,GAAA9L,EAAA,CAAAsL,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAhM,EAAA,CAAAsL,iBAAA,CAAAW,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAd9H,cAAc;IAAA+H,SAAA;IAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;qCAPd,CACTzN,cAAc,EACdD,YAAY,CACb,GAAAmB,EAAA,CAAAyM,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAP,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC1CKvM,EAJR,CAAAC,cAAA,aAAqB,aACE,aACgC,aACmB,aACvB;QACzCD,EAAA,CAAAwC,UAAA,IAAAuK,6BAAA,iBAAkE;QAKlE/M,EAAA,CAAAC,cAAA,aAAuF;QAmGpFD,EAlGD,CAAAwC,UAAA,IAAAwK,qCAAA,QAAsB,IAAAC,qCAAA,QAkGd;QA0ClBjN,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF;QAENH,EAAA,CAAAE,SAAA,iBAA6E;QAzJ/EF,EAAA,CAAAG,YAAA,EAAqB;;;QAK4BH,EAAA,CAAAI,SAAA,GAAyB;QAAzBJ,EAAA,CAAAK,UAAA,WAAAmM,GAAA,CAAAhM,iBAAA,CAAyB;QAM9DR,EAAA,CAAAI,SAAA,GAuIC;QAvIDJ,EAAA,CAAAwD,aAAA,IAAAgJ,GAAA,CAAAxH,gBAAA,SAuIC;QAO2BhF,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAkN,UAAA,CAAAlN,EAAA,CAAAY,eAAA,IAAAuM,GAAA,EAA4B;;;mBD1HhEtN,YAAY,EAAAuN,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,OAAA,EACZ3O,cAAc,EAAA4O,EAAA,CAAAC,QAAA,EACd/O,WAAW,EAAAgP,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXhP,eAAe,EAAAiP,GAAA,CAAAC,SAAA,EACfpP,cAAc,EAAAqP,GAAA,CAAAC,QAAA,EACdjP,WAAW,EAAAkP,GAAA,CAAAC,KAAA,EACXlP,gBAAgB,EAChBU,eAAe,EACfD,oBAAoB,EACpBG,YAAY,EACZG,kBAAkB;IAAAoO,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}