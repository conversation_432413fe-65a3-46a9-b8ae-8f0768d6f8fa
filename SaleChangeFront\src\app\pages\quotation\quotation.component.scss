/* 導入共用樣式 */
@import '../../../style/main';

:host {
  display: block;
}

/* 主要容器 - 統一背景風格 */
.wrapper {
  min-height: 100vh;
  background: $bg-secondary;
}

.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 頁面標題 - 與其他頁面一致 */
.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: $text-primary;
  text-align: center;
  margin-bottom: 2rem;
}

/* API 資料狀態標示樣式 - 統一風格 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background: $bg-primary;
  border-radius: 12px;
  margin: 20px 0;
  box-shadow: $shadow-sm;
}

.loading-spinner {
  font-size: 18px;
  color: $primary-gold-light;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.error-container {
  text-align: center;
  padding: 40px;
  background: $bg-primary;
  border-radius: 12px;
  margin: 20px 0;
  border: 1px solid $warning;
  box-shadow: $shadow-sm;
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
  font-size: 16px;
  color: $error;
}

.status-draft {
  background: rgba($primary-gold-light, 0.15);
  color: $primary-gold-darker;
  border: 1px solid rgba($primary-gold-light, 0.25);
}

.status-sent {
  background: rgba($primary-gold-light, 0.1);
  color: $primary-gold-darker;
  border: 1px solid rgba($primary-gold-light, 0.25);
}

.status-approved {
  background: $success-light;
  color: $success;
  border: 1px solid rgba($success, 0.2);
}

.status-rejected {
  background: $error-light;
  color: $error;
  border: 1px solid rgba($error, 0.2);
}

.status-expired {
  background: $warning-light;
  color: darken($warning, 25%);
  border: 1px solid rgba($warning, 0.2);
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* 按鈕樣式 - 適應新的 header 風格 */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.btn-primary {
  background: linear-gradient(135deg, $primary-gold-light 0%, $primary-gold-dark 100%);
  color: $text-light;
  box-shadow: $shadow-md;
}

.btn-primary:hover {
  background: linear-gradient(135deg, $primary-gold-dark 0%, $primary-gold-darker 100%);
  box-shadow: $shadow-lg;
  transform: translateY(-1px);
}

.btn-secondary {
  background: $container-bg-hover;
  color: $text-primary;
  border: 1px solid $container-border-hover;
}

.btn-secondary:hover {
  background: rgba($primary-gold-light, 0.15);
  border-color: $container-border-selected;
  transform: translateY(-1px);
}

.btn-success {
  background: linear-gradient(135deg, $success 0%, rgba($success, 0.9) 100%);
  color: $text-light;
  box-shadow: rgba($success, 0.25) 0 2px 8px;
}

.btn-success:hover {
  background: linear-gradient(135deg, rgba($success, 0.95) 0%, rgba($success, 0.85) 100%);
  box-shadow: rgba($success, 0.35) 0 4px 12px;
  transform: translateY(-1px);
}

/* 主要內容區域 */
.quotation-content {
  width: 100%;
}

.quotation-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 報價單頭部區域 - 現代化卡片風格 */
.quotation-header {
  background: linear-gradient(135deg, $bg-primary 0%, rgba($bg-primary, 0.95) 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba($primary-gold-light, 0.1);
  margin-bottom: 2rem;
  overflow: hidden;
  position: relative;

  // 裝飾性背景
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 100%;
    background: linear-gradient(45deg, transparent 0%, rgba($primary-gold-light, 0.03) 100%);
    pointer-events: none;
  }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem;
  gap: 2rem;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1.5rem;
  }
}

.quotation-info {
  flex: 1;
  min-width: 0;
}

.quotation-main-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

// 報價單編號區域
.quotation-number-section {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1.5rem;
  }
}

// 欄位容器
.number-container,
.status-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-width: 0;
  flex: 1;

  @media (max-width: 768px) {
    width: 100%;
  }
}

// 欄位標題樣式
.field-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: $text-secondary;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;

  i {
    font-size: 1rem;
    color: $primary-gold-light;
    opacity: 0.8;
  }

  span {
    line-height: 1.2;
  }
}

.number-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, $primary-gold-light 0%, $primary-gold-dark 100%);
  color: $text-light;
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 700;
  box-shadow: 0 4px 16px rgba($primary-gold-light, 0.3);
  position: relative;
  min-height: 48px;
  transition: all 0.3s ease;

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
    border-radius: 12px;
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba($primary-gold-light, 0.4);
  }

  .number-text {
    font-size: 1.125rem;
    letter-spacing: 0.5px;
    line-height: 1.2;
    position: relative;
    z-index: 1;
  }

  @media (max-width: 768px) {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    min-height: 44px;

    .number-text {
      font-size: 1rem;
    }
  }
}

.status-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-height: 48px;

  @media (max-width: 768px) {
    min-height: 44px;
    justify-content: center;
  }
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: 48px;
  justify-content: center;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  i {
    font-size: 1rem;
  }

  @media (max-width: 768px) {
    padding: 0.625rem 1rem;
    font-size: 0.8rem;
    min-height: 44px;

    i {
      font-size: 0.875rem;
    }
  }
}

// 狀態徽章具體樣式
.status-badge {

  &.status-pending,
  &.status-draft {
    background: linear-gradient(135deg, rgba($primary-gold-light, 0.15) 0%, rgba($primary-gold-light, 0.08) 100%);
    color: $primary-gold-darker;
    border: 1px solid rgba($primary-gold-light, 0.25);
  }

  &.status-sent {
    background: linear-gradient(135deg, rgba($primary-gold-light, 0.12) 0%, rgba($primary-gold-light, 0.06) 100%);
    color: $primary-gold-darker;
    border: 1px solid rgba($primary-gold-light, 0.25);
  }

  &.status-approved,
  &.status-confirmed {
    background: linear-gradient(135deg, rgba($success, 0.15) 0%, rgba($success, 0.08) 100%);
    color: $success;
    border: 1px solid rgba($success, 0.25);
  }

  &.status-rejected {
    background: linear-gradient(135deg, rgba($error, 0.15) 0%, rgba($error, 0.08) 100%);
    color: $error;
    border: 1px solid rgba($error, 0.25);
  }

  &.status-expired {
    background: linear-gradient(135deg, rgba($warning, 0.15) 0%, rgba($warning, 0.08) 100%);
    color: darken($warning, 25%);
    border: 1px solid rgba($warning, 0.25);
  }
}

// 詳細資訊格線
.quotation-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.25rem;

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba($bg-tertiary, 0.3);
  border-radius: 12px;
  border: 1px solid rgba($primary-gold-light, 0.08);
  transition: all 0.3s ease;

  &:hover {
    background: rgba($bg-tertiary, 0.5);
    border-color: rgba($primary-gold-light, 0.15);
    transform: translateY(-1px);
  }

  &.amount-highlight {
    background: linear-gradient(135deg, rgba($primary-gold-light, 0.05) 0%, rgba($primary-gold-dark, 0.08) 100%);
    border-color: rgba($primary-gold-light, 0.2);

    &:hover {
      background: linear-gradient(135deg, rgba($primary-gold-light, 0.08) 0%, rgba($primary-gold-dark, 0.12) 100%);
      border-color: rgba($primary-gold-light, 0.3);
    }

    .detail-value {
      color: $primary-gold-darker;
      font-weight: 700;
    }
  }
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: $text-secondary;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  i {
    font-size: 1rem;
    opacity: 0.7;
  }
}

.detail-value {
  color: $text-primary;
  font-size: 1rem;
  font-weight: 600;

  &.amount-value {
    font-size: 1.25rem;
    font-weight: 700;
  }
}

// 操作按鈕容器 - 獨立區塊設計
.header-actions-container {
  background: linear-gradient(135deg, rgba($primary-gold-light, 0.03) 0%, rgba($primary-gold-light, 0.01) 100%);
  border: 2px solid rgba($primary-gold-light, 0.15);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba($primary-gold-light, 0.08);
  position: relative;
  overflow: hidden;
  min-width: 320px;

  // 裝飾性背景元素
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba($primary-gold-light, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
  }

  @media (max-width: 768px) {
    min-width: auto;
    width: 100%;
    padding: 1.25rem;
  }
}

.actions-header {
  margin-bottom: 1.25rem;
  position: relative;
  z-index: 1;

  .actions-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: $primary-gold-darker;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    i {
      font-size: 1.125rem;
      color: $primary-gold-light;
    }
  }
}

// 操作按鈕區域
.header-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    gap: 0.625rem;
  }
}

// 按鈕樣式重新設計 - 更突出的設計
.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  min-width: 120px;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }

  i {
    font-size: 1rem;
  }

  span {
    white-space: nowrap;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
}

// 新的動作按鈕樣式 - 更大更突出
.btn-action {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-radius: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  min-width: 200px;
  justify-content: flex-start;
  border: 2px solid transparent;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }

  .btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.15);
    flex-shrink: 0;
    transition: all 0.3s ease;

    i {
      font-size: 1.25rem;
    }
  }

  .btn-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    flex: 1;

    .btn-label {
      font-size: 0.9rem;
      font-weight: 600;
      line-height: 1.2;
    }

    .btn-description {
      font-size: 0.75rem;
      opacity: 0.8;
      font-weight: 400;
      line-height: 1.2;
    }
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);

    .btn-icon {
      background: rgba(255, 255, 255, 0.25);
      transform: scale(1.05);
    }
  }

  &:active {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    min-width: auto;
    padding: 0.875rem 1.25rem;
    gap: 0.875rem;

    .btn-icon {
      width: 36px;
      height: 36px;

      i {
        font-size: 1.125rem;
      }
    }

    .btn-content {
      .btn-label {
        font-size: 0.85rem;
      }

      .btn-description {
        font-size: 0.7rem;
      }
    }
  }
}

.btn-outline {
  background: $bg-primary;
  color: $text-primary;
  border: 2px solid rgba($primary-gold-light, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:hover {
    background: rgba($primary-gold-light, 0.05);
    border-color: rgba($primary-gold-light, 0.4);
    color: $primary-gold-darker;
  }

  // 動作按鈕的 outline 樣式
  &.btn-action {
    background: linear-gradient(135deg, $bg-primary 0%, rgba($bg-primary, 0.95) 100%);
    border: 2px solid rgba($primary-gold-light, 0.25);
    color: $text-primary;
    box-shadow: 0 4px 16px rgba($primary-gold-light, 0.1);

    .btn-icon {
      background: linear-gradient(135deg, rgba($primary-gold-light, 0.15) 0%, rgba($primary-gold-light, 0.08) 100%);
      border: 1px solid rgba($primary-gold-light, 0.2);

      i {
        color: $primary-gold-darker;
      }
    }

    .btn-content {
      .btn-label {
        color: $text-primary;
      }

      .btn-description {
        color: $text-secondary;
      }
    }

    &:hover {
      background: linear-gradient(135deg, rgba($primary-gold-light, 0.08) 0%, rgba($primary-gold-light, 0.05) 100%);
      border-color: rgba($primary-gold-light, 0.4);
      box-shadow: 0 8px 25px rgba($primary-gold-light, 0.2);

      .btn-icon {
        background: linear-gradient(135deg, rgba($primary-gold-light, 0.25) 0%, rgba($primary-gold-light, 0.15) 100%);
        border-color: rgba($primary-gold-light, 0.3);
      }

      .btn-content .btn-label {
        color: $primary-gold-darker;
      }
    }
  }
}

.btn-primary {
  background: linear-gradient(135deg, $primary-gold-light 0%, $primary-gold-dark 100%);
  color: $text-light;
  box-shadow: 0 4px 16px rgba($primary-gold-light, 0.3);

  &:hover {
    background: linear-gradient(135deg, $primary-gold-dark 0%, $primary-gold-darker 100%);
    box-shadow: 0 6px 20px rgba($primary-gold-light, 0.4);
  }

  // 動作按鈕的 primary 樣式
  &.btn-action {
    background: linear-gradient(135deg, $primary-gold-light 0%, $primary-gold-dark 100%);
    color: $text-light;
    box-shadow: 0 6px 20px rgba($primary-gold-light, 0.3);
    border: 2px solid rgba($primary-gold-dark, 0.3);

    .btn-icon {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.15);

      i {
        color: $text-light;
      }
    }

    .btn-content {

      .btn-label,
      .btn-description {
        color: $text-light;
      }
    }

    &:hover {
      background: linear-gradient(135deg, $primary-gold-dark 0%, $primary-gold-darker 100%);
      box-shadow: 0 10px 30px rgba($primary-gold-light, 0.4);
      border-color: rgba($primary-gold-darker, 0.4);

      .btn-icon {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.25);
      }
    }
  }
}

.btn-success {
  background: linear-gradient(135deg, $success 0%, rgba($success, 0.9) 100%);
  color: $text-light;
  box-shadow: 0 4px 16px rgba($success, 0.25);

  &:hover {
    background: linear-gradient(135deg, rgba($success, 0.95) 0%, rgba($success, 0.85) 100%);
    box-shadow: 0 6px 20px rgba($success, 0.35);
  }

  // 動作按鈕的 success 樣式
  &.btn-action {
    background: linear-gradient(135deg, $success 0%, rgba($success, 0.9) 100%);
    color: $text-light;
    box-shadow: 0 6px 20px rgba($success, 0.25);
    border: 2px solid rgba($success, 0.3);

    .btn-icon {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.15);

      i {
        color: $text-light;
      }
    }

    .btn-content {

      .btn-label,
      .btn-description {
        color: $text-light;
      }
    }

    &:hover {
      background: linear-gradient(135deg, rgba($success, 0.95) 0%, rgba($success, 0.85) 100%);
      box-shadow: 0 10px 30px rgba($success, 0.35);
      border-color: rgba($success, 0.4);

      .btn-icon {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.25);
      }
    }
  }
}

/* 區塊樣式 - 統一卡片風格 */
.section {
  margin-bottom: 1.5rem;
}

.section-card {
  background: $bg-primary;
  border-radius: 12px;
  box-shadow: $shadow-sm;
  overflow: hidden;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: $text-primary;
  margin: 0;
  padding: 1.5rem 1.5rem 0;
}

/* 區塊標題區域 */
.section-header {
  .section-title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 0;

    .section-title {
      padding: 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      i {
        color: $primary-gold-dark;
        font-size: 1.1rem;
      }
    }

    .items-count-badge {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      background: linear-gradient(135deg, rgba($primary-gold-light, 0.1), rgba($primary-gold-light, 0.05));
      border: 1px solid rgba($primary-gold-light, 0.2);
      padding: 0.5rem 0.75rem;
      border-radius: 20px;
      font-size: 0.875rem;
      color: $primary-gold-darker;
      font-weight: 500;
      transition: all 0.2s ease;

      i {
        font-size: 0.8rem;
        color: $primary-gold-dark;
      }

      .count-text {
        font-weight: 600;
        color: $primary-gold-darker;
        min-width: 1.2rem;
        text-align: center;
      }

      .count-label {
        color: $text-secondary;
        margin-left: 0.1rem;
      }

      &:hover {
        background: linear-gradient(135deg, rgba($primary-gold-light, 0.15), rgba($primary-gold-light, 0.08));
        border-color: rgba($primary-gold-light, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba($primary-gold-light, 0.15);
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;

      .items-count-badge {
        align-self: flex-end;
      }
    }
  }
}

/* 報價項目表格 - 統一風格 */
.items-table {
  margin: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid $border-light;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr 1.5fr 1.5fr;
  background: $bg-tertiary;
  font-weight: 600;
  color: $text-primary;

  >div {
    padding: 1rem;
    border-right: 1px solid $border-light;

    &:last-child {
      border-right: none;
    }
  }
}

.table-body {
  .table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr 1.5fr 1.5fr;
    border-bottom: 1px solid $border-light;
    transition: background-color 0.2s ease;

    &:hover {
      background: $bg-secondary;
    }

    &.row-even {
      background: rgba($bg-secondary, 0.5);
    }

    &:last-child {
      border-bottom: none;
    }

    >div {
      padding: 1rem;
      border-right: 1px solid $border-light;
      display: flex;
      align-items: center;

      &:last-child {
        border-right: none;
      }
    }
  }
}

/* 金額計算區域 - 統一風格 */
.calculation-table {
  margin: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid $border-light;
}

.calc-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid $border-light;

  &:last-child {
    border-bottom: none;
  }

  &.total-row {
    background: $bg-tertiary;
    font-weight: 600;
    font-size: 1.125rem;
  }

  &.other-fee-row {
    background: rgba(255, 193, 7, 0.1); // 淡黃色背景，突出額外費用
    border-left: 3px solid #ffc107; // 左側黃色邊框

    .calc-label {
      color: #856404; // 深黃色文字
      font-weight: 500;

      i {
        color: #ffc107; // 黃色圖示
      }
    }

    .calc-value {
      color: #856404; // 深黃色數字
      font-weight: 600;
    }
  }

  label {
    color: $text-primary;
    font-weight: 500;
  }

  span {
    color: $text-primary;
    font-weight: 600;
  }
}

.total-amount {
  color: $primary-gold-dark;
  font-size: 1.25rem;
}

/* 備註區域 - 統一風格 */
.notes-content {
  padding: 1.5rem;

  .note-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: $bg-secondary;
    border-radius: 6px;

    &:last-child {
      margin-bottom: 0;
    }

    i {
      color: $primary-gold-light;
      font-size: 1.125rem;
    }

    span {
      color: $text-primary;
      font-weight: 500;
    }
  }
}

/* Dialog 中的狀態樣式 */
.dialog-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  padding: 40px;

  .loading-spinner {
    font-size: 16px;
    color: $primary-gold-light;
    animation: pulse 1.5s ease-in-out infinite;
  }
}

.dialog-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  padding: 3rem;
  background: linear-gradient(135deg, rgba($error, 0.05) 0%, $bg-primary 100%);

  .error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    text-align: center;

    i {
      font-size: 3rem;
      color: $error;
    }

    .error-text {
      font-size: 1rem;
      color: $error;
      max-width: 300px;
    }
  }
}

.dialog-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  padding: 3rem;
  background: linear-gradient(135deg, $bg-primary 0%, rgba($bg-secondary, 0.3) 100%);

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    text-align: center;

    i {
      font-size: 3rem;
      color: $text-secondary;
      opacity: 0.6;
    }

    span {
      font-size: 1.125rem;
      color: $text-secondary;
      font-weight: 500;
    }
  }
}

/* 版本歷程 Dialog 樣式 */
::ng-deep .p-dialog .p-dialog-content {
  padding: 0 !important;
  overflow: hidden;
}

.version-dialog-content {
  width: 700px;
  max-height: 70vh;
  overflow: hidden;

  @media (max-width: 768px) {
    width: 95vw;
    max-height: 80vh;
  }
}

.version-summary {
  padding: 20px 24px 16px;
  background: linear-gradient(135deg, $primary-gold-light 0%, rgba($primary-gold-light, 0.8) 100%);
  border-bottom: 1px solid rgba($primary-gold-dark, 0.2);
  color: white;

  .summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .total-count {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      background: rgba(255, 255, 255, 0.15);
      padding: 4px 12px;
      border-radius: 20px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
  }

  .current-version-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .current-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
    }

    .current-version {
      font-weight: 600;
      color: white;
      background: rgba(255, 255, 255, 0.2);
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 14px;
    }
  }
}

.version-list-container {
  max-height: 50vh;
  overflow-y: auto;
  padding: 8px 0;

  /* 自定義滾動條樣式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: $bg-secondary;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: $primary-gold-light;
    border-radius: 3px;

    &:hover {
      background: $primary-gold-dark;
    }
  }
}

.version-items {
  .version-item {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid $border-light;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;

    &:hover {
      background: linear-gradient(135deg, rgba($primary-gold-light, 0.08) 0%, rgba($primary-gold-light, 0.05) 100%);
    }

    &.active {
      background: linear-gradient(135deg, rgba($primary-gold-light, 0.15) 0%, rgba($primary-gold-light, 0.1) 100%);
      border-left: 4px solid $primary-gold-dark;
      padding-left: 20px;

      .version-arrow {
        display: block;
      }
    }

    &.current {
      .version-current-badge {
        display: inline-flex;
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .version-content {
    flex: 1;

    .version-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .version-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .version-name {
          font-weight: 600;
          color: $text-primary;
          font-size: 16px;
        }

        .version-current-badge {
          display: none;
          align-items: center;
          gap: 4px;
          background: linear-gradient(135deg, $primary-gold-dark 0%, $primary-gold-darker 100%);
          color: white;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          box-shadow: 0 2px 4px rgba($primary-gold-dark, 0.3);

          i {
            font-size: 10px;
          }
        }
      }

      .version-status {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;

        &.status-pending {
          background: linear-gradient(135deg, rgba($warning, 0.15) 0%, rgba($warning, 0.1) 100%);
          color: darken($warning, 15%);
          border: 1px solid rgba($warning, 0.2);
        }

        &.status-quoted {
          background: linear-gradient(135deg, rgba($primary-gold-light, 0.15) 0%, rgba($primary-gold-light, 0.1) 100%);
          color: $primary-gold-darker;
        }

        &.status-confirmed {
          background: rgba($success, 0.1);
          color: $success;
        }

        &.status-sent {
          background: rgba($primary-gold-light, 0.1);
          color: $primary-gold-dark;
        }

        &.status-approved {
          background: rgba($success, 0.1);
          color: $success;
        }

        &.status-rejected {
          background: rgba($error, 0.1);
          color: $error;
        }

        &.status-expired {
          background: rgba($text-secondary, 0.1);
          color: $text-secondary;
        }
      }
    }

    .version-details {
      .version-meta {
        margin-bottom: 12px;

        .version-date {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
          color: $text-secondary;

          i {
            font-size: 12px;
          }
        }
      }

      .version-summary-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .version-amount {
          display: flex;
          flex-direction: column;

          .amount-label {
            font-size: 12px;
            color: $text-secondary;
            margin-bottom: 2px;
          }

          .amount-value {
            font-weight: 600;
            color: $text-primary;
            font-size: 14px;
          }
        }

        .version-items-count {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: $text-secondary;
          background: $bg-secondary;
          padding: 4px 8px;
          border-radius: 4px;

          i {
            font-size: 10px;
          }
        }
      }
    }
  }

  .version-arrow {
    display: none;
    margin-left: 12px;
    color: $primary-gold-dark;

    i {
      font-size: 16px;
      animation: bounce 1s ease-in-out infinite;
    }
  }
}

/* 版本歷程 Dialog - 版本清單樣式 */
.version-list {
  max-height: 50vh;
  overflow-y: auto;
  padding: 0;
  margin: 0;

  .version-item {
    padding: 12px 16px;
    border-bottom: 1px solid $border-light;
    cursor: pointer;
    transition: all $transition-normal;
    background: $bg-primary;
    position: relative;
    box-sizing: border-box;

    &:hover {
      background: $container-bg-hover;
      border-left: 3px solid $primary-gold-light;
      padding-left: 13px;
      box-shadow: $shadow-sm;
    }

    &.selected {
      background: linear-gradient(135deg, rgba($primary-gold-light, 0.08) 0%, rgba($primary-gold-light, 0.03) 100%);
      border-left: 3px solid $primary-gold-dark;
      padding-left: 13px;
      box-shadow: $shadow-md;

      .version-header {
        .version-number span {
          color: $primary-gold-darker;
          font-weight: 600;
        }
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .version-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    gap: 12px;

    .version-number {
      display: flex;
      align-items: center;
      gap: 6px;
      font-weight: 500;
      color: $text-primary;
      flex: 1;
      overflow: hidden;

      i {
        color: $primary-gold-light;
        font-size: 14px;
        flex-shrink: 0;
      }

      span {
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.2;
      }
    }

    .version-status {
      flex-shrink: 0;

      .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        font-size: 11px;
        padding: 3px 8px;
        border-radius: 10px;
        white-space: nowrap;

        &.status-pending {
          background: $warning-light;
          color: darken($warning, 20%);
        }

        &.status-quoted {
          background: rgba($primary-gold-light, 0.1);
          color: $primary-gold-darker;
        }

        &.status-confirmed {
          background: $success-light;
          color: darken($success, 10%);
        }

        &.status-sent {
          background: rgba($primary-gold-light, 0.15);
          color: $primary-gold-darker;
        }

        &.status-approved {
          background: $success-light;
          color: $success;
        }

        &.status-rejected {
          background: $error-light;
          color: darken($error, 10%);
        }

        &.status-expired {
          background: rgba($warning, 0.1);
          color: darken($warning, 30%);
        }

        i {
          font-size: 10px;
        }
      }
    }
  }

  .version-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    color: $text-secondary;
    font-size: 12px;
    gap: 12px;

    .version-date,
    .version-amount {
      display: flex;
      align-items: center;
      gap: 4px;
      white-space: nowrap;

      i {
        font-size: 12px;
        flex-shrink: 0;
        color: $primary-gold-light;
      }
    }

    .version-amount {
      font-weight: 500;
      color: $text-primary;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
    }
  }

  .version-items {
    .items-count {
      display: flex;
      align-items: center;
      gap: 4px;
      color: $text-secondary;
      font-size: 11px;

      i {
        font-size: 10px;
        flex-shrink: 0;
      }
    }
  }

  .version-other-fee {
    .other-fee-info {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #856404; // 深黃色文字
      font-size: 11px;
      font-weight: 500;
      background: rgba(255, 193, 7, 0.1); // 淡黃色背景
      padding: 2px 6px;
      border-radius: 4px;
      margin-top: 4px;

      i {
        font-size: 10px;
        color: #ffc107; // 黃色圖示
        flex-shrink: 0;
      }
    }
  }
}

/* 自定義滾動條樣式 */
.version-list::-webkit-scrollbar {
  width: 6px;
}

.version-list::-webkit-scrollbar-track {
  background: $bg-secondary;
  border-radius: 3px;
}

.version-list::-webkit-scrollbar-thumb {
  background: $primary-gold-light;
  border-radius: 3px;

  &:hover {
    background: $primary-gold-dark;
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(0);
  }

  40% {
    transform: translateX(-3px);
  }

  60% {
    transform: translateX(-1px);
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .version-dialog-content {
    .version-items {
      .version-item {
        padding: 12px 16px;

        &.active {
          padding-left: 12px;
        }
      }

      .version-content {
        .version-header {
          .version-title {
            .version-name {
              font-size: 14px;
            }
          }
        }

        .version-details {
          .version-summary-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
        }
      }
    }
  }
}

/* 列印樣式 */
@media print {
  .wrapper {
    background: white !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  /* 隱藏不需要列印的元素 */
  .header-actions,
  .loading-container,
  .error-container {
    display: none !important;
  }

  /* 隱藏版本歷程按鈕 */
  .btn {
    display: none !important;
  }

  /* 調整頁面佈局 */
  .content {
    max-width: none;
    margin: 0;
    padding: 0;
  }

  /* 確保表格在列印時完整顯示 */
  .items-table,
  .calculation-table {
    page-break-inside: avoid;
  }

  /* 調整字體大小 */
  .page-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 1.2rem;
  }

  /* 移除陰影和背景 */
  .section-card {
    box-shadow: none !important;
    border: 1px solid #ddd;
    background: white !important;
  }

  /* 確保文字顏色在列印時清晰 */
  * {
    color: black !important;
    background: transparent !important;
  }

  /* 保持重要的背景顏色 */
  .table-header {
    background: #f5f5f5 !important;
    color: black !important;
  }

  .total-row {
    border-top: 2px solid black !important;
    font-weight: bold !important;
  }
}

/* 響應式設計 - 與其他頁面一致 */
@media (max-width: $main-mobileL) {
  .quotation-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;

    >div {
      border-right: none;
      border-bottom: 1px solid $border-light;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .table-header>div::before,
  .table-row>div::before {
    content: attr(data-label) ': ';
    font-weight: 600;
    color: $text-secondary;
    margin-right: 0.5rem;
  }

  /* 移動端項目計數徽章優化 */
  .section-header .section-title-wrapper {
    .items-count-badge {
      font-size: 0.8rem;
      padding: 0.4rem 0.6rem;

      .count-text {
        min-width: 1rem;
        font-size: 0.85rem;
      }

      .count-label {
        font-size: 0.75rem;
      }
    }
  }
}

@media (max-width: $main-mobileM) {
  .content {
    padding: 1rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .btn {
    min-width: 100px;
    font-size: 0.75rem;
  }

  .quotation-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  /* 小螢幕項目計數進一步優化 */
  .section-header .section-title-wrapper {
    padding: 1rem 1rem 0;

    .section-title {
      font-size: 1.1rem;

      i {
        font-size: 1rem;
      }
    }

    .items-count-badge {
      font-size: 0.75rem;
      padding: 0.35rem 0.5rem;
      border-radius: 16px;

      i {
        font-size: 0.7rem;
      }

      .count-text {
        font-size: 0.8rem;
        min-width: 0.8rem;
      }

      .count-label {
        font-size: 0.7rem;
      }
    }
  }
}

/* 版本歷程 Dialog - 狀態樣式 */
.dialog-loading,
.dialog-error,
.dialog-empty {
  padding: 40px 20px;
  text-align: center;
  color: $text-secondary;
}

.dialog-loading {
  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    i {
      font-size: 24px;
      color: $primary-gold-light;
      animation: spin 1s linear infinite;
    }

    span {
      font-size: 14px;
      color: $text-primary;
    }
  }
}

.dialog-error {
  .error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;

    i {
      font-size: 32px;
      color: $error;
    }

    .error-text {
      color: $text-primary;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .btn {
      padding: 8px 16px;
      font-size: 14px;
      border-radius: 6px;
      border: none;
      cursor: pointer;
      transition: background-color $transition-normal;

      &.btn-primary {
        background: $gradient-primary;
        color: $text-light;
        box-shadow: $shadow-md;

        &:hover {
          background: $gradient-primary-hover;
          box-shadow: $shadow-lg;
        }
      }

      i {
        font-size: 14px;
        margin-right: 6px;
      }
    }
  }
}

.dialog-empty {
  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    i {
      font-size: 32px;
      color: $text-secondary;
    }

    span {
      color: $text-secondary;
      font-size: 14px;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

// 響應式設計和動畫效果
@media (max-width: 1024px) {
  .quotation-header {
    .header-content {
      padding: 1.5rem;
    }

    .quotation-details-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .header-actions-container {
      min-width: 280px;
      padding: 1.25rem;

      .actions-title {
        font-size: 0.9rem;
      }
    }

    .header-actions {
      gap: 0.625rem;

      .btn {
        padding: 0.625rem 1rem;
        font-size: 0.8125rem;
        min-width: 100px;
      }

      .btn-action {
        min-width: 180px;
        padding: 0.875rem 1.25rem;

        .btn-icon {
          width: 36px;
          height: 36px;

          i {
            font-size: 1.125rem;
          }
        }

        .btn-content {
          .btn-label {
            font-size: 0.85rem;
          }

          .btn-description {
            font-size: 0.7rem;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .quotation-header {
    .header-content {
      padding: 1rem;
    }

    .quotation-number-section {
      flex-direction: column;
      align-items: stretch;
      gap: 1.25rem;

      .number-container,
      .status-container {
        width: 100%;
        align-items: center;
        text-align: center;

        .field-title {
          justify-content: center;
          margin-bottom: 0.5rem;
        }
      }
    }

    .number-badge {
      padding: 0.625rem 1rem;
      font-size: 1rem;

      .number-text {
        font-size: 1rem;
      }
    }

    .header-actions-container {
      min-width: auto;
      width: 100%;
      padding: 1rem;

      .actions-title {
        font-size: 0.85rem;
        text-align: center;
      }
    }

    .header-actions {
      width: 100%;

      .btn {
        flex: 1;
        min-width: auto;
      }

      .btn-action {
        min-width: auto;
        width: 100%;
        padding: 0.75rem 1rem;
        gap: 0.75rem;

        .btn-icon {
          width: 32px;
          height: 32px;

          i {
            font-size: 1rem;
          }
        }

        .btn-content {
          .btn-label {
            font-size: 0.8rem;
          }

          .btn-description {
            font-size: 0.65rem;
          }
        }
      }
    }
  }
}

// 動畫關鍵幀
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 應用動畫
.quotation-header {
  animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-item {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  &:nth-child(1) {
    animation-delay: 0.1s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }
}

.btn {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  &:nth-child(1) {
    animation-delay: 0.3s;
  }

  &:nth-child(2) {
    animation-delay: 0.4s;
  }

  &:nth-child(3) {
    animation-delay: 0.5s;
  }
}