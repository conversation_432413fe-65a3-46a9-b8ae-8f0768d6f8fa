{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { finalize, forkJoin, tap } from 'rxjs';\nimport { LoadingService } from '../../shared/services/loading.service';\nimport { STORAGE_KEY } from '../../shared/constant/constant';\nimport { LocalStorageService } from '../../shared/services/local-storage.service';\nimport { StepIiChoiceComponent } from './components/step-ii-choice/step-ii-choice.component';\nimport { StepIiiChoiceComponent } from './components/step-iii-choice/step-iii-choice.component';\nimport { StepIvChoiceComponent } from './components/step-iv-choice/step-iv-choice.component';\nimport { DialogPopupComponent } from '../../components/dialog-popup/dialog-popup.component';\nimport { ToastMessage } from '../../shared/services/message.service';\nimport { ToastModule } from 'primeng/toast';\nimport { MessageService } from 'primeng/api';\nimport { CONTENT_OPTIONS } from '../../shared/constant/constant';\nimport { HeaderSteppersComponent } from './components/header-steppers/header-steppers.component';\nimport { StepVChoiceComponent } from './components/step-v-choice/step-v-choice.component';\nimport { StepViChoiceComponent } from './components/step-vi-choice/step-vi-choice.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/api/services\";\nimport * as i3 from \"../../shared/services/message.service\";\nimport * as i4 from \"primeng/toast\";\nconst _c0 = () => ({\n  \"width\": \"22rem\"\n});\nfunction ChoiceComponent_Case_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-step-iv-choice\", 9);\n    i0.ɵɵtwoWayListener(\"agreeToGoNextStep4Change\", function ChoiceComponent_Case_9_Template_app_step_iv_choice_agreeToGoNextStep4Change_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.agreeToGoNextStep4, $event) || (ctx_r1.agreeToGoNextStep4 = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"nextEvent\", function ChoiceComponent_Case_9_Template_app_step_iv_choice_nextEvent_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.next());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"agreeToGoNextStep4\", ctx_r1.agreeToGoNextStep4);\n  }\n}\nfunction ChoiceComponent_Case_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-step-v-choice\", 10);\n    i0.ɵɵlistener(\"nextEvent\", function ChoiceComponent_Case_10_Template_app_step_v_choice_nextEvent_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.next());\n    });\n    i0.ɵɵtwoWayListener(\"listHouseReviewChange\", function ChoiceComponent_Case_10_Template_app_step_v_choice_listHouseReviewChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.listHouseReview, $event) || (ctx_r1.listHouseReview = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"refreshHouseReview\", function ChoiceComponent_Case_10_Template_app_step_v_choice_refreshHouseReview_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refreshListHouseReview());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"listHouseReview\", ctx_r1.listHouseReview);\n  }\n}\nfunction ChoiceComponent_Case_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-step-ii-choice\", 11);\n    i0.ɵɵlistener(\"nextEvent\", function ChoiceComponent_Case_11_Template_app_step_ii_choice_nextEvent_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.next());\n    });\n    i0.ɵɵtwoWayListener(\"listRegularChangeItemChange\", function ChoiceComponent_Case_11_Template_app_step_ii_choice_listRegularChangeItemChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.listRegularChangeItem, $event) || (ctx_r1.listRegularChangeItem = $event);\n      return i0.ɵɵresetView($event);\n    })(\"currentTypeUIChange\", function ChoiceComponent_Case_11_Template_app_step_ii_choice_currentTypeUIChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.currentTypeUI, $event) || (ctx_r1.currentTypeUI = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"refreshListRegular\", function ChoiceComponent_Case_11_Template_app_step_ii_choice_refreshListRegular_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refreshListRegular());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"isDisabledOptionStep2\", ctx_r1.isDisabledOptionStep2);\n    i0.ɵɵtwoWayProperty(\"listRegularChangeItem\", ctx_r1.listRegularChangeItem)(\"currentTypeUI\", ctx_r1.currentTypeUI);\n  }\n}\nfunction ChoiceComponent_Case_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-step-iii-choice\", 12);\n    i0.ɵɵlistener(\"nextEvent\", function ChoiceComponent_Case_12_Template_app_step_iii_choice_nextEvent_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.next());\n    })(\"goBackEvent\", function ChoiceComponent_Case_12_Template_app_step_iii_choice_goBackEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBackEvent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selectedData\", ctx_r1.selectedData);\n  }\n}\nfunction ChoiceComponent_Case_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-step-vi-choice\", 13);\n    i0.ɵɵlistener(\"nextEvent\", function ChoiceComponent_Case_13_Template_app_step_vi_choice_nextEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.next($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"listHouseRequirement\", ctx_r1.listHouseRequirement);\n  }\n}\nexport class ChoiceComponent {\n  constructor(_router, houseService, _toastService, _regularChangeService) {\n    this._router = _router;\n    this.houseService = houseService;\n    this._toastService = _toastService;\n    this._regularChangeService = _regularChangeService;\n    this.dataUser = LocalStorageService.GetLocalStorage(STORAGE_KEY.USER);\n    this.step = 0;\n    //step 1: data\n    this.agreeToGoNextStep4 = false;\n    //step 2: data\n    this.listRegularChangeItem = [];\n    this.currentTypeUI = 0;\n    //step 3: data\n    this.selectedData = [];\n    //step 4: data\n    this.listHouseReview = [];\n    //step 5: data\n    this.listHouseRequirement = [];\n    //handle popup\n    this.visible = false;\n    this.disclaimer = \"\";\n    this.textData = CONTENT_OPTIONS['default'];\n    this.isDisabledOptionStep2 = false;\n    this.listSteps = [{\n      id: 1,\n      name: \"客變原則\",\n      isFinished: true\n    }, {\n      id: 2,\n      name: \"標準圖面審閱\",\n      isFinished: false\n    }, {\n      id: 3,\n      name: \"建材選樣選色\",\n      isFinished: false\n    }, {\n      id: 4,\n      name: \"選樣選擇結果確認\",\n      isFinished: false\n    }, {\n      id: 5,\n      name: \"客變需求確認\",\n      isFinished: false\n    }];\n    this.sign_info = LocalStorageService.GetLocalStorage(STORAGE_KEY.SIGN_INFO);\n    this.step = LocalStorageService.GetLocalStorage(STORAGE_KEY.NUMBER_STEP) || 0;\n    this.isDisabledOptionStep2 = LocalStorageService.GetLocalStorage(STORAGE_KEY.IS_LOCK);\n  }\n  ngOnInit() {\n    if (this.step >= 0) {\n      this.agreeToGoNextStep4 = true;\n    }\n    for (let index = 1; index <= this.step; index++) {\n      const element = this.listSteps[index - 1];\n      element.isFinished = true;\n      if (index == 1) {\n        this.getListRegularChangeItem().subscribe();\n      }\n      if (index == 2) {\n        this.getDataStep3().subscribe();\n      }\n      if (index == 3) {\n        this.getHouseReview().subscribe();\n      }\n      if (index == 4) {\n        this.getHouseRequirement();\n      }\n      if (index == 5) {\n        this.step = 4;\n      }\n    }\n  }\n  //Step 2\n  getListRegularChangeItem() {\n    LoadingService.loading(true);\n    return this._regularChangeService.apiRegularChangeItemGetListRegularChangeItemPost$Json({}).pipe(tap(res => {\n      this.listRegularChangeItem = res.Entries ?? [];\n    }), finalize(() => LoadingService.loading(false)));\n  }\n  //Step 3\n  getDataStep3() {\n    LoadingService.loading(true);\n    return this._regularChangeService.apiRegularChangeItemGetSumaryRegularChangeItemPost$Json().pipe(tap(res => {\n      this.selectedData = res.Entries ?? [];\n    }), finalize(() => LoadingService.loading(false)));\n  }\n  //Step 4\n  getHouseReview() {\n    LoadingService.loading(true);\n    return this.houseService.apiHouseGetHouseReviewPost$Json({}).pipe(tap(res => {\n      this.listHouseReview = res.Entries ?? [];\n    }), finalize(() => LoadingService.loading(false)));\n  }\n  //step 5\n  getHouseRequirement() {\n    LoadingService.loading(true);\n    this.houseService.apiHouseGetHouseRequirementPost$Json({}).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listHouseRequirement = res.Entries ?? [];\n      }\n    }), finalize(() => LoadingService.loading(false))).subscribe();\n  }\n  goBackEvent(event) {\n    this.step--;\n    this.currentTypeUI = event;\n  }\n  next(event) {\n    if (this.step == 0) {\n      this.getListRegularChangeItem().subscribe();\n    }\n    if (this.step == 1) {\n      this.getDataStep3().subscribe();\n    }\n    if (this.step == 2) {\n      this.getHouseReview().subscribe();\n    }\n    if (this.step == 3) {\n      this.getHouseRequirement();\n    }\n    if (this.step == 4) {\n      this.visible = true;\n      this.updateProgress(2);\n      this.saveMileStone(5);\n      LoadingService.loading(false);\n      if (event) {\n        this.textData = CONTENT_OPTIONS['remind'];\n      } else {\n        this.textData = CONTENT_OPTIONS['confirm'];\n      }\n      return;\n    }\n    this.step++;\n    this.listSteps[this.step - 1].isFinished = true;\n    this.saveMileStone();\n  }\n  back() {\n    this.step--;\n  }\n  handleEventBack(isAvailable) {\n    if (!this.dataUser.isFinishBuilding && !isAvailable) {\n      this._router.navigateByUrl('home');\n      return;\n    }\n    let payload = {\n      name: this.dataUser.name,\n      isFinishBuilding: isAvailable,\n      acceptChangeTime: this.dataUser.acceptChangeTime\n    };\n    LocalStorageService.AddLocalStorage(STORAGE_KEY.USER, payload);\n  }\n  handleNavigate() {\n    if (this.textData.title == \"sign\") {\n      this._router.navigateByUrl('sign');\n    } else {\n      this.visible = true;\n      this.textData = CONTENT_OPTIONS['disclaimer'];\n    }\n  }\n  actionButtonLeft() {\n    if (this.textData.title == 'confirmChangeStep') {\n      this.visible = false;\n    }\n    switch (this.textData.title) {\n      case 'remind':\n        this.visible = false;\n        break;\n      default:\n    }\n  }\n  actionButtonRight() {\n    switch (this.textData.title) {\n      case 'confirm':\n        this.agreeDisclaimer().subscribe(() => {\n          this._router.navigateByUrl('sign');\n        });\n        break;\n      case 'remind':\n        this.checkToFinishStep();\n        break;\n      default:\n    }\n  }\n  refreshListRegular() {\n    this.getListRegularChangeItem().subscribe();\n  }\n  refreshListHouseReview() {\n    this.getHouseReview().subscribe();\n  }\n  changeStep(event) {\n    if (event == 1 && !this.agreeToGoNextStep4) {\n      this.visible = true;\n      this.textData = CONTENT_OPTIONS['confirmChangeStep'];\n      return;\n    }\n    this.step = event;\n    this.currentTypeUI = 0;\n  }\n  checkToFinishStep() {\n    forkJoin([this._regularChangeService.apiRegularChangeItemCheckRegularChangePost$Json({}), this.houseService.apiHouseCheckIsReviewPost$Json({})]).pipe(tap(([regularChange, isReview]) => {\n      if (regularChange.StatusCode == 0 && isReview.StatusCode == 0) {\n        if (regularChange.Entries && isReview.Entries) {\n          this._router.navigateByUrl(\"reserve\");\n        }\n      } else {\n        regularChange.Message !== \"\" ? this._toastService.showErrorMSG(regularChange.Message) : \"\";\n        isReview.Message !== \"\" ? this._toastService.showErrorMSG(isReview.Message) : \"\";\n      }\n    })).subscribe();\n  }\n  agreeDisclaimer() {\n    LoadingService.loading(true);\n    return this.houseService.apiHouseAgreeDisclaimerPost$Json({}).pipe(finalize(() => LoadingService.loading(false)));\n  }\n  saveMileStone(step) {\n    this.houseService.apiHouseSaveMilestonePost$Json({\n      body: step ?? this.step\n    }).subscribe(() => {\n      LocalStorageService.AddLocalStorage(STORAGE_KEY.NUMBER_STEP, this.step);\n    });\n  }\n  updateProgress(progress) {\n    LoadingService.loading(true);\n    this.houseService.apiHouseUpdateHouseProgressPost$Json({\n      body: progress\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0 && res.Message == \"Success\") {\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.CURRENT_PROGRESS, progress);\n      }\n    })).subscribe();\n  }\n  static #_ = this.ɵfac = function ChoiceComponent_Factory(t) {\n    return new (t || ChoiceComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HouseService), i0.ɵɵdirectiveInject(i3.ToastMessage), i0.ɵɵdirectiveInject(i2.RegularChangeItemService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChoiceComponent,\n    selectors: [[\"app-choice\"]],\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([ToastMessage, MessageService]), i0.ɵɵStandaloneFeature],\n    decls: 16,\n    vars: 8,\n    consts: [[1, \"wrapper\"], [1, \"content\"], [1, \"flex\", \"justify-center\"], [1, \"page-title\"], [1, \"step-header\"], [3, \"currentStepChange\", \"changeStep\", \"currentStep\", \"listSteps\"], [1, \"choice\"], [3, \"visibleChange\", \"actionButtonLeft\", \"actionButtonRight\", \"textData\", \"visible\"], [\"pRipple\", \"\", \"position\", \"top-right\"], [3, \"agreeToGoNextStep4Change\", \"nextEvent\", \"agreeToGoNextStep4\"], [3, \"nextEvent\", \"listHouseReviewChange\", \"refreshHouseReview\", \"listHouseReview\"], [3, \"nextEvent\", \"listRegularChangeItemChange\", \"currentTypeUIChange\", \"refreshListRegular\", \"isDisabledOptionStep2\", \"listRegularChangeItem\", \"currentTypeUI\"], [3, \"nextEvent\", \"goBackEvent\", \"selectedData\"], [3, \"nextEvent\", \"listHouseRequirement\"]],\n    template: function ChoiceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵtext(4, \"Step2\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"div\", 4)(6, \"app-header-steppers\", 5);\n        i0.ɵɵtwoWayListener(\"currentStepChange\", function ChoiceComponent_Template_app_header_steppers_currentStepChange_6_listener($event) {\n          i0.ɵɵtwoWayBindingSet(ctx.listSteps[ctx.step], $event) || (ctx.listSteps[ctx.step] = $event);\n          return $event;\n        });\n        i0.ɵɵlistener(\"changeStep\", function ChoiceComponent_Template_app_header_steppers_changeStep_6_listener($event) {\n          return ctx.changeStep($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 2)(8, \"div\", 6);\n        i0.ɵɵtemplate(9, ChoiceComponent_Case_9_Template, 1, 1)(10, ChoiceComponent_Case_10_Template, 1, 1)(11, ChoiceComponent_Case_11_Template, 1, 3)(12, ChoiceComponent_Case_12_Template, 1, 1)(13, ChoiceComponent_Case_13_Template, 1, 1);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(14, \"app-dialog-popup\", 7);\n        i0.ɵɵtwoWayListener(\"visibleChange\", function ChoiceComponent_Template_app_dialog_popup_visibleChange_14_listener($event) {\n          i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n          return $event;\n        });\n        i0.ɵɵlistener(\"actionButtonLeft\", function ChoiceComponent_Template_app_dialog_popup_actionButtonLeft_14_listener() {\n          return ctx.actionButtonLeft();\n        })(\"actionButtonRight\", function ChoiceComponent_Template_app_dialog_popup_actionButtonRight_14_listener() {\n          return ctx.actionButtonRight();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(15, \"p-toast\", 8);\n      }\n      if (rf & 2) {\n        let tmp_2_0;\n        i0.ɵɵadvance(6);\n        i0.ɵɵtwoWayProperty(\"currentStep\", ctx.listSteps[ctx.step]);\n        i0.ɵɵproperty(\"listSteps\", ctx.listSteps);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(9, (tmp_2_0 = ctx.step) === 0 ? 9 : tmp_2_0 === 1 ? 10 : tmp_2_0 === 2 ? 11 : tmp_2_0 === 3 ? 12 : tmp_2_0 === 4 ? 13 : -1);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"textData\", ctx.textData);\n        i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n        i0.ɵɵadvance();\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(7, _c0));\n      }\n    },\n    dependencies: [CommonModule, ToastModule, i4.Toast, ReactiveFormsModule, FormsModule, RouterModule, CheckboxModule, DropdownModule, RadioButtonModule, HeaderSteppersComponent, StepIiChoiceComponent, StepIiiChoiceComponent, StepIvChoiceComponent, StepVChoiceComponent, StepViChoiceComponent, DialogPopupComponent],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}html[_ngcontent-%COMP%]{line-height:initial;-webkit-text-size-adjust:100%;width:100%;height:100%}body[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:#23181599;font-family:Noto Sans TC!important;box-sizing:border-box;width:100%;height:auto;overflow:auto;overscroll-behavior:none;letter-spacing:.32px}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{-webkit-text-size-adjust:none;text-size-adjust:none}main[_ngcontent-%COMP%]{display:block}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:400}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible;margin:10px 0;border:none;border-top:1px solid #000}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1rem}a[_ngcontent-%COMP%]{background-color:transparent;text-decoration:none;-webkit-tap-highlight-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:500}strong[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:600}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}p[_ngcontent-%COMP%]{font-size:1rem}img[_ngcontent-%COMP%]{border-style:none;max-width:100%}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:Noto Sans TC,\\\\5fae\\\\8edf\\\\6b63\\\\9ed1\\\\9ad4,Arial,sans-serif;font-size:1rem;line-height:1.15;margin:0;color:#2f2f2f;-webkit-tap-highlight-color:transparent;letter-spacing:2px}select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]{padding:0;background-color:transparent;border:none;cursor:pointer}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}div[role=tab][_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]{border-spacing:0px}ol[_ngcontent-%COMP%], ul[_ngcontent-%COMP%]{padding-left:0;margin-top:0}.form-check-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:none}.container[_ngcontent-%COMP%]{width:100%}@media (min-width: 640px){.container[_ngcontent-%COMP%]{max-width:640px}}@media (min-width: 768px){.container[_ngcontent-%COMP%]{max-width:768px}}@media (min-width: 1024px){.container[_ngcontent-%COMP%]{max-width:1024px}}@media (min-width: 1280px){.container[_ngcontent-%COMP%]{max-width:1280px}}@media (min-width: 1536px){.container[_ngcontent-%COMP%]{max-width:1536px}}.visible[_ngcontent-%COMP%]{visibility:visible}.collapse[_ngcontent-%COMP%]{visibility:collapse}.static[_ngcontent-%COMP%]{position:static}.absolute[_ngcontent-%COMP%]{position:absolute}.relative[_ngcontent-%COMP%]{position:relative}.inset-y-0[_ngcontent-%COMP%]{top:0;bottom:0}.bottom-0[_ngcontent-%COMP%]{bottom:0}.left-0[_ngcontent-%COMP%]{left:0}.left-1\\\\/2[_ngcontent-%COMP%]{left:50%}.left-3[_ngcontent-%COMP%]{left:.75rem}.right-0[_ngcontent-%COMP%]{right:0}.top-1[_ngcontent-%COMP%]{top:.25rem}.top-1\\\\/2[_ngcontent-%COMP%]{top:50%}.top-3[_ngcontent-%COMP%]{top:.75rem}.m-2[_ngcontent-%COMP%]{margin:.5rem}.m-auto[_ngcontent-%COMP%]{margin:auto}.mx-3[_ngcontent-%COMP%]{margin-left:.75rem;margin-right:.75rem}.mx-4[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.mx-5[_ngcontent-%COMP%]{margin-left:1.25rem;margin-right:1.25rem}.mx-\\\\__ph-0__[_ngcontent-%COMP%]{margin-left:20%;margin-right:20%}.my-12[_ngcontent-%COMP%]{margin-top:3rem;margin-bottom:3rem}.my-2[_ngcontent-%COMP%]{margin-top:.5rem;margin-bottom:.5rem}.my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.my-6[_ngcontent-%COMP%]{margin-top:1.5rem;margin-bottom:1.5rem}.\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:.75rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ml-1[_ngcontent-%COMP%]{margin-left:.25rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.ml-4[_ngcontent-%COMP%]{margin-left:1rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-4[_ngcontent-%COMP%]{margin-right:1rem}.mr-5[_ngcontent-%COMP%]{margin-right:1.25rem}.mr-8[_ngcontent-%COMP%]{margin-right:2rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.mt-14[_ngcontent-%COMP%]{margin-top:3.5rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:.75rem}.mt-4[_ngcontent-%COMP%]{margin-top:1rem}.mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.mt-6[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-7[_ngcontent-%COMP%]{margin-top:1.75rem}.mt-8[_ngcontent-%COMP%]{margin-top:2rem}.mt-\\\\__ph-0__[_ngcontent-%COMP%]{margin-top:16px}.block[_ngcontent-%COMP%]{display:block}.flex[_ngcontent-%COMP%]{display:flex}.table[_ngcontent-%COMP%]{display:table}.table-row[_ngcontent-%COMP%]{display:table-row}.grid[_ngcontent-%COMP%]{display:grid}.\\\\!h-\\\\__ph-0__[_ngcontent-%COMP%]{height:107px!important}.\\\\!h-full[_ngcontent-%COMP%]{height:100%!important}.h-28[_ngcontent-%COMP%]{height:7rem}.h-32[_ngcontent-%COMP%]{height:8rem}.h-6[_ngcontent-%COMP%]{height:1.5rem}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:100px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:145px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:280px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:31px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:40vh}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:47px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:480px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:4px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:50px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:55px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:665px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:80px}.h-auto[_ngcontent-%COMP%]{height:auto}.h-fit[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content}.h-full[_ngcontent-%COMP%]{height:100%}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:200px}.\\\\!min-h-\\\\__ph-0__[_ngcontent-%COMP%]{min-height:60vh!important}.\\\\!w-40[_ngcontent-%COMP%]{width:10rem!important}.\\\\!w-48[_ngcontent-%COMP%]{width:12rem!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:107px!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:120px!important}.\\\\!w-full[_ngcontent-%COMP%]{width:100%!important}.w-1\\\\/3[_ngcontent-%COMP%]{width:33.333333%}.w-2\\\\/3[_ngcontent-%COMP%]{width:66.666667%}.w-28[_ngcontent-%COMP%]{width:7rem}.w-6[_ngcontent-%COMP%]{width:1.5rem}.w-7\\\\/12[_ngcontent-%COMP%]{width:58.333333%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:100%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:150px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:160px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:309px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:30rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:400px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:45%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:485px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:50%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:550px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:660px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:760px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:88px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:968px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:97%}.w-full[_ngcontent-%COMP%]{width:100%}.\\\\!max-w-full[_ngcontent-%COMP%]{max-width:100%!important}.max-w-4xl[_ngcontent-%COMP%]{max-width:56rem}.max-w-\\\\__ph-0__[_ngcontent-%COMP%]{max-width:1216px}.flex-shrink[_ngcontent-%COMP%]{flex-shrink:1}.grow[_ngcontent-%COMP%]{flex-grow:1}.border-collapse[_ngcontent-%COMP%]{border-collapse:collapse}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.resize-none[_ngcontent-%COMP%]{resize:none}.grid-cols-10[_ngcontent-%COMP%]{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}.grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}.flex-row[_ngcontent-%COMP%]{flex-direction:row}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.items-start[_ngcontent-%COMP%]{align-items:flex-start}.items-center[_ngcontent-%COMP%]{align-items:center}.justify-start[_ngcontent-%COMP%]{justify-content:flex-start}.justify-end[_ngcontent-%COMP%]{justify-content:flex-end}.justify-center[_ngcontent-%COMP%]{justify-content:center}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.gap-x-1[_ngcontent-%COMP%]{column-gap:.25rem}.self-center[_ngcontent-%COMP%]{align-self:center}.overflow-hidden[_ngcontent-%COMP%]{overflow:hidden}.overflow-y-scroll[_ngcontent-%COMP%]{overflow-y:scroll}.rounded[_ngcontent-%COMP%]{border-radius:.25rem}.rounded-3xl[_ngcontent-%COMP%]{border-radius:1.5rem}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.rounded-lg[_ngcontent-%COMP%]{border-radius:.5rem}.rounded-md[_ngcontent-%COMP%]{border-radius:.375rem}.border[_ngcontent-%COMP%]{border-width:1px}.border-2[_ngcontent-%COMP%]{border-width:2px}.border-solid[_ngcontent-%COMP%]{border-style:solid}.border-blue-400[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(96 165 250 / var(--tw-border-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 143 199 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(184 166 118 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea80}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea99}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(217 217 217 / var(--tw-bg-opacity))}.bg-gray-500[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(107 114 128 / var(--tw-bg-opacity))}.bg-white[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.bg-opacity-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: .04}.object-cover[_ngcontent-%COMP%]{object-fit:cover}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-4[_ngcontent-%COMP%]{padding:1rem}.p-\\\\__ph-0__[_ngcontent-%COMP%]{padding:16px}.px-3[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-6[_ngcontent-%COMP%]{padding-left:1.5rem;padding-right:1.5rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.py-2[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.pb-4[_ngcontent-%COMP%]{padding-bottom:1rem}.pb-5[_ngcontent-%COMP%]{padding-bottom:1.25rem}.pb-8[_ngcontent-%COMP%]{padding-bottom:2rem}.pl-4[_ngcontent-%COMP%]{padding-left:1rem}.pl-6[_ngcontent-%COMP%]{padding-left:1.5rem}.pr-1[_ngcontent-%COMP%]{padding-right:.25rem}.pr-3[_ngcontent-%COMP%]{padding-right:.75rem}.pr-4[_ngcontent-%COMP%]{padding-right:1rem}.text-left[_ngcontent-%COMP%]{text-align:left}.\\\\!text-center[_ngcontent-%COMP%]{text-align:center!important}.text-center[_ngcontent-%COMP%]{text-align:center}.text-right[_ngcontent-%COMP%]{text-align:right}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-3xl[_ngcontent-%COMP%]{font-size:1.875rem;line-height:2.25rem}.text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xl[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-light[_ngcontent-%COMP%]{font-weight:300}.font-medium[_ngcontent-%COMP%]{font-weight:500}.font-normal[_ngcontent-%COMP%]{font-weight:400}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.tracking-wider[_ngcontent-%COMP%]{letter-spacing:.05em}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 143 199 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(35 24 21 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(182 152 103 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(184 166 118 / var(--tw-text-opacity))}.text-black[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-blue-400[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.text-gray-500[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-stone-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(87 83 78 / var(--tw-text-opacity))}.text-stone-900[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(28 25 23 / var(--tw-text-opacity))}.text-white[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.shadow[_ngcontent-%COMP%]{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.grayscale[_ngcontent-%COMP%]{--tw-grayscale: grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter[_ngcontent-%COMP%]{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.content[_ngcontent-%COMP%]{width:100%}@media screen and (max-width: 1024px){.content[_ngcontent-%COMP%]{width:100%;padding:0 16px 50px}}.pc[_ngcontent-%COMP%], .inpc[_ngcontent-%COMP%]{display:initial}@media screen and (max-width: 1024px){.inpc[_ngcontent-%COMP%]{display:none}}.flat[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 1024px){.flat[_ngcontent-%COMP%]{display:initial}}.mb[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.mb[_ngcontent-%COMP%]{display:initial}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{position:relative}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background-image:url(/assets/background/background2.png);width:180px;height:150px;background-size:unset;background-position:left;z-index:-1;right:0;bottom:20px}.button1[_ngcontent-%COMP%]{width:200px;height:47px;border:1px solid #CDCDCD;color:#3a4246;border-radius:28px;background-origin:border-box;background-clip:content-box,border-box;transition:all .3s ease}.button1[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#b8a676;transition:color .3s ease}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#ae9b66}@media screen and (max-width: 912px){.button1[_ngcontent-%COMP%]{width:100%}}.button2[_ngcontent-%COMP%]{width:200px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button2[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button2[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button2[_ngcontent-%COMP%]{width:100%}}.button3[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button3[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button3[_ngcontent-%COMP%]{width:100%}}.button4[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:#979797;border-radius:24px;transition:all .3s ease}.button4[_ngcontent-%COMP%]:hover{background:#7e7e7e}.button4[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button4[_ngcontent-%COMP%]{width:100%}}.checkbox-zone[_ngcontent-%COMP%]{padding:2px;border-radius:2px;position:relative;background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 1px 4px #ae9b664d}.avoid-resize-img[_ngcontent-%COMP%]{max-width:none}.fit-size[_ngcontent-%COMP%]{height:inherit;width:inherit;object-fit:cover;object-position:center}.p-calendar[_ngcontent-%COMP%]{width:100%}.p-calendar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#23181599}@media screen and (max-width: 912px){.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]{border:none;width:100%}.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#231815}@media screen and (max-width: 912px){.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]   .p-dropdown-trigger[_ngcontent-%COMP%]{border-radius:0;border-bottom:1px solid rgba(144,150,157,.4)}.p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{box-shadow:none}.p-radiobutton[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]{width:22px;height:22px;border:2px solid #CDCDCD;background-color:#fff;border-radius:50%;transition:all .3s cubic-bezier(.25,.46,.45,.94);position:relative;box-shadow:0 1px 3px #ae9b661a;cursor:pointer}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:hover{border-color:#b8a676;background:radial-gradient(circle at center,#b8a67614,#b8a67605);transform:scale(1.05);box-shadow:0 2px 8px #ae9b6626}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #b8a67640,0 2px 8px #ae9b6633;border-color:#a39460}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:active{transform:scale(.98)}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-color:#ae9b66;box-shadow:0 2px 12px #ae9b6640,inset 0 1px 2px #fff3}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:8px;height:8px;background:radial-gradient(circle,#fff,#ffffffe6);border-radius:50%;transform:translate(-50%,-50%) scale(0);box-shadow:0 1px 2px #0003;animation:_ngcontent-%COMP%_radioAppear .2s ease forwards}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);border-color:#9b8a5a;transform:scale(1.05);box-shadow:0 3px 15px #ae9b6659,inset 0 1px 2px #ffffff40}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #b8a6764d,0 3px 15px #ae9b6659}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-icon[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.2);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{transform:translate(-50%,-50%) scale(0);opacity:.6}to{transform:translate(-50%,-50%) scale(4);opacity:0}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background-color:#fff;color:#846a52;font-size:16px;border:1px solid #E6F0F3;border-radius:6px;padding:16px 32px}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 32px 8px;border-bottom:none!important;border-bottom-right-radius:0;border-bottom-left-radius:0}@media screen and (max-width: 912px){.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(-180deg)!important;transition:transform .3s linear}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(0)!important;transition:transform .3s linear}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]:focus{box-shadow:none}.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 32px 16px}@media screen and (max-width: 912px){.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 16px 16px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{width:24px;height:24px}.p-dialog-mask[_ngcontent-%COMP%]{width:100%;height:100%;background-color:#0006;top:0;z-index:20;pointer-events:all!important}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]{padding:32px 24px 16px;color:#231815;font-size:24px;text-align:center;display:flex;justify-content:center}.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]{padding:32px 24px 0;background:linear-gradient(180deg,#f3f1ea,#fff)}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]{display:none}.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]{padding:24px 24px 40px;display:flex;justify-content:center}.p-checkbox[_ngcontent-%COMP%]{width:18px;height:18px;display:inline-flex;align-items:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]{width:18px;height:18px;border:2px solid #CDCDCD;border-radius:3px;transition:.3s ease;background:#fff;position:relative;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:hover{border-color:#b8a676}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:focus{outline:none;border-color:#b8a676;box-shadow:0 0 0 2px #b8a67633}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]{background:#b8a676!important;border-color:#b8a676!important}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{color:#fff!important;font-size:12px;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:not(.p-highlight)   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(0);opacity:0}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]:before{content:\\\"\\\\e915\\\";font-family:primeicons;font-weight:400;font-style:normal;font-size:12px;display:inline-block}.p-checkbox-label[_ngcontent-%COMP%]{margin-left:8px;cursor:pointer;-webkit-user-select:none;user-select:none}.input[_ngcontent-%COMP%]{width:100%;border:none;padding:12.5px 0;border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px;height:4px}[_ngcontent-%COMP%]::-webkit-scrollbar-button{background:transparent;border-radius:2px}[_ngcontent-%COMP%]::-webkit-scrollbar-track-piece{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:4px;background-color:#90969d33}[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:transparent}.p-radiobutton-label[_ngcontent-%COMP%]{color:#231815!important;font-weight:400;cursor:pointer;transition:all .3s ease}.p-radiobutton-label[_ngcontent-%COMP%]:hover{color:#ae9b66!important}label[for*=requirement_][_ngcontent-%COMP%]{color:#231815!important;font-weight:400}label[for*=requirement_][_ngcontent-%COMP%]:hover{color:#ae9b66!important}input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%], .p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%] + label[_ngcontent-%COMP%]{color:#ae9b66!important;font-weight:600}  .choice{margin-top:24px;width:100%}@media screen and (max-width: 912px){  .choice{text-align:center;width:100%;margin-top:24px}}  .choice .title{font-size:20px;font-weight:500;color:#231815;margin-bottom:16px}  .choice .box{position:relative;height:400px}@media screen and (max-width: 1024px){  .choice .box{height:auto}}@media screen and (max-width: 912px){  .choice .box{margin-right:20px}}@media screen and (max-width: 640px){  .choice .box{height:auto;margin-right:0;margin-bottom:20px}  .choice .box:last-child{margin-bottom:0}  .choice .box img{width:100%}}  .choice .box:last-child{margin-right:0}  .choice .plan{width:100%;max-width:760px;margin-right:68px;height:512px;max-height:512px;overflow-y:auto;padding-right:24px}@media screen and (max-width: 912px){  .choice .plan{width:100%;order:2;margin-top:50px;text-align:left;max-width:100%}}  .choice .review .text{color:#231815cc}  .choice .review .img{width:360px;height:400px;border-radius:4px}@media screen and (max-width: 912px){  .choice .review{width:100%;order:1}}  .choice .button3{display:flex;justify-content:center;align-items:center;width:204px;position:relative}@media screen and (max-width: 912px){  .choice .button3{width:100%}}  .choice .button3 img{position:absolute;right:25px}  .choice .p-radiobutton{width:20px;height:20px}  .choice .p-radiobutton .p-radiobutton-box{width:20px;height:20px}  .choice .drop50{width:322px}  .choice .drop100{width:660px}  .choice .droptext{color:#231815cc}@media screen and (max-width: 912px){  .choicestep2{text-align:left}}  .choicedialog .p-dialog .p-dialog-header{padding-bottom:0}  .choicedialog .p-dialog .p-dialog-content{padding-top:16px;padding-bottom:24px}  .choicedialog .p-dialog .p-dialog-footer{padding-top:16px;background:#fff}  .choicedialog .button2{padding:10px 16px}  .bigimgdialog .p-dialog .p-dialog-header{padding-bottom:0}  .bigimgdialog .p-dialog .p-dialog-content{padding-top:16px;padding-bottom:0}  .bigimgdialog .p-dialog .p-dialog-footer{padding-top:24px;background:#fff}  .bigimgdialog .button2{padding:10px 16px}.marerialflex[_ngcontent-%COMP%]{flex-shrink:0}.marerialbox[_ngcontent-%COMP%]{width:90px;border-radius:4px;margin-bottom:8px}.marerialrightbox[_ngcontent-%COMP%]{width:360px;height:400px;border-radius:4px}.planreview-box[_ngcontent-%COMP%]{display:flex;flex-direction:row}@media screen and (max-width: 912px){.planreview-box[_ngcontent-%COMP%]{flex-direction:column}}.buttonbox[_ngcontent-%COMP%]{display:flex}@media screen and (max-width: 640px){.buttonbox[_ngcontent-%COMP%]{display:block;margin:48px 0}}.button1[_ngcontent-%COMP%], .button2[_ngcontent-%COMP%]{width:180px;margin-top:48px;margin-bottom:48px}@media screen and (max-width: 640px){.button1[_ngcontent-%COMP%], .button2[_ngcontent-%COMP%]{width:100%;margin:0}}.buttonstep4[_ngcontent-%COMP%]{margin:16px 0}.dialog-content[_ngcontent-%COMP%]{display:flex;justify-content:center}.checkbox[_ngcontent-%COMP%]{border:1px solid #008FC7;padding:12px 16px;border-radius:4px;color:#231815cc;background-color:#fff}.giveup[_ngcontent-%COMP%]{overflow-y:auto;height:250px;max-height:250px;padding-right:10px;margin-bottom:16px}.sign[_ngcontent-%COMP%]{background-color:#e6f0f3;border-radius:4px;height:100px}@media screen and (max-width: 912px){.sign[_ngcontent-%COMP%]{height:200px}}.choicebutn[_ngcontent-%COMP%]{padding:4px 16px;width:81px;height:auto;cursor:pointer;margin:auto}.m-0[_ngcontent-%COMP%]{margin:0}.mr-4[_ngcontent-%COMP%]{margin-right:16px}@media screen and (max-width: 640px){.butn1[_ngcontent-%COMP%]{margin-top:16px}}.page-title[_ngcontent-%COMP%]{font-size:32px;color:#000;font-weight:700;width:100%;padding-top:20px;display:block}.step-header[_ngcontent-%COMP%]{padding-top:24px}.content[_ngcontent-%COMP%]{padding:0 120px}@media screen and (max-width: 912px){.page-title[_ngcontent-%COMP%]{display:none}.content[_ngcontent-%COMP%]{padding:0!important}.step-header[_ngcontent-%COMP%]{padding:0 30px}}@media not all and (min-width: 1280px){.max-xl\\\\:grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}}@media not all and (min-width: 1024px){.max-lg\\\\:bottom-12[_ngcontent-%COMP%]{bottom:3rem}.max-lg\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-lg\\\\:grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}}@media not all and (min-width: 768px){.max-md\\\\:bottom-14[_ngcontent-%COMP%]{bottom:3.5rem}.max-md\\\\:my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.max-md\\\\:ml-0[_ngcontent-%COMP%]{margin-left:0}.max-md\\\\:mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.max-md\\\\:mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.max-md\\\\:hidden[_ngcontent-%COMP%]{display:none}.max-md\\\\:h-\\\\__ph-0__[_ngcontent-%COMP%]{height:65%}.max-md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:450px!important}.max-md\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:500px}.max-md\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-md\\\\:max-w-full[_ngcontent-%COMP%]{max-width:100%}.max-md\\\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.max-md\\\\:flex-col[_ngcontent-%COMP%]{flex-direction:column}.max-md\\\\:flex-col-reverse[_ngcontent-%COMP%]{flex-direction:column-reverse}.max-md\\\\:flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.max-md\\\\:items-start[_ngcontent-%COMP%]{align-items:flex-start}}@media not all and (min-width: 640px){.max-sm\\\\:bottom-10[_ngcontent-%COMP%]{bottom:2.5rem}.max-sm\\\\:ml-8[_ngcontent-%COMP%]{margin-left:2rem}.max-sm\\\\:block[_ngcontent-%COMP%]{display:block}.max-sm\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:340px!important}.max-sm\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:380px}.max-sm\\\\:\\\\!grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))!important}.max-sm\\\\:grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.max-sm\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.max-sm\\\\:gap-0[_ngcontent-%COMP%]{gap:0px}.max-sm\\\\:px-0[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.max-sm\\\\:pl-0[_ngcontent-%COMP%]{padding-left:0}.max-sm\\\\:text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}}@media (min-width: 640px){.sm\\\\:mx-0[_ngcontent-%COMP%]{margin-left:0;margin-right:0}.sm\\\\:mb-0[_ngcontent-%COMP%]{margin-bottom:0}.sm\\\\:mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.sm\\\\:flex[_ngcontent-%COMP%]{display:flex}.sm\\\\:items-center[_ngcontent-%COMP%]{align-items:center}.sm\\\\:pl-3[_ngcontent-%COMP%]{padding-left:.75rem}}@media (min-width: 768px){.md\\\\:flex[_ngcontent-%COMP%]{display:flex}.md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:634px!important}.md\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.md\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.md\\\\:pb-20[_ngcontent-%COMP%]{padding-bottom:5rem}}@media (min-width: 1024px){.lg\\\\:\\\\!mx-0[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.lg\\\\:\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.lg\\\\:flex[_ngcontent-%COMP%]{display:flex}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:800px!important}.lg\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.lg\\\\:\\\\!px-4[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.lg\\\\:\\\\!px-\\\\__ph-0__[_ngcontent-%COMP%]{padding-left:22px!important;padding-right:22px!important}.lg\\\\:\\\\!py-\\\\__ph-0__[_ngcontent-%COMP%]{padding-top:18px!important;padding-bottom:18px!important}.lg\\\\:text-center[_ngcontent-%COMP%]{text-align:center}}\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "RouterModule", "CheckboxModule", "DropdownModule", "RadioButtonModule", "finalize", "fork<PERSON><PERSON>n", "tap", "LoadingService", "STORAGE_KEY", "LocalStorageService", "StepIiChoiceComponent", "StepIiiChoiceComponent", "StepIvChoiceComponent", "DialogPopupComponent", "ToastMessage", "ToastModule", "MessageService", "CONTENT_OPTIONS", "HeaderSteppersComponent", "StepVChoiceComponent", "StepViChoiceComponent", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "ChoiceComponent_Case_9_Template_app_step_iv_choice_agreeToGoNextStep4Change_0_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "agreeToGoNextStep4", "ɵɵresetView", "ɵɵlistener", "ChoiceComponent_Case_9_Template_app_step_iv_choice_nextEvent_0_listener", "next", "ɵɵelementEnd", "ɵɵtwoWayProperty", "ChoiceComponent_Case_10_Template_app_step_v_choice_nextEvent_0_listener", "_r3", "ChoiceComponent_Case_10_Template_app_step_v_choice_listHouseReviewChange_0_listener", "listHouseReview", "ChoiceComponent_Case_10_Template_app_step_v_choice_refreshHouseReview_0_listener", "refreshListHouseReview", "ChoiceComponent_Case_11_Template_app_step_ii_choice_nextEvent_0_listener", "_r4", "ChoiceComponent_Case_11_Template_app_step_ii_choice_listRegularChangeItemChange_0_listener", "listRegularChangeItem", "ChoiceComponent_Case_11_Template_app_step_ii_choice_currentTypeUIChange_0_listener", "currentTypeUI", "ChoiceComponent_Case_11_Template_app_step_ii_choice_refreshListRegular_0_listener", "refreshListRegular", "ɵɵproperty", "isDisabledOptionStep2", "ChoiceComponent_Case_12_Template_app_step_iii_choice_nextEvent_0_listener", "_r5", "ChoiceComponent_Case_12_Template_app_step_iii_choice_goBackEvent_0_listener", "goBackEvent", "selectedData", "ChoiceComponent_Case_13_Template_app_step_vi_choice_nextEvent_0_listener", "_r6", "listHouseRequirement", "ChoiceComponent", "constructor", "_router", "houseService", "_toastService", "_regularChangeService", "dataUser", "GetLocalStorage", "USER", "step", "visible", "disclaimer", "textData", "listSteps", "id", "name", "isFinished", "sign_info", "SIGN_INFO", "NUMBER_STEP", "IS_LOCK", "ngOnInit", "index", "element", "getListRegularChangeItem", "subscribe", "getDataStep3", "getHouseReview", "getHouseRequirement", "loading", "apiRegularChangeItemGetListRegularChangeItemPost$Json", "pipe", "res", "Entries", "apiRegularChangeItemGetSumaryRegularChangeItemPost$Json", "apiHouseGetHouseReviewPost$Json", "apiHouseGetHouseRequirementPost$Json", "StatusCode", "event", "updateProgress", "saveMileStone", "back", "handleEventBack", "isAvailable", "isFinishBuilding", "navigateByUrl", "payload", "acceptChangeTime", "AddLocalStorage", "handleNavigate", "title", "actionButtonLeft", "actionButtonRight", "agreeDisclaimer", "checkToFinishStep", "changeStep", "apiRegularChangeItemCheckRegularChangePost$Json", "apiHouseCheckIsReviewPost$Json", "regularChange", "isReview", "Message", "showErrorMSG", "apiHouseAgreeDisclaimerPost$Json", "apiHouseSaveMilestonePost$Json", "body", "progress", "apiHouseUpdateHouseProgressPost$Json", "CURRENT_PROGRESS", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "HouseService", "i3", "RegularChangeItemService", "_2", "selectors", "standalone", "features", "ɵɵProvidersFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChoiceComponent_Template", "rf", "ctx", "ɵɵtext", "ChoiceComponent_Template_app_header_steppers_currentStepChange_6_listener", "ChoiceComponent_Template_app_header_steppers_changeStep_6_listener", "ɵɵtemplate", "ChoiceComponent_Case_9_Template", "ChoiceComponent_Case_10_Template", "ChoiceComponent_Case_11_Template", "ChoiceComponent_Case_12_Template", "ChoiceComponent_Case_13_Template", "ChoiceComponent_Template_app_dialog_popup_visibleChange_14_listener", "ChoiceComponent_Template_app_dialog_popup_actionButtonLeft_14_listener", "ChoiceComponent_Template_app_dialog_popup_actionButtonRight_14_listener", "ɵɵelement", "ɵɵadvance", "ɵɵconditional", "tmp_2_0", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "i4", "Toast", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\choice.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\choice.component.html"], "sourcesContent": ["import { CommonModule, NgIf } from '@angular/common';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { finalize, forkJoin, tap } from 'rxjs';\r\nimport {\r\n  GetFinalDocRes,\r\n  GetHouseReview,\r\n  GetSumaryRegularChangeItemRes,\r\n  HouseRequirementRes,\r\n  GetListRegularChangeItemRes,\r\n} from '../../../services/api/models';\r\nimport { LoadingService } from '../../shared/services/loading.service';\r\nimport { STORAGE_KEY, UserInfo } from '../../shared/constant/constant';\r\nimport { LocalStorageService } from '../../shared/services/local-storage.service';\r\nimport {\r\n  StepIChoiceComponent\r\n} from './components/step-i-choice/step-i-choice.component';\r\nimport { StepIiChoiceComponent } from './components/step-ii-choice/step-ii-choice.component';\r\nimport { StepIiiChoiceComponent } from './components/step-iii-choice/step-iii-choice.component';\r\nimport { StepIvChoiceComponent } from './components/step-iv-choice/step-iv-choice.component';\r\nimport { DialogPopupComponent } from '../../components/dialog-popup/dialog-popup.component';\r\nimport { SignaturePadComponent } from '../../components/signature-pad/signature-pad.component';\r\nimport { BaseFilePipe } from '../../shared/pipes/base-file.pipe';\r\nimport { ContentDialog } from '../../../model/choice.model';\r\nimport { ToastMessage } from '../../shared/services/message.service';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService } from 'primeng/api';\r\nimport { CONTENT_OPTIONS } from '../../shared/constant/constant';\r\nimport { HeaderSteppersComponent } from './components/header-steppers/header-steppers.component';\r\nimport { StepVChoiceComponent } from './components/step-v-choice/step-v-choice.component';\r\nimport { StepViChoiceComponent } from './components/step-vi-choice/step-vi-choice.component';\r\nimport { HouseService, RegularChangeItemService } from '../../../services/api/services';\r\n\r\n\r\n@Component({\r\n  selector: 'app-choice',\r\n  standalone: true,\r\n  imports: [\r\n    NgIf,\r\n    BaseFilePipe,\r\n    CommonModule,\r\n    ToastModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    RouterModule,\r\n    CheckboxModule,\r\n    DropdownModule,\r\n    RadioButtonModule,\r\n    HeaderSteppersComponent,\r\n    StepIChoiceComponent,\r\n    StepIiChoiceComponent,\r\n    StepIiiChoiceComponent,\r\n    StepIvChoiceComponent,\r\n    StepVChoiceComponent,\r\n    StepViChoiceComponent,\r\n    DialogPopupComponent,\r\n    SignaturePadComponent,\r\n  ],\r\n  providers: [\r\n    ToastMessage,\r\n    MessageService\r\n  ],\r\n  templateUrl: './choice.component.html',\r\n  styleUrls: ['./choice.component.scss']\r\n})\r\n\r\n\r\nexport class ChoiceComponent implements OnInit {\r\n\r\n  dataUser = LocalStorageService.GetLocalStorage(STORAGE_KEY.USER) as UserInfo;\r\n  step = 0;\r\n  sign_info: any;\r\n\r\n  //step 1: data\r\n  agreeToGoNextStep4: boolean = false;\r\n\r\n  //step 2: data\r\n  listRegularChangeItem: GetListRegularChangeItemRes[] = []\r\n  currentTypeUI: number = 0\r\n\r\n  //step 3: data\r\n  selectedData: GetSumaryRegularChangeItemRes[] = []\r\n\r\n  //step 4: data\r\n  listHouseReview: GetHouseReview[] = []\r\n\r\n  //step 5: data\r\n  listHouseRequirement: HouseRequirementRes[] = []\r\n\r\n  //handle popup\r\n  visible: boolean = false;\r\n  disclaimer: string = \"\"\r\n  textData: ContentDialog = CONTENT_OPTIONS['default']\r\n  isDisabledOptionStep2: boolean = false;\r\n  listSteps = [\r\n    {\r\n      id: 1,\r\n      name: \"客變原則\",\r\n      isFinished: true\r\n    },\r\n    {\r\n      id: 2,\r\n      name: \"標準圖面審閱\",\r\n      isFinished: false\r\n    },\r\n    {\r\n      id: 3,\r\n      name: \"建材選樣選色\",\r\n      isFinished: false\r\n    },\r\n    {\r\n      id: 4,\r\n      name: \"選樣選擇結果確認\",\r\n      isFinished: false\r\n    },\r\n    {\r\n      id: 5,\r\n      name: \"客變需求確認\",\r\n      isFinished: false\r\n    }\r\n  ]\r\n\r\n  constructor(\r\n    private _router: Router,\r\n    private houseService: HouseService,\r\n    private _toastService: ToastMessage,\r\n    private _regularChangeService: RegularChangeItemService\r\n  ) {\r\n    this.sign_info = LocalStorageService.GetLocalStorage(STORAGE_KEY.SIGN_INFO)\r\n    this.step = LocalStorageService.GetLocalStorage(STORAGE_KEY.NUMBER_STEP) || 0\r\n    this.isDisabledOptionStep2 = LocalStorageService.GetLocalStorage(STORAGE_KEY.IS_LOCK)\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    if (this.step >= 0) {\r\n      this.agreeToGoNextStep4 = true;\r\n    }\r\n    for (let index = 1; index <= this.step; index++) {\r\n      const element = this.listSteps[index - 1];\r\n      element.isFinished = true\r\n      if (index == 1) {\r\n        this.getListRegularChangeItem().subscribe();\r\n      }\r\n      if (index == 2) {\r\n        this.getDataStep3().subscribe();\r\n      }\r\n      if (index == 3) {\r\n        this.getHouseReview().subscribe()\r\n      }\r\n      if (index == 4) {\r\n        this.getHouseRequirement()\r\n      }\r\n      if (index == 5) {\r\n        this.step = 4\r\n      }\r\n    }\r\n  }\r\n\r\n  //Step 2\r\n  getListRegularChangeItem() {\r\n    LoadingService.loading(true)\r\n    return this._regularChangeService.apiRegularChangeItemGetListRegularChangeItemPost$Json({}).pipe(\r\n      tap((res) => {\r\n        this.listRegularChangeItem = res.Entries! ?? []\r\n      }),\r\n      finalize(() => LoadingService.loading(false))\r\n    )\r\n  }\r\n\r\n  //Step 3\r\n  getDataStep3() {\r\n    LoadingService.loading(true)\r\n    return this._regularChangeService.apiRegularChangeItemGetSumaryRegularChangeItemPost$Json().pipe(\r\n      tap((res) => {\r\n        this.selectedData = res.Entries! ?? []\r\n      }),\r\n      finalize(() => LoadingService.loading(false))\r\n    )\r\n  }\r\n\r\n  //Step 4\r\n  getHouseReview() {\r\n    LoadingService.loading(true)\r\n    return this.houseService.apiHouseGetHouseReviewPost$Json({}).pipe(\r\n      tap((res) => {\r\n        this.listHouseReview = res.Entries! ?? []\r\n      }),\r\n      finalize(() => LoadingService.loading(false))\r\n    )\r\n  }\r\n\r\n  //step 5\r\n  getHouseRequirement() {\r\n    LoadingService.loading(true);\r\n    this.houseService.apiHouseGetHouseRequirementPost$Json({}).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.listHouseRequirement = res.Entries! ?? []\r\n        }\r\n      }),\r\n      finalize(() => LoadingService.loading(false))\r\n    ).subscribe()\r\n  }\r\n\r\n  goBackEvent(event: any) {\r\n    this.step--\r\n    this.currentTypeUI = event;\r\n  }\r\n\r\n  next(event?: any) {\r\n    if (this.step == 0) {\r\n      this.getListRegularChangeItem().subscribe()\r\n    }\r\n    if (this.step == 1) {\r\n      this.getDataStep3().subscribe()\r\n    }\r\n    if (this.step == 2) {\r\n      this.getHouseReview().subscribe()\r\n    }\r\n    if (this.step == 3) {\r\n      this.getHouseRequirement()\r\n    }\r\n    if (this.step == 4) {\r\n      this.visible = true;\r\n      this.updateProgress(2);\r\n      this.saveMileStone(5);\r\n      LoadingService.loading(false);\r\n      if (event) {\r\n        this.textData = CONTENT_OPTIONS['remind']\r\n      } else {\r\n        this.textData = CONTENT_OPTIONS['confirm']\r\n      }\r\n      return;\r\n    }\r\n    this.step++;\r\n    this.listSteps[this.step - 1].isFinished = true;\r\n    this.saveMileStone();\r\n  }\r\n\r\n  back() { this.step-- }\r\n\r\n  handleEventBack(isAvailable: boolean) {\r\n    if ((!this.dataUser.isFinishBuilding && !isAvailable)) {\r\n      this._router.navigateByUrl('home');\r\n      return;\r\n    }\r\n\r\n    let payload: UserInfo = {\r\n      name: this.dataUser.name,\r\n      isFinishBuilding: isAvailable,\r\n      acceptChangeTime: this.dataUser.acceptChangeTime\r\n    }\r\n    LocalStorageService.AddLocalStorage(STORAGE_KEY.USER, payload)\r\n  }\r\n\r\n  handleNavigate() {\r\n    if (this.textData.title == \"sign\") {\r\n      this._router.navigateByUrl('sign')\r\n    } else {\r\n      this.visible = true;\r\n      this.textData = CONTENT_OPTIONS['disclaimer']\r\n    }\r\n  }\r\n\r\n  actionButtonLeft() {\r\n    if (this.textData.title == 'confirmChangeStep') {\r\n      this.visible = false;\r\n    }\r\n    switch (this.textData.title) {\r\n      case 'remind':\r\n        this.visible = false;\r\n        break;\r\n      default:\r\n    }\r\n  }\r\n\r\n  actionButtonRight() {\r\n    switch (this.textData.title) {\r\n      case 'confirm':\r\n        this.agreeDisclaimer().subscribe(() => {\r\n          this._router.navigateByUrl('sign');\r\n        })\r\n        break;\r\n      case 'remind':\r\n        this.checkToFinishStep()\r\n        break;\r\n      default:\r\n    }\r\n  }\r\n\r\n  refreshListRegular() {\r\n    this.getListRegularChangeItem().subscribe()\r\n  }\r\n\r\n  refreshListHouseReview() {\r\n    this.getHouseReview().subscribe()\r\n  }\r\n\r\n  changeStep(event: any) {\r\n    if (event == 1 && !this.agreeToGoNextStep4) {\r\n      this.visible = true;\r\n      this.textData = CONTENT_OPTIONS['confirmChangeStep']\r\n      return;\r\n    }\r\n    this.step = event\r\n    this.currentTypeUI = 0;\r\n  }\r\n\r\n  checkToFinishStep() {\r\n    forkJoin([\r\n      this._regularChangeService.apiRegularChangeItemCheckRegularChangePost$Json({}),\r\n      this.houseService.apiHouseCheckIsReviewPost$Json({})\r\n    ]).pipe(\r\n      tap(([regularChange, isReview]) => {\r\n        if (regularChange.StatusCode == 0 && isReview.StatusCode == 0) {\r\n          if (regularChange.Entries! && isReview.Entries!) {\r\n            this._router.navigateByUrl(\"reserve\")\r\n          }\r\n        } else {\r\n          regularChange.Message! !== \"\" ? this._toastService.showErrorMSG(regularChange.Message!) : \"\"\r\n          isReview.Message! !== \"\" ? this._toastService.showErrorMSG(isReview.Message!) : \"\"\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  agreeDisclaimer() {\r\n    LoadingService.loading(true);\r\n    return this.houseService.apiHouseAgreeDisclaimerPost$Json({}).pipe(\r\n      finalize(() => LoadingService.loading(false))\r\n    )\r\n  }\r\n\r\n  saveMileStone(step?: number) {\r\n    this.houseService.apiHouseSaveMilestonePost$Json({\r\n      body: step ?? this.step\r\n    }).subscribe(() => {\r\n      LocalStorageService.AddLocalStorage(STORAGE_KEY.NUMBER_STEP, this.step)\r\n    })\r\n  }\r\n\r\n  updateProgress(progress: number) {\r\n    LoadingService.loading(true);\r\n    this.houseService.apiHouseUpdateHouseProgressPost$Json({ body: progress })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0 && res.Message == \"Success\") {\r\n            LocalStorageService.AddLocalStorage(STORAGE_KEY.CURRENT_PROGRESS, progress)\r\n          }\r\n        })\r\n      ).subscribe();\r\n  }\r\n}\r\n\r\n\r\n", "<div class=\"wrapper\">\r\n  <div class=\"content\">\r\n    <div class=\"flex justify-center\">\r\n      <div class=\"page-title\">Step2</div>\r\n    </div>\r\n    <div class=\"step-header\">\r\n      <app-header-steppers [(currentStep)]=\"listSteps[step]\" [listSteps]=\"listSteps\" (changeStep)=\"changeStep($event)\">\r\n      </app-header-steppers>\r\n    </div>\r\n    <div class=\"flex justify-center\">\r\n      <div class=\"choice\">\r\n        @switch (step){\r\n        @case (0) {\r\n        <app-step-iv-choice [(agreeToGoNextStep4)]=\"agreeToGoNextStep4\" (nextEvent)=\"next()\"></app-step-iv-choice>\r\n        }\r\n        @case (1) {\r\n        <app-step-v-choice (nextEvent)=\"next()\" [(listHouseReview)]=\"listHouseReview\"\r\n          (refreshHouseReview)=\"refreshListHouseReview()\"></app-step-v-choice>\r\n        }\r\n        @case (2) {\r\n        <app-step-ii-choice [isDisabledOptionStep2]=\"isDisabledOptionStep2\" (nextEvent)=\"next()\"\r\n          [(listRegularChangeItem)]=\"listRegularChangeItem\" [(currentTypeUI)]=\"currentTypeUI\"\r\n          (refreshListRegular)=\"refreshListRegular()\"></app-step-ii-choice>\r\n        }\r\n        @case (3) {\r\n        <app-step-iii-choice (nextEvent)=\"next()\" (goBackEvent)=\"goBackEvent($event)\"\r\n          [selectedData]=\"selectedData\"></app-step-iii-choice>\r\n        }\r\n\r\n        @case (4) {\r\n        <app-step-vi-choice (nextEvent)=\"next($event)\"\r\n          [listHouseRequirement]=\"listHouseRequirement\"></app-step-vi-choice>\r\n        }\r\n        }\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<app-dialog-popup [textData]=\"textData\" [(visible)]=\"visible\" (actionButtonLeft)=\"actionButtonLeft()\"\r\n  (actionButtonRight)=\"actionButtonRight()\">\r\n</app-dialog-popup>\r\n\r\n<p-toast pRipple position=\"top-right\" [style]=\"{'width': '22rem'}\"></p-toast>"], "mappings": "AAAA,SAASA,YAAY,QAAc,iBAAiB;AAEpD,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAQ9C,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,WAAW,QAAkB,gCAAgC;AACtE,SAASC,mBAAmB,QAAQ,6CAA6C;AAIjF,SAASC,qBAAqB,QAAQ,sDAAsD;AAC5F,SAASC,sBAAsB,QAAQ,wDAAwD;AAC/F,SAASC,qBAAqB,QAAQ,sDAAsD;AAC5F,SAASC,oBAAoB,QAAQ,sDAAsD;AAI3F,SAASC,YAAY,QAAQ,uCAAuC;AACpE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,oBAAoB,QAAQ,oDAAoD;AACzF,SAASC,qBAAqB,QAAQ,sDAAsD;;;;;;;;;;;;ICrBpFC,EAAA,CAAAC,cAAA,4BAAqF;IAAjED,EAAA,CAAAE,gBAAA,sCAAAC,uFAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAG,kBAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,kBAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA2C;IAACJ,EAAA,CAAAY,UAAA,uBAAAC,wEAAA;MAAAb,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAaJ,MAAA,CAAAO,IAAA,EAAM;IAAA,EAAC;IAACd,EAAA,CAAAe,YAAA,EAAqB;;;;IAAtFf,EAAA,CAAAgB,gBAAA,uBAAAT,MAAA,CAAAG,kBAAA,CAA2C;;;;;;IAG/DV,EAAA,CAAAC,cAAA,4BACkD;IAD/BD,EAAA,CAAAY,UAAA,uBAAAK,wEAAA;MAAAjB,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAaJ,MAAA,CAAAO,IAAA,EAAM;IAAA,EAAC;IAACd,EAAA,CAAAE,gBAAA,mCAAAiB,oFAAAf,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAa,eAAA,EAAAhB,MAAA,MAAAG,MAAA,CAAAa,eAAA,GAAAhB,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAqC;IAC3EJ,EAAA,CAAAY,UAAA,gCAAAS,iFAAA;MAAArB,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAsBJ,MAAA,CAAAe,sBAAA,EAAwB;IAAA,EAAC;IAACtB,EAAA,CAAAe,YAAA,EAAoB;;;;IAD9Bf,EAAA,CAAAgB,gBAAA,oBAAAT,MAAA,CAAAa,eAAA,CAAqC;;;;;;IAI7EpB,EAAA,CAAAC,cAAA,6BAE8C;IAFsBD,EAAA,CAAAY,UAAA,uBAAAW,yEAAA;MAAAvB,EAAA,CAAAK,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAaJ,MAAA,CAAAO,IAAA,EAAM;IAAA,EAAC;IACpCd,EAAlD,CAAAE,gBAAA,yCAAAuB,2FAAArB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAmB,qBAAA,EAAAtB,MAAA,MAAAG,MAAA,CAAAmB,qBAAA,GAAAtB,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAiD,iCAAAuB,mFAAAvB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAqB,aAAA,EAAAxB,MAAA,MAAAG,MAAA,CAAAqB,aAAA,GAAAxB,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAkC;IACnFJ,EAAA,CAAAY,UAAA,gCAAAiB,kFAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAsBJ,MAAA,CAAAuB,kBAAA,EAAoB;IAAA,EAAC;IAAC9B,EAAA,CAAAe,YAAA,EAAqB;;;;IAF/Cf,EAAA,CAAA+B,UAAA,0BAAAxB,MAAA,CAAAyB,qBAAA,CAA+C;IACfhC,EAAlD,CAAAgB,gBAAA,0BAAAT,MAAA,CAAAmB,qBAAA,CAAiD,kBAAAnB,MAAA,CAAAqB,aAAA,CAAkC;;;;;;IAIrF5B,EAAA,CAAAC,cAAA,8BACgC;IADUD,EAArB,CAAAY,UAAA,uBAAAqB,0EAAA;MAAAjC,EAAA,CAAAK,aAAA,CAAA6B,GAAA;MAAA,MAAA3B,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAaJ,MAAA,CAAAO,IAAA,EAAM;IAAA,EAAC,yBAAAqB,4EAAA/B,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA6B,GAAA;MAAA,MAAA3B,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAgBJ,MAAA,CAAA6B,WAAA,CAAAhC,MAAA,CAAmB;IAAA,EAAC;IAC7CJ,EAAA,CAAAe,YAAA,EAAsB;;;;IAApDf,EAAA,CAAA+B,UAAA,iBAAAxB,MAAA,CAAA8B,YAAA,CAA6B;;;;;;IAI/BrC,EAAA,CAAAC,cAAA,6BACgD;IAD5BD,EAAA,CAAAY,UAAA,uBAAA0B,yEAAAlC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAaJ,MAAA,CAAAO,IAAA,CAAAV,MAAA,CAAY;IAAA,EAAC;IACEJ,EAAA,CAAAe,YAAA,EAAqB;;;;IAAnEf,EAAA,CAAA+B,UAAA,yBAAAxB,MAAA,CAAAiC,oBAAA,CAA6C;;;ADwCvD,OAAM,MAAOC,eAAe;EAuD1BC,YACUC,OAAe,EACfC,YAA0B,EAC1BC,aAA2B,EAC3BC,qBAA+C;IAH/C,KAAAH,OAAO,GAAPA,OAAO;IACP,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IAzD/B,KAAAC,QAAQ,GAAG3D,mBAAmB,CAAC4D,eAAe,CAAC7D,WAAW,CAAC8D,IAAI,CAAa;IAC5E,KAAAC,IAAI,GAAG,CAAC;IAGR;IACA,KAAAxC,kBAAkB,GAAY,KAAK;IAEnC;IACA,KAAAgB,qBAAqB,GAAkC,EAAE;IACzD,KAAAE,aAAa,GAAW,CAAC;IAEzB;IACA,KAAAS,YAAY,GAAoC,EAAE;IAElD;IACA,KAAAjB,eAAe,GAAqB,EAAE;IAEtC;IACA,KAAAoB,oBAAoB,GAA0B,EAAE;IAEhD;IACA,KAAAW,OAAO,GAAY,KAAK;IACxB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,QAAQ,GAAkBzD,eAAe,CAAC,SAAS,CAAC;IACpD,KAAAoC,qBAAqB,GAAY,KAAK;IACtC,KAAAsB,SAAS,GAAG,CACV;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,MAAM;MACZC,UAAU,EAAE;KACb,EACD;MACEF,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdC,UAAU,EAAE;KACb,EACD;MACEF,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdC,UAAU,EAAE;KACb,EACD;MACEF,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,UAAU;MAChBC,UAAU,EAAE;KACb,EACD;MACEF,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdC,UAAU,EAAE;KACb,CACF;IAQC,IAAI,CAACC,SAAS,GAAGtE,mBAAmB,CAAC4D,eAAe,CAAC7D,WAAW,CAACwE,SAAS,CAAC;IAC3E,IAAI,CAACT,IAAI,GAAG9D,mBAAmB,CAAC4D,eAAe,CAAC7D,WAAW,CAACyE,WAAW,CAAC,IAAI,CAAC;IAC7E,IAAI,CAAC5B,qBAAqB,GAAG5C,mBAAmB,CAAC4D,eAAe,CAAC7D,WAAW,CAAC0E,OAAO,CAAC;EACvF;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACZ,IAAI,IAAI,CAAC,EAAE;MAClB,IAAI,CAACxC,kBAAkB,GAAG,IAAI;;IAEhC,KAAK,IAAIqD,KAAK,GAAG,CAAC,EAAEA,KAAK,IAAI,IAAI,CAACb,IAAI,EAAEa,KAAK,EAAE,EAAE;MAC/C,MAAMC,OAAO,GAAG,IAAI,CAACV,SAAS,CAACS,KAAK,GAAG,CAAC,CAAC;MACzCC,OAAO,CAACP,UAAU,GAAG,IAAI;MACzB,IAAIM,KAAK,IAAI,CAAC,EAAE;QACd,IAAI,CAACE,wBAAwB,EAAE,CAACC,SAAS,EAAE;;MAE7C,IAAIH,KAAK,IAAI,CAAC,EAAE;QACd,IAAI,CAACI,YAAY,EAAE,CAACD,SAAS,EAAE;;MAEjC,IAAIH,KAAK,IAAI,CAAC,EAAE;QACd,IAAI,CAACK,cAAc,EAAE,CAACF,SAAS,EAAE;;MAEnC,IAAIH,KAAK,IAAI,CAAC,EAAE;QACd,IAAI,CAACM,mBAAmB,EAAE;;MAE5B,IAAIN,KAAK,IAAI,CAAC,EAAE;QACd,IAAI,CAACb,IAAI,GAAG,CAAC;;;EAGnB;EAEA;EACAe,wBAAwBA,CAAA;IACtB/E,cAAc,CAACoF,OAAO,CAAC,IAAI,CAAC;IAC5B,OAAO,IAAI,CAACxB,qBAAqB,CAACyB,qDAAqD,CAAC,EAAE,CAAC,CAACC,IAAI,CAC9FvF,GAAG,CAAEwF,GAAG,IAAI;MACV,IAAI,CAAC/C,qBAAqB,GAAG+C,GAAG,CAACC,OAAQ,IAAI,EAAE;IACjD,CAAC,CAAC,EACF3F,QAAQ,CAAC,MAAMG,cAAc,CAACoF,OAAO,CAAC,KAAK,CAAC,CAAC,CAC9C;EACH;EAEA;EACAH,YAAYA,CAAA;IACVjF,cAAc,CAACoF,OAAO,CAAC,IAAI,CAAC;IAC5B,OAAO,IAAI,CAACxB,qBAAqB,CAAC6B,uDAAuD,EAAE,CAACH,IAAI,CAC9FvF,GAAG,CAAEwF,GAAG,IAAI;MACV,IAAI,CAACpC,YAAY,GAAGoC,GAAG,CAACC,OAAQ,IAAI,EAAE;IACxC,CAAC,CAAC,EACF3F,QAAQ,CAAC,MAAMG,cAAc,CAACoF,OAAO,CAAC,KAAK,CAAC,CAAC,CAC9C;EACH;EAEA;EACAF,cAAcA,CAAA;IACZlF,cAAc,CAACoF,OAAO,CAAC,IAAI,CAAC;IAC5B,OAAO,IAAI,CAAC1B,YAAY,CAACgC,+BAA+B,CAAC,EAAE,CAAC,CAACJ,IAAI,CAC/DvF,GAAG,CAAEwF,GAAG,IAAI;MACV,IAAI,CAACrD,eAAe,GAAGqD,GAAG,CAACC,OAAQ,IAAI,EAAE;IAC3C,CAAC,CAAC,EACF3F,QAAQ,CAAC,MAAMG,cAAc,CAACoF,OAAO,CAAC,KAAK,CAAC,CAAC,CAC9C;EACH;EAEA;EACAD,mBAAmBA,CAAA;IACjBnF,cAAc,CAACoF,OAAO,CAAC,IAAI,CAAC;IAC5B,IAAI,CAAC1B,YAAY,CAACiC,oCAAoC,CAAC,EAAE,CAAC,CAACL,IAAI,CAC7DvF,GAAG,CAACwF,GAAG,IAAG;MACR,IAAIA,GAAG,CAACK,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACtC,oBAAoB,GAAGiC,GAAG,CAACC,OAAQ,IAAI,EAAE;;IAElD,CAAC,CAAC,EACF3F,QAAQ,CAAC,MAAMG,cAAc,CAACoF,OAAO,CAAC,KAAK,CAAC,CAAC,CAC9C,CAACJ,SAAS,EAAE;EACf;EAEA9B,WAAWA,CAAC2C,KAAU;IACpB,IAAI,CAAC7B,IAAI,EAAE;IACX,IAAI,CAACtB,aAAa,GAAGmD,KAAK;EAC5B;EAEAjE,IAAIA,CAACiE,KAAW;IACd,IAAI,IAAI,CAAC7B,IAAI,IAAI,CAAC,EAAE;MAClB,IAAI,CAACe,wBAAwB,EAAE,CAACC,SAAS,EAAE;;IAE7C,IAAI,IAAI,CAAChB,IAAI,IAAI,CAAC,EAAE;MAClB,IAAI,CAACiB,YAAY,EAAE,CAACD,SAAS,EAAE;;IAEjC,IAAI,IAAI,CAAChB,IAAI,IAAI,CAAC,EAAE;MAClB,IAAI,CAACkB,cAAc,EAAE,CAACF,SAAS,EAAE;;IAEnC,IAAI,IAAI,CAAChB,IAAI,IAAI,CAAC,EAAE;MAClB,IAAI,CAACmB,mBAAmB,EAAE;;IAE5B,IAAI,IAAI,CAACnB,IAAI,IAAI,CAAC,EAAE;MAClB,IAAI,CAACC,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC6B,cAAc,CAAC,CAAC,CAAC;MACtB,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC;MACrB/F,cAAc,CAACoF,OAAO,CAAC,KAAK,CAAC;MAC7B,IAAIS,KAAK,EAAE;QACT,IAAI,CAAC1B,QAAQ,GAAGzD,eAAe,CAAC,QAAQ,CAAC;OAC1C,MAAM;QACL,IAAI,CAACyD,QAAQ,GAAGzD,eAAe,CAAC,SAAS,CAAC;;MAE5C;;IAEF,IAAI,CAACsD,IAAI,EAAE;IACX,IAAI,CAACI,SAAS,CAAC,IAAI,CAACJ,IAAI,GAAG,CAAC,CAAC,CAACO,UAAU,GAAG,IAAI;IAC/C,IAAI,CAACwB,aAAa,EAAE;EACtB;EAEAC,IAAIA,CAAA;IAAK,IAAI,CAAChC,IAAI,EAAE;EAAC;EAErBiC,eAAeA,CAACC,WAAoB;IAClC,IAAK,CAAC,IAAI,CAACrC,QAAQ,CAACsC,gBAAgB,IAAI,CAACD,WAAW,EAAG;MACrD,IAAI,CAACzC,OAAO,CAAC2C,aAAa,CAAC,MAAM,CAAC;MAClC;;IAGF,IAAIC,OAAO,GAAa;MACtB/B,IAAI,EAAE,IAAI,CAACT,QAAQ,CAACS,IAAI;MACxB6B,gBAAgB,EAAED,WAAW;MAC7BI,gBAAgB,EAAE,IAAI,CAACzC,QAAQ,CAACyC;KACjC;IACDpG,mBAAmB,CAACqG,eAAe,CAACtG,WAAW,CAAC8D,IAAI,EAAEsC,OAAO,CAAC;EAChE;EAEAG,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACrC,QAAQ,CAACsC,KAAK,IAAI,MAAM,EAAE;MACjC,IAAI,CAAChD,OAAO,CAAC2C,aAAa,CAAC,MAAM,CAAC;KACnC,MAAM;MACL,IAAI,CAACnC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACE,QAAQ,GAAGzD,eAAe,CAAC,YAAY,CAAC;;EAEjD;EAEAgG,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACvC,QAAQ,CAACsC,KAAK,IAAI,mBAAmB,EAAE;MAC9C,IAAI,CAACxC,OAAO,GAAG,KAAK;;IAEtB,QAAQ,IAAI,CAACE,QAAQ,CAACsC,KAAK;MACzB,KAAK,QAAQ;QACX,IAAI,CAACxC,OAAO,GAAG,KAAK;QACpB;MACF;;EAEJ;EAEA0C,iBAAiBA,CAAA;IACf,QAAQ,IAAI,CAACxC,QAAQ,CAACsC,KAAK;MACzB,KAAK,SAAS;QACZ,IAAI,CAACG,eAAe,EAAE,CAAC5B,SAAS,CAAC,MAAK;UACpC,IAAI,CAACvB,OAAO,CAAC2C,aAAa,CAAC,MAAM,CAAC;QACpC,CAAC,CAAC;QACF;MACF,KAAK,QAAQ;QACX,IAAI,CAACS,iBAAiB,EAAE;QACxB;MACF;;EAEJ;EAEAjE,kBAAkBA,CAAA;IAChB,IAAI,CAACmC,wBAAwB,EAAE,CAACC,SAAS,EAAE;EAC7C;EAEA5C,sBAAsBA,CAAA;IACpB,IAAI,CAAC8C,cAAc,EAAE,CAACF,SAAS,EAAE;EACnC;EAEA8B,UAAUA,CAACjB,KAAU;IACnB,IAAIA,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAACrE,kBAAkB,EAAE;MAC1C,IAAI,CAACyC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACE,QAAQ,GAAGzD,eAAe,CAAC,mBAAmB,CAAC;MACpD;;IAEF,IAAI,CAACsD,IAAI,GAAG6B,KAAK;IACjB,IAAI,CAACnD,aAAa,GAAG,CAAC;EACxB;EAEAmE,iBAAiBA,CAAA;IACf/G,QAAQ,CAAC,CACP,IAAI,CAAC8D,qBAAqB,CAACmD,+CAA+C,CAAC,EAAE,CAAC,EAC9E,IAAI,CAACrD,YAAY,CAACsD,8BAA8B,CAAC,EAAE,CAAC,CACrD,CAAC,CAAC1B,IAAI,CACLvF,GAAG,CAAC,CAAC,CAACkH,aAAa,EAAEC,QAAQ,CAAC,KAAI;MAChC,IAAID,aAAa,CAACrB,UAAU,IAAI,CAAC,IAAIsB,QAAQ,CAACtB,UAAU,IAAI,CAAC,EAAE;QAC7D,IAAIqB,aAAa,CAACzB,OAAQ,IAAI0B,QAAQ,CAAC1B,OAAQ,EAAE;UAC/C,IAAI,CAAC/B,OAAO,CAAC2C,aAAa,CAAC,SAAS,CAAC;;OAExC,MAAM;QACLa,aAAa,CAACE,OAAQ,KAAK,EAAE,GAAG,IAAI,CAACxD,aAAa,CAACyD,YAAY,CAACH,aAAa,CAACE,OAAQ,CAAC,GAAG,EAAE;QAC5FD,QAAQ,CAACC,OAAQ,KAAK,EAAE,GAAG,IAAI,CAACxD,aAAa,CAACyD,YAAY,CAACF,QAAQ,CAACC,OAAQ,CAAC,GAAG,EAAE;;IAEtF,CAAC,CAAC,CACH,CAACnC,SAAS,EAAE;EACf;EAEA4B,eAAeA,CAAA;IACb5G,cAAc,CAACoF,OAAO,CAAC,IAAI,CAAC;IAC5B,OAAO,IAAI,CAAC1B,YAAY,CAAC2D,gCAAgC,CAAC,EAAE,CAAC,CAAC/B,IAAI,CAChEzF,QAAQ,CAAC,MAAMG,cAAc,CAACoF,OAAO,CAAC,KAAK,CAAC,CAAC,CAC9C;EACH;EAEAW,aAAaA,CAAC/B,IAAa;IACzB,IAAI,CAACN,YAAY,CAAC4D,8BAA8B,CAAC;MAC/CC,IAAI,EAAEvD,IAAI,IAAI,IAAI,CAACA;KACpB,CAAC,CAACgB,SAAS,CAAC,MAAK;MAChB9E,mBAAmB,CAACqG,eAAe,CAACtG,WAAW,CAACyE,WAAW,EAAE,IAAI,CAACV,IAAI,CAAC;IACzE,CAAC,CAAC;EACJ;EAEA8B,cAAcA,CAAC0B,QAAgB;IAC7BxH,cAAc,CAACoF,OAAO,CAAC,IAAI,CAAC;IAC5B,IAAI,CAAC1B,YAAY,CAAC+D,oCAAoC,CAAC;MAAEF,IAAI,EAAEC;IAAQ,CAAE,CAAC,CACvElC,IAAI,CACHvF,GAAG,CAACwF,GAAG,IAAG;MACR,IAAIA,GAAG,CAACK,UAAU,IAAI,CAAC,IAAIL,GAAG,CAAC4B,OAAO,IAAI,SAAS,EAAE;QACnDjH,mBAAmB,CAACqG,eAAe,CAACtG,WAAW,CAACyH,gBAAgB,EAAEF,QAAQ,CAAC;;IAE/E,CAAC,CAAC,CACH,CAACxC,SAAS,EAAE;EACjB;EAAC,QAAA2C,CAAA,G;qBA5RUpE,eAAe,EAAAzC,EAAA,CAAA8G,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAhH,EAAA,CAAA8G,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAlH,EAAA,CAAA8G,iBAAA,CAAAK,EAAA,CAAA1H,YAAA,GAAAO,EAAA,CAAA8G,iBAAA,CAAAG,EAAA,CAAAG,wBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAf5E,eAAe;IAAA6E,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAxH,EAAA,CAAAyH,kBAAA,CATf,CACThI,YAAY,EACZE,cAAc,CACf,GAAAK,EAAA,CAAA0H,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC9DGhI,EAHN,CAAAC,cAAA,aAAqB,aACE,aACc,aACP;QAAAD,EAAA,CAAAkI,MAAA,YAAK;QAC/BlI,EAD+B,CAAAe,YAAA,EAAM,EAC/B;QAEJf,EADF,CAAAC,cAAA,aAAyB,6BAC0F;QAA5FD,EAAA,CAAAE,gBAAA,+BAAAiI,0EAAA/H,MAAA;UAAAJ,EAAA,CAAAS,kBAAA,CAAAwH,GAAA,CAAA3E,SAAA,CAAA2E,GAAA,CAAA/E,IAAA,GAAA9C,MAAA,MAAA6H,GAAA,CAAA3E,SAAA,CAAA2E,GAAA,CAAA/E,IAAA,IAAA9C,MAAA;UAAA,OAAAA,MAAA;QAAA,EAAiC;QAAyBJ,EAAA,CAAAY,UAAA,wBAAAwH,mEAAAhI,MAAA;UAAA,OAAc6H,GAAA,CAAAjC,UAAA,CAAA5F,MAAA,CAAkB;QAAA,EAAC;QAElHJ,EADE,CAAAe,YAAA,EAAsB,EAClB;QAEJf,EADF,CAAAC,cAAA,aAAiC,aACX;QAmBlBD,EAjBA,CAAAqI,UAAA,IAAAC,+BAAA,OAAW,KAAAC,gCAAA,OAGA,KAAAC,gCAAA,OAIA,KAAAC,gCAAA,OAKA,KAAAC,gCAAA,OAKA;QAQnB1I,EAHM,CAAAe,YAAA,EAAM,EACF,EACF,EACF;QAENf,EAAA,CAAAC,cAAA,2BAC4C;QADJD,EAAA,CAAAE,gBAAA,2BAAAyI,oEAAAvI,MAAA;UAAAJ,EAAA,CAAAS,kBAAA,CAAAwH,GAAA,CAAA9E,OAAA,EAAA/C,MAAA,MAAA6H,GAAA,CAAA9E,OAAA,GAAA/C,MAAA;UAAA,OAAAA,MAAA;QAAA,EAAqB;QAC3DJ,EAD4D,CAAAY,UAAA,8BAAAgI,uEAAA;UAAA,OAAoBX,GAAA,CAAArC,gBAAA,EAAkB;QAAA,EAAC,+BAAAiD,wEAAA;UAAA,OAC9EZ,GAAA,CAAApC,iBAAA,EAAmB;QAAA,EAAC;QAC3C7F,EAAA,CAAAe,YAAA,EAAmB;QAEnBf,EAAA,CAAA8I,SAAA,kBAA6E;;;;QArClD9I,EAAA,CAAA+I,SAAA,GAAiC;QAAjC/I,EAAA,CAAAgB,gBAAA,gBAAAiH,GAAA,CAAA3E,SAAA,CAAA2E,GAAA,CAAA/E,IAAA,EAAiC;QAAClD,EAAA,CAAA+B,UAAA,cAAAkG,GAAA,CAAA3E,SAAA,CAAuB;QAK5EtD,EAAA,CAAA+I,SAAA,GAsBC;QAtBD/I,EAAA,CAAAgJ,aAAA,KAAAC,OAAA,GAAAhB,GAAA,CAAA/E,IAAA,OAAC,OAAA+F,OAAA,KAAD,CAAC,QAAAA,OAAA,KAAD,CAAC,QAAAA,OAAA,KAAD,CAAC,QAAAA,OAAA,KAAD,CAAC,WAsBA;QAMSjJ,EAAA,CAAA+I,SAAA,GAAqB;QAArB/I,EAAA,CAAA+B,UAAA,aAAAkG,GAAA,CAAA5E,QAAA,CAAqB;QAACrD,EAAA,CAAAgB,gBAAA,YAAAiH,GAAA,CAAA9E,OAAA,CAAqB;QAIvBnD,EAAA,CAAA+I,SAAA,EAA4B;QAA5B/I,EAAA,CAAAkJ,UAAA,CAAAlJ,EAAA,CAAAmJ,eAAA,IAAAC,GAAA,EAA4B;;;mBDC9D5K,YAAY,EACZkB,WAAW,EAAA2J,EAAA,CAAAC,KAAA,EACX7K,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,iBAAiB,EACjBe,uBAAuB,EAEvBR,qBAAqB,EACrBC,sBAAsB,EACtBC,qBAAqB,EACrBO,oBAAoB,EACpBC,qBAAqB,EACrBP,oBAAoB;IAAA+J,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}