import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DropdownModule } from 'primeng/dropdown';
import { FormsModule } from '@angular/forms';
import { finalize, tap } from 'rxjs';
import { LoadingService } from '../../../../shared/services/loading.service';
import { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';
import _ from 'lodash';
import { DialogPopupComponent } from '../../../../components/dialog-popup/dialog-popup.component';
import { DialogModule } from 'primeng/dialog';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { HouseService } from '../../../../../services/api/services';

@Component({
  selector: 'app-step-i-choice',
  standalone: true,
  imports: [
    FormsModule,
    DropdownModule,
    DialogModule,
    PdfViewerModule,
    CheckboxModule,
    ButtonModule,
    DialogPopupComponent,
    BaseFilePipe
  ],
  templateUrl: './step-i-choice.component.html',
  styleUrl: './step-i-choice.component.scss'
})
export class StepIChoiceComponent implements OnInit {

  @Input() agreeToGoNextStep1: boolean = false;

  @Output() agreeToGoNextStep1Change = new EventEmitter();
  @Output() nextEvent = new EventEmitter()

  detailPicture: string = ""

  constructor(
    private _houseService: HouseService,
  ) { }

  ngOnInit(): void {
    this.getImage()
  }

  getImage() {
    LoadingService.loading(true);
    this._houseService.apiHouseGetRegularNoticeFilePost$Json({})
      .pipe(
        tap(res => {
          if (res.Entries) {
            this.detailPicture = res.Entries!.CFileUrl!
          }
        }),
        finalize(() => LoadingService.loading(false))
      ).subscribe();
  }

  next() {
    this.nextEvent.emit();
  }

  changeCheckbox(event: any) {
    this.agreeToGoNextStep1Change.emit(event)
  }
}
