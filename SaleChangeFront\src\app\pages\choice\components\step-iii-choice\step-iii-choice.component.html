<section class="flex flex-col justify-center items-center w-full">
  <div class="w-full max-w-4xl mt-6" *ngIf="selectedData.length > 0">
    <p-accordion [multiple]="true">
      <p-accordionTab *ngFor="let item of selectedData; let i = index">
        <ng-template pTemplate="header">
          <div class="flex justify-between items-center w-full">
            <div class="flex flex-col">
              <div class="font-bold text-xl">{{item.CItemName}}</div>
              <span *ngIf="item.RegularDetails && item.RegularDetails[0]" class="font-bold text-[#B69867] text-lg mt-1">
                選擇項目：{{item.RegularDetails[0].CSelectName}}
              </span>
            </div>
            <div class="font-bold text-[#B69867] text-lg" *ngIf="item.CShowPrice">小計: NT$ {{getItemTotal(item) |
              number:'1.0-0'}}</div>
          </div>
        </ng-template>
        <div *ngFor="let detail of item.RegularDetails" class="mb-4 px-4">
          <img class="fit-size rounded shadow" [src]="detail.CFile | addBaseFile" *ngIf="detail.CFile">
        </div>
      </p-accordionTab>
    </p-accordion>
    <div class="bg-[#F3F1EA80] p-4 mt-6 rounded-lg flex justify-between items-center"
      *ngIf="selectedData[0].CShowPrice">
      <span class="font-bold text-[#333333]">總計金額</span>
      <span class="font-bold text-lg text-[#B69867]">NT$ {{getTotalPrice() | number:'1.0-0'}}</span>
    </div>
  </div>
  <div class="no-data" *ngIf="selectedData.length <= 0">
    表單尚未鎖定
  </div>
  <button class="button2 !w-48 butn1 flex justify-center items-center mt-4 mb-4" *ngIf="selectedData.length > 0" pButton
    (click)="next()">
    下一步
  </button>
</section>