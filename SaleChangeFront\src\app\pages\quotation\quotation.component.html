<div class="wrapper">
  <div class="content">

    <!-- 載入狀態 -->
    <div *ngIf="isLoading()" class="loading-container">
      <div class="loading-spinner">載入中...</div>
    </div>

    <!-- 錯誤狀態 -->
    <div *ngIf="error()" class="error-container">
      <div class="error-message">
        <i class="icon-alert"></i>
        {{ error() }}
      </div>
      <button class="btn btn-primary" (click)="loadQuotationHistory()">重新載入</button>
    </div>

    <!-- 主要內容 - 只有在沒有載入且沒有錯誤時顯示 -->
    <div *ngIf="!isLoading() && !error()">

      <!-- 頁面標題 -->
      <div class="flex justify-center mb-6">
        <div class="page-title">報價單管理</div>
      </div>

      <!-- 無報價單時的空狀態 -->
      <div *ngIf="quotationVersions().length === 0" class="empty-state">
        <div class="empty-state-content">
          <div class="empty-icon">
            <i class="icon-file-text"></i>
          </div>
          <h3 class="empty-title">暫無報價單記錄</h3>
          <p class="empty-description">
            目前系統中沒有任何報價單，<br>
            請確認是否有報價單資料或嘗試重新載入
          </p>
          <div class="empty-actions">
            <button class="btn btn-primary" (click)="loadQuotationHistory()">
              <i class="icon-refresh-cw"></i>
              重新載入資料
            </button>
          </div>
        </div>
      </div>

      <!-- 有報價單時顯示的內容 -->
      <div *ngIf="quotationVersions().length > 0">
        <!-- 頁面標題與版本控制 -->
        <div class="quotation-header">
          <div class="header-content">
            <div class="quotation-info" *ngIf="selectedVersion()">
              <div class="quotation-main-section">
                <!-- 報價單編號區域 -->
                <div class="quotation-number-section">
                  <div class="number-container">
                    <div class="field-title">
                      <i class="icon-file-text"></i>
                      <span>報價單號</span>
                    </div>
                    <div class="number-badge">
                      <span class="number-text">{{ getVersionNumber(selectedVersion()!) }}</span>
                    </div>
                  </div>
                  <div class="status-container">
                    <div class="field-title">
                      <i class="icon-info"></i>
                      <span>狀態</span>
                    </div>
                    <div class="status-wrapper">
                      <span class="status-badge" [ngClass]="getStatusClass(selectedVersion()!)">
                        <i [class]="getStatusIcon(selectedVersion()!)"></i>
                        {{ getStatusText(selectedVersion()!) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 詳細資訊區域 -->
                <div class="quotation-details-grid">
                  <div class="detail-item">
                    <div class="detail-label">
                      <i class="icon-calendar"></i>
                      <span>建立日期</span>
                    </div>
                    <div class="detail-value">
                      {{ getCreateDate(selectedVersion()!) | date:'yyyy/MM/dd' }}
                    </div>
                  </div>
                  <div class="detail-item amount-highlight">
                    <div class="detail-label">
                      <i class="icon-dollar-sign"></i>
                      <span>總金額</span>
                    </div>
                    <div class="detail-value amount-value">
                      NT$ {{ selectedVersion()!.CTotalAmount | number:'1.0-0' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按鈕區域 - 優化後的獨立區塊 -->
            <div class="header-actions-container">
              <div class="actions-header">
                <h3 class="actions-title">
                  <i class="icon-settings"></i>
                  操作功能
                </h3>
              </div>
              <div class="header-actions">
                <button class="btn btn-action btn-outline" (click)="toggleVersionHistory()">
                  <div class="btn-icon">
                    <i class="icon-history"></i>
                  </div>
                  <div class="btn-content">
                    <span class="btn-label">版本歷程</span>
                    <span class="btn-description">查看所有版本</span>
                  </div>
                </button>
                <button class="btn btn-action btn-primary" (click)="printPreview()">
                  <div class="btn-icon">
                    <i class="icon-printer"></i>
                  </div>
                  <div class="btn-content">
                    <span class="btn-label">預覽列印</span>
                    <span class="btn-description">列印報價單</span>
                  </div>
                </button>

                <!-- 根據狀態顯示不同的操作按鈕 -->
                <button class="btn btn-action btn-success" (click)="viewContract()"
                  *ngIf="selectedVersion() && convertStatusFromAPI(selectedVersion()!.CQuotationStatus) === 'confirmed'">
                  <div class="btn-icon">
                    <i class="icon-file-text"></i>
                  </div>
                  <div class="btn-content">
                    <span class="btn-label">查看合約</span>
                    <span class="btn-description">檢視合約內容</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 版本歷程 Dialog -->
        <app-dialog-popup [(visible)]="showVersionHistory"
          [textData]="{header: '版本歷程', content: '', titleButtonLeft: '', titleButtonRight: ''}">

          <div class="version-dialog-content">
            <!-- 載入狀態 -->
            <div *ngIf="isLoading()" class="dialog-loading">
              <div class="loading-spinner">
                <i class="icon-loader"></i>
                <span>載入版本資料中...</span>
              </div>
            </div>

            <!-- 錯誤狀態 -->
            <div *ngIf="error()" class="dialog-error">
              <div class="error-content">
                <i class="icon-alert-triangle"></i>
                <span class="error-text">{{ error() }}</span>
                <button class="btn btn-primary" (click)="loadQuotationHistory()">
                  <i class="icon-refresh-cw"></i>
                  重新載入
                </button>
              </div>
            </div>

            <!-- 空狀態 -->
            <div *ngIf="!isLoading() && !error() && quotationVersions().length === 0" class="dialog-empty">
              <div class="empty-content">
                <i class="icon-file-text"></i>
                <span>暫無版本記錄</span>
              </div>
            </div>

            <!-- 版本清單 -->
            <div *ngIf="!isLoading() && !error() && quotationVersions().length > 0" class="version-list">
              <div class="version-item" *ngFor="let version of quotationVersions(); let i = index"
                [class.selected]="selectedVersion() === version" (click)="selectVersion(version)">
                <div class="version-header">
                  <div class="version-number">
                    <i class="icon-file-text"></i>
                    <span>{{ getVersionNumber(version) }}</span>
                  </div>
                  <div class="version-status">
                    <span class="status-badge" [ngClass]="getStatusClass(version)">
                      <i [class]="getStatusIcon(version)"></i>
                      {{ getStatusText(version) }}
                    </span>
                  </div>
                </div>
                <div class="version-details">
                  <div class="version-date">
                    <i class="icon-calendar"></i>
                    <span>{{ getCreateDate(version) | date:'yyyy/MM/dd HH:mm' }}</span>
                  </div>
                  <div class="version-amount">
                    <i class="icon-dollar-sign"></i>
                    <span>NT$ {{ version.CTotalAmount | number:'1.0-0' }}</span>
                  </div>
                </div>
                <div class="version-items" *ngIf="version.tblQuotationItems">
                  <span class="items-count">
                    <i class="icon-list"></i>
                    {{ version.tblQuotationItems.length }} 個項目
                  </span>
                </div>
                <!-- 額外費用資訊 -->
                <div class="version-other-fee" *ngIf="version.CShowOther && version.COtherPercent">
                  <span class="other-fee-info">
                    <i class="icon-plus-circle"></i>
                    {{ version.COtherName || '額外費用' }} ({{ version.COtherPercent }}%)
                  </span>
                </div>
              </div>
            </div>
          </div>
        </app-dialog-popup>

        <!-- 主要內容區域 -->
        <div class="quotation-content">
          <div class="quotation-details" *ngIf="selectedVersion()">

            <!-- 報價項目 -->
            <div class="section items-section">
              <div class="section-card">
                <div class="section-header">
                  <div class="section-title-wrapper">
                    <h2 class="section-title">
                      <i class="icon-list"></i>
                      報價項目
                    </h2>
                    <div class="items-count-badge" *ngIf="selectedVersion()?.tblQuotationItems">
                      <i class="icon-hash"></i>
                      <span class="count-text">{{ selectedVersion()?.tblQuotationItems?.length || 0 }}</span>
                      <span class="count-label">個項目</span>
                    </div>
                  </div>
                </div>
                <div class="items-table">
                  <div class="table-header">
                    <div class="col-item">
                      <i class="icon-package"></i>
                      項目名稱
                    </div>
                    <div class="col-unit">
                      <i class="icon-tag"></i>
                      單位
                    </div>
                    <div class="col-qty">
                      <i class="icon-hash"></i>
                      數量
                    </div>
                    <div class="col-price">
                      <i class="icon-dollar-sign"></i>
                      單價
                    </div>
                    <div class="col-total">
                      <i class="icon-calculator"></i>
                      小計
                    </div>
                    <div class="col-remark">
                      <i class="icon-message-square"></i>
                      備註
                    </div>
                  </div>
                  <div class="table-body">
                    <div class="table-row" *ngFor="let item of selectedVersion()?.tblQuotationItems; let i = index"
                      [class.row-even]="i % 2 === 0">
                      <div class="col-item">
                        <div class="item-info">
                          <span class="item-name">{{ item.CItemName }}</span>
                        </div>
                      </div>
                      <div class="col-unit">
                        <span class="unit-value">{{ item.CUnit || '-' }}</span>
                      </div>
                      <div class="col-qty">
                        <span class="qty-value">{{ item.CCount }}</span>
                      </div>
                      <div class="col-price">
                        <span class="price-value">NT$ {{ item.CUnitPrice | number:'1.0-0' }}</span>
                      </div>
                      <div class="col-total">
                        <span class="total-value">NT$ {{ item.CSubtotal | number:'1.0-0' }}</span>
                      </div>
                      <div class="col-remark">
                        <span class="remark-value">{{ item.CRemark || '-' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 金額計算 -->
          <div class="section calculation-section">
            <div class="section-card">
              <div class="section-header">
                <h2 class="section-title">
                  <i class="icon-calculator"></i>
                  金額明細
                </h2>
              </div>
              <div class="calculation-table">
                <!-- 小計 -->
                <div class="calc-row subtotal-row">
                  <div class="calc-label">
                    <i class="icon-plus"></i>
                    <span>小計</span>
                  </div>
                  <div class="calc-value">
                    NT$ {{ calculateSubtotal(selectedVersion()!.tblQuotationItems) | number:'1.0-0' }}
                  </div>
                </div>

                <!-- 額外費用區域 (自定義) -->
                <div class="calc-row other-fee-row"
                  *ngIf="selectedVersion()!.CShowOther && selectedVersion()!.COtherPercent">
                  <div class="calc-label">
                    <i class="icon-plus-circle"></i>
                    <span>{{ selectedVersion()!.COtherName || '額外費用' }} ({{ selectedVersion()!.COtherPercent }}%)</span>
                  </div>
                  <div class="calc-value">
                    NT$ {{ calculateOtherFee(selectedVersion()!.tblQuotationItems, selectedVersion()!.COtherPercent) |
                    number:'1.0-0' }}
                  </div>
                </div>

                <!-- 總計 -->
                <div class="calc-row total-row">
                  <div class="calc-label">
                    <i class="icon-dollar-sign"></i>
                    <span>總計</span>
                  </div>
                  <div class="calc-value total-amount">
                    NT$ {{ calculateTotalWithOther(selectedVersion()!) | number:'1.0-0' }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 備註 -->
          <div class="section notes-section">
            <div class="section-card">
              <div class="section-header">
                <h2 class="section-title">
                  <i class="icon-info"></i>
                  重要備註
                </h2>
              </div>
              <div class="notes-content">
                <div class="note-category">
                  <h4 class="note-category-title">
                    <i class="icon-clock"></i>
                    效期與時間
                  </h4>
                  <div class="note-item">
                    <span class="note-text">報價單有效期限為30天</span>
                  </div>
                </div>

                <div class="note-category">
                  <h4 class="note-category-title">
                    <i class="icon-dollar-sign"></i>
                    價格說明
                  </h4>
                  <div class="note-item">
                    <span class="note-text">價格含稅</span>
                  </div>
                </div>

                <div class="note-category">
                  <h4 class="note-category-title">
                    <i class="icon-credit-card"></i>
                    付款條件
                  </h4>
                  <div class="note-item">
                    <span class="note-text">付款方式：交貨後30天內付款</span>
                  </div>
                </div>

                <div class="note-category">
                  <h4 class="note-category-title">
                    <i class="icon-help-circle"></i>
                    聯繫方式
                  </h4>
                  <div class="note-item">
                    <span class="note-text">如有疑問請洽詢業務人員</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div> <!-- 關閉主要內容區塊 -->

  </div> <!-- 關閉有報價單時顯示的內容 -->
</div>