import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { CheckboxModule } from 'primeng/checkbox';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { HouseService } from '../../../../../services/api/services';

@Component({
  selector: 'app-step-iv-choice',
  standalone: true,
  imports: [
    BaseFilePipe,

    PdfViewerModule,
    CheckboxModule,
    ButtonModule,
    FormsModule,
    CommonModule
  ],
  templateUrl: './step-iv-choice.component.html',
  styleUrl: './step-iv-choice.component.scss'
})

export class StepIvChoiceComponent implements OnInit {

  @Input() agreeToGoNextStep4: boolean = false;

  @Output() agreeToGoNextStep4Change = new EventEmitter();
  @Output() nextEvent = new EventEmitter()

  detailPicture: string | any

  constructor(
    private _houseService: HouseService
  ) {

  }

  ngOnInit(): void {
    this.getImage()
  }

  getImage() {
    this._houseService.apiHouseGetSpecialNoticeFilePost$Json({})
      .subscribe(res => {
        if (res.Entries) {
          this.detailPicture = res.Entries!.CFileUrl!
        }
      })
  }

  next() {
    this.nextEvent.emit();
  }

  changeCheckbox(event: any) {
    this.agreeToGoNextStep4Change.emit(event)
  }
}
