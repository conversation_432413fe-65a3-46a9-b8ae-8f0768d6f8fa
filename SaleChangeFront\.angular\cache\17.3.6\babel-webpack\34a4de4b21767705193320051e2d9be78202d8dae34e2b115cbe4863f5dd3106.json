{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';\nimport { CommonModule } from '@angular/common';\nimport { LoadingService } from '../../../../shared/services/loading.service';\nimport { finalize } from 'rxjs';\nimport { AccordionModule } from 'primeng/accordion';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../services/api/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"../../services/check-user-data.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/accordion\";\nfunction StepIiiChoiceComponent_div_1_p_accordionTab_2_ng_template_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u9078\\u64C7\\u9805\\u76EE\\uFF1A\", item_r1.RegularDetails[0].CSelectName, \" \");\n  }\n}\nfunction StepIiiChoiceComponent_div_1_p_accordionTab_2_ng_template_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u5C0F\\u8A08: NT$ \", i0.ɵɵpipeBind2(2, 1, ctx_r1.getItemTotal(item_r1), \"1.0-0\"), \"\");\n  }\n}\nfunction StepIiiChoiceComponent_div_1_p_accordionTab_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, StepIiiChoiceComponent_div_1_p_accordionTab_2_ng_template_1_span_4_Template, 2, 1, \"span\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, StepIiiChoiceComponent_div_1_p_accordionTab_2_ng_template_1_div_5_Template, 3, 4, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r1.CItemName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r1.RegularDetails && item_r1.RegularDetails[0]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r1.CShowPrice);\n  }\n}\nfunction StepIiiChoiceComponent_div_1_p_accordionTab_2_div_2_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 19);\n    i0.ɵɵpipe(1, \"addBaseFile\");\n  }\n  if (rf & 2) {\n    const detail_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, detail_r3.CFile), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction StepIiiChoiceComponent_div_1_p_accordionTab_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, StepIiiChoiceComponent_div_1_p_accordionTab_2_div_2_img_1_Template, 2, 3, \"img\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r3.CFile);\n  }\n}\nfunction StepIiiChoiceComponent_div_1_p_accordionTab_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-accordionTab\");\n    i0.ɵɵtemplate(1, StepIiiChoiceComponent_div_1_p_accordionTab_2_ng_template_1_Template, 6, 3, \"ng-template\", 8)(2, StepIiiChoiceComponent_div_1_p_accordionTab_2_div_2_Template, 2, 1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r1.RegularDetails);\n  }\n}\nfunction StepIiiChoiceComponent_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"span\", 21);\n    i0.ɵɵtext(2, \"\\u7E3D\\u8A08\\u91D1\\u984D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 22);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind2(5, 1, ctx_r1.getTotalPrice(), \"1.0-0\"), \"\");\n  }\n}\nfunction StepIiiChoiceComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"p-accordion\", 5);\n    i0.ɵɵtemplate(2, StepIiiChoiceComponent_div_1_p_accordionTab_2_Template, 3, 1, \"p-accordionTab\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, StepIiiChoiceComponent_div_1_div_3_Template, 6, 4, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"multiple\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedData);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedData[0].CShowPrice);\n  }\n}\nfunction StepIiiChoiceComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" \\u8868\\u55AE\\u5C1A\\u672A\\u9396\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepIiiChoiceComponent_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function StepIiiChoiceComponent_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.next());\n    });\n    i0.ɵɵtext(1, \" \\u4E0B\\u4E00\\u6B65 \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class StepIiiChoiceComponent {\n  constructor(_regularChangeService, router, messageService, checkUserDataService) {\n    this._regularChangeService = _regularChangeService;\n    this.router = router;\n    this.messageService = messageService;\n    this.checkUserDataService = checkUserDataService;\n    this.selectedData = [];\n    this.nextEvent = new EventEmitter();\n    this.goBackEvent = new EventEmitter();\n  }\n  ngOnInit() {\n    this.getDataStep3();\n    this.selectedData = this.checkUserDataService.getSelectedData();\n  }\n  next() {\n    const hasUnsetPrice = this.selectedData.some(item => item.RegularDetails?.some(detail => detail.CPrice === null));\n    if (hasUnsetPrice) {\n      this.messageService.add({\n        severity: 'warn',\n        summary: '警告',\n        detail: '有項目尚未設定價格，請確認後再繼續。'\n      });\n      return;\n    }\n    this.nextEvent.emit();\n  }\n  goBackHandle(step) {\n    this.goBackEvent.emit(step);\n  }\n  getDataStep3() {\n    LoadingService.loading(true);\n    this._regularChangeService.apiRegularChangeItemGetSumaryRegularChangeItemPost$Json().pipe(finalize(() => LoadingService.loading(false))).subscribe(res => {\n      if (res.Entries) {\n        this.selectedData = res.Entries;\n      }\n    });\n  }\n  getTotalPrice() {\n    return this.selectedData.reduce((total, item) => {\n      const detailsTotal = item.RegularDetails?.reduce((detailTotal, detail) => {\n        return detailTotal + (detail.CPrice || 0);\n      }, 0) || 0;\n      return total + detailsTotal;\n    }, 0);\n  }\n  getItemTotal(item) {\n    return item.RegularDetails?.reduce((sum, detail) => {\n      return sum + (detail.CPrice || 0);\n    }, 0) || 0;\n  }\n  static #_ = this.ɵfac = function StepIiiChoiceComponent_Factory(t) {\n    return new (t || StepIiiChoiceComponent)(i0.ɵɵdirectiveInject(i1.RegularChangeItemService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.CheckUserDataService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StepIiiChoiceComponent,\n    selectors: [[\"app-step-iii-choice\"]],\n    inputs: {\n      selectedData: \"selectedData\"\n    },\n    outputs: {\n      nextEvent: \"nextEvent\",\n      goBackEvent: \"goBackEvent\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 4,\n    vars: 3,\n    consts: [[1, \"flex\", \"flex-col\", \"justify-center\", \"items-center\", \"w-full\"], [\"class\", \"w-full max-w-4xl mt-6\", 4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"class\", \"button2 !w-48 butn1 flex justify-center items-center mt-4 mb-4\", \"pButton\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"w-full\", \"max-w-4xl\", \"mt-6\"], [3, \"multiple\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"bg-[#F3F1EA80] p-4 mt-6 rounded-lg flex justify-between items-center\", 4, \"ngIf\"], [\"pTemplate\", \"header\"], [\"class\", \"mb-4 px-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"w-full\"], [1, \"flex\", \"flex-col\"], [1, \"font-bold\", \"text-xl\"], [\"class\", \"font-bold text-[#B69867] text-lg mt-1\", 4, \"ngIf\"], [\"class\", \"font-bold text-[#B69867] text-lg\", 4, \"ngIf\"], [1, \"font-bold\", \"text-[#B69867]\", \"text-lg\", \"mt-1\"], [1, \"font-bold\", \"text-[#B69867]\", \"text-lg\"], [1, \"mb-4\", \"px-4\"], [\"class\", \"fit-size rounded shadow\", 3, \"src\", 4, \"ngIf\"], [1, \"fit-size\", \"rounded\", \"shadow\", 3, \"src\"], [1, \"bg-[#F3F1EA80]\", \"p-4\", \"mt-6\", \"rounded-lg\", \"flex\", \"justify-between\", \"items-center\"], [1, \"font-bold\", \"text-[#333333]\"], [1, \"font-bold\", \"text-lg\", \"text-[#B69867]\"], [1, \"no-data\"], [\"pButton\", \"\", 1, \"button2\", \"!w-48\", \"butn1\", \"flex\", \"justify-center\", \"items-center\", \"mt-4\", \"mb-4\", 3, \"click\"]],\n    template: function StepIiiChoiceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵtemplate(1, StepIiiChoiceComponent_div_1_Template, 4, 3, \"div\", 1)(2, StepIiiChoiceComponent_div_2_Template, 2, 0, \"div\", 2)(3, StepIiiChoiceComponent_button_3_Template, 2, 0, \"button\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedData.length > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedData.length <= 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedData.length > 0);\n      }\n    },\n    dependencies: [FormsModule, CommonModule, i5.NgForOf, i5.NgIf, i5.DecimalPipe, BaseFilePipe, AccordionModule, i6.Accordion, i6.AccordionTab, i3.PrimeTemplate],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}html[_ngcontent-%COMP%]{line-height:initial;-webkit-text-size-adjust:100%;width:100%;height:100%}body[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:#23181599;font-family:Noto Sans TC!important;box-sizing:border-box;width:100%;height:auto;overflow:auto;overscroll-behavior:none;letter-spacing:.32px}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{-webkit-text-size-adjust:none;text-size-adjust:none}main[_ngcontent-%COMP%]{display:block}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:400}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible;margin:10px 0;border:none;border-top:1px solid #000}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1rem}a[_ngcontent-%COMP%]{background-color:transparent;text-decoration:none;-webkit-tap-highlight-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:500}strong[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:600}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}p[_ngcontent-%COMP%]{font-size:1rem}img[_ngcontent-%COMP%]{border-style:none;max-width:100%}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:Noto Sans TC,\\\\5fae\\\\8edf\\\\6b63\\\\9ed1\\\\9ad4,Arial,sans-serif;font-size:1rem;line-height:1.15;margin:0;color:#2f2f2f;-webkit-tap-highlight-color:transparent;letter-spacing:2px}select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]{padding:0;background-color:transparent;border:none;cursor:pointer}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}div[role=tab][_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]{border-spacing:0px}ol[_ngcontent-%COMP%], ul[_ngcontent-%COMP%]{padding-left:0;margin-top:0}.form-check-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:none}.container[_ngcontent-%COMP%]{width:100%}@media (min-width: 640px){.container[_ngcontent-%COMP%]{max-width:640px}}@media (min-width: 768px){.container[_ngcontent-%COMP%]{max-width:768px}}@media (min-width: 1024px){.container[_ngcontent-%COMP%]{max-width:1024px}}@media (min-width: 1280px){.container[_ngcontent-%COMP%]{max-width:1280px}}@media (min-width: 1536px){.container[_ngcontent-%COMP%]{max-width:1536px}}.visible[_ngcontent-%COMP%]{visibility:visible}.collapse[_ngcontent-%COMP%]{visibility:collapse}.static[_ngcontent-%COMP%]{position:static}.absolute[_ngcontent-%COMP%]{position:absolute}.relative[_ngcontent-%COMP%]{position:relative}.inset-y-0[_ngcontent-%COMP%]{top:0;bottom:0}.bottom-0[_ngcontent-%COMP%]{bottom:0}.left-0[_ngcontent-%COMP%]{left:0}.left-1\\\\/2[_ngcontent-%COMP%]{left:50%}.left-3[_ngcontent-%COMP%]{left:.75rem}.right-0[_ngcontent-%COMP%]{right:0}.top-1[_ngcontent-%COMP%]{top:.25rem}.top-1\\\\/2[_ngcontent-%COMP%]{top:50%}.top-3[_ngcontent-%COMP%]{top:.75rem}.m-0[_ngcontent-%COMP%]{margin:0}.m-2[_ngcontent-%COMP%]{margin:.5rem}.m-auto[_ngcontent-%COMP%]{margin:auto}.mx-3[_ngcontent-%COMP%]{margin-left:.75rem;margin-right:.75rem}.mx-4[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.mx-5[_ngcontent-%COMP%]{margin-left:1.25rem;margin-right:1.25rem}.mx-\\\\__ph-0__[_ngcontent-%COMP%]{margin-left:20%;margin-right:20%}.my-12[_ngcontent-%COMP%]{margin-top:3rem;margin-bottom:3rem}.my-2[_ngcontent-%COMP%]{margin-top:.5rem;margin-bottom:.5rem}.my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.my-6[_ngcontent-%COMP%]{margin-top:1.5rem;margin-bottom:1.5rem}.\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:.75rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ml-1[_ngcontent-%COMP%]{margin-left:.25rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.ml-4[_ngcontent-%COMP%]{margin-left:1rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-4[_ngcontent-%COMP%]{margin-right:1rem}.mr-5[_ngcontent-%COMP%]{margin-right:1.25rem}.mr-8[_ngcontent-%COMP%]{margin-right:2rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.mt-14[_ngcontent-%COMP%]{margin-top:3.5rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:.75rem}.mt-4[_ngcontent-%COMP%]{margin-top:1rem}.mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.mt-6[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-7[_ngcontent-%COMP%]{margin-top:1.75rem}.mt-8[_ngcontent-%COMP%]{margin-top:2rem}.mt-\\\\__ph-0__[_ngcontent-%COMP%]{margin-top:16px}.block[_ngcontent-%COMP%]{display:block}.flex[_ngcontent-%COMP%]{display:flex}.table[_ngcontent-%COMP%]{display:table}.table-row[_ngcontent-%COMP%]{display:table-row}.grid[_ngcontent-%COMP%]{display:grid}.\\\\!h-\\\\__ph-0__[_ngcontent-%COMP%]{height:107px!important}.\\\\!h-full[_ngcontent-%COMP%]{height:100%!important}.h-28[_ngcontent-%COMP%]{height:7rem}.h-32[_ngcontent-%COMP%]{height:8rem}.h-6[_ngcontent-%COMP%]{height:1.5rem}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:100px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:145px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:280px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:31px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:40vh}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:47px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:480px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:4px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:50px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:55px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:665px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:80px}.h-auto[_ngcontent-%COMP%]{height:auto}.h-fit[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content}.h-full[_ngcontent-%COMP%]{height:100%}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:200px}.\\\\!min-h-\\\\__ph-0__[_ngcontent-%COMP%]{min-height:60vh!important}.\\\\!w-40[_ngcontent-%COMP%]{width:10rem!important}.\\\\!w-48[_ngcontent-%COMP%]{width:12rem!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:107px!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:120px!important}.\\\\!w-full[_ngcontent-%COMP%]{width:100%!important}.w-1\\\\/3[_ngcontent-%COMP%]{width:33.333333%}.w-2\\\\/3[_ngcontent-%COMP%]{width:66.666667%}.w-28[_ngcontent-%COMP%]{width:7rem}.w-6[_ngcontent-%COMP%]{width:1.5rem}.w-7\\\\/12[_ngcontent-%COMP%]{width:58.333333%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:100%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:150px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:160px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:309px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:30rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:400px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:45%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:485px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:50%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:550px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:660px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:760px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:88px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:968px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:97%}.w-full[_ngcontent-%COMP%]{width:100%}.\\\\!max-w-full[_ngcontent-%COMP%]{max-width:100%!important}.max-w-4xl[_ngcontent-%COMP%]{max-width:56rem}.max-w-\\\\__ph-0__[_ngcontent-%COMP%]{max-width:1216px}.flex-shrink[_ngcontent-%COMP%]{flex-shrink:1}.grow[_ngcontent-%COMP%]{flex-grow:1}.border-collapse[_ngcontent-%COMP%]{border-collapse:collapse}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.resize-none[_ngcontent-%COMP%]{resize:none}.grid-cols-10[_ngcontent-%COMP%]{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}.grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}.flex-row[_ngcontent-%COMP%]{flex-direction:row}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.items-start[_ngcontent-%COMP%]{align-items:flex-start}.items-center[_ngcontent-%COMP%]{align-items:center}.justify-start[_ngcontent-%COMP%]{justify-content:flex-start}.justify-end[_ngcontent-%COMP%]{justify-content:flex-end}.justify-center[_ngcontent-%COMP%]{justify-content:center}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.gap-x-1[_ngcontent-%COMP%]{column-gap:.25rem}.self-center[_ngcontent-%COMP%]{align-self:center}.overflow-hidden[_ngcontent-%COMP%]{overflow:hidden}.overflow-y-scroll[_ngcontent-%COMP%]{overflow-y:scroll}.rounded[_ngcontent-%COMP%]{border-radius:.25rem}.rounded-3xl[_ngcontent-%COMP%]{border-radius:1.5rem}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.rounded-lg[_ngcontent-%COMP%]{border-radius:.5rem}.rounded-md[_ngcontent-%COMP%]{border-radius:.375rem}.border[_ngcontent-%COMP%]{border-width:1px}.border-2[_ngcontent-%COMP%]{border-width:2px}.border-solid[_ngcontent-%COMP%]{border-style:solid}.border-blue-400[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(96 165 250 / var(--tw-border-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 143 199 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(184 166 118 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea80}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea99}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(217 217 217 / var(--tw-bg-opacity))}.bg-gray-500[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(107 114 128 / var(--tw-bg-opacity))}.bg-white[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.bg-opacity-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: .04}.object-cover[_ngcontent-%COMP%]{object-fit:cover}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-4[_ngcontent-%COMP%]{padding:1rem}.p-\\\\__ph-0__[_ngcontent-%COMP%]{padding:16px}.px-3[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-6[_ngcontent-%COMP%]{padding-left:1.5rem;padding-right:1.5rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.py-2[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.pb-4[_ngcontent-%COMP%]{padding-bottom:1rem}.pb-5[_ngcontent-%COMP%]{padding-bottom:1.25rem}.pb-8[_ngcontent-%COMP%]{padding-bottom:2rem}.pl-4[_ngcontent-%COMP%]{padding-left:1rem}.pl-6[_ngcontent-%COMP%]{padding-left:1.5rem}.pr-1[_ngcontent-%COMP%]{padding-right:.25rem}.pr-3[_ngcontent-%COMP%]{padding-right:.75rem}.pr-4[_ngcontent-%COMP%]{padding-right:1rem}.text-left[_ngcontent-%COMP%]{text-align:left}.\\\\!text-center[_ngcontent-%COMP%]{text-align:center!important}.text-center[_ngcontent-%COMP%]{text-align:center}.text-right[_ngcontent-%COMP%]{text-align:right}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-3xl[_ngcontent-%COMP%]{font-size:1.875rem;line-height:2.25rem}.text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xl[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-light[_ngcontent-%COMP%]{font-weight:300}.font-medium[_ngcontent-%COMP%]{font-weight:500}.font-normal[_ngcontent-%COMP%]{font-weight:400}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.tracking-wider[_ngcontent-%COMP%]{letter-spacing:.05em}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 143 199 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(35 24 21 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(182 152 103 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(184 166 118 / var(--tw-text-opacity))}.text-black[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-blue-400[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.text-gray-500[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-stone-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(87 83 78 / var(--tw-text-opacity))}.text-stone-900[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(28 25 23 / var(--tw-text-opacity))}.text-white[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.shadow[_ngcontent-%COMP%]{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.grayscale[_ngcontent-%COMP%]{--tw-grayscale: grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter[_ngcontent-%COMP%]{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.content[_ngcontent-%COMP%]{width:100%}@media screen and (max-width: 1024px){.content[_ngcontent-%COMP%]{width:100%;padding:0 16px 50px}}.pc[_ngcontent-%COMP%], .inpc[_ngcontent-%COMP%]{display:initial}@media screen and (max-width: 1024px){.inpc[_ngcontent-%COMP%]{display:none}}.flat[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 1024px){.flat[_ngcontent-%COMP%]{display:initial}}.mb[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.mb[_ngcontent-%COMP%]{display:initial}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{position:relative}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background-image:url(/assets/background/background2.png);width:180px;height:150px;background-size:unset;background-position:left;z-index:-1;right:0;bottom:20px}.button1[_ngcontent-%COMP%]{width:200px;height:47px;border:1px solid #CDCDCD;color:#3a4246;border-radius:28px;background-origin:border-box;background-clip:content-box,border-box;transition:all .3s ease}.button1[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#b8a676;transition:color .3s ease}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#ae9b66}@media screen and (max-width: 912px){.button1[_ngcontent-%COMP%]{width:100%}}.button2[_ngcontent-%COMP%]{width:200px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button2[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button2[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button2[_ngcontent-%COMP%]{width:100%}}.button3[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button3[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button3[_ngcontent-%COMP%]{width:100%}}.button4[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:#979797;border-radius:24px;transition:all .3s ease}.button4[_ngcontent-%COMP%]:hover{background:#7e7e7e}.button4[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button4[_ngcontent-%COMP%]{width:100%}}.checkbox-zone[_ngcontent-%COMP%]{padding:2px;border-radius:2px;position:relative;background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 1px 4px #ae9b664d}.avoid-resize-img[_ngcontent-%COMP%]{max-width:none}.fit-size[_ngcontent-%COMP%]{height:inherit;width:inherit;object-fit:cover;object-position:center}.p-calendar[_ngcontent-%COMP%]{width:100%}.p-calendar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#23181599}@media screen and (max-width: 912px){.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]{border:none;width:100%}.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#231815}@media screen and (max-width: 912px){.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]   .p-dropdown-trigger[_ngcontent-%COMP%]{border-radius:0;border-bottom:1px solid rgba(144,150,157,.4)}.p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{box-shadow:none}.p-radiobutton[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]{width:22px;height:22px;border:2px solid #CDCDCD;background-color:#fff;border-radius:50%;transition:all .3s cubic-bezier(.25,.46,.45,.94);position:relative;box-shadow:0 1px 3px #ae9b661a;cursor:pointer}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:hover{border-color:#b8a676;background:radial-gradient(circle at center,#b8a67614,#b8a67605);transform:scale(1.05);box-shadow:0 2px 8px #ae9b6626}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #b8a67640,0 2px 8px #ae9b6633;border-color:#a39460}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:active{transform:scale(.98)}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-color:#ae9b66;box-shadow:0 2px 12px #ae9b6640,inset 0 1px 2px #fff3}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:8px;height:8px;background:radial-gradient(circle,#fff,#ffffffe6);border-radius:50%;transform:translate(-50%,-50%) scale(0);box-shadow:0 1px 2px #0003;animation:_ngcontent-%COMP%_radioAppear .2s ease forwards}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);border-color:#9b8a5a;transform:scale(1.05);box-shadow:0 3px 15px #ae9b6659,inset 0 1px 2px #ffffff40}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #b8a6764d,0 3px 15px #ae9b6659}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-icon[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.2);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{transform:translate(-50%,-50%) scale(0);opacity:.6}to{transform:translate(-50%,-50%) scale(4);opacity:0}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background-color:#fff;color:#846a52;font-size:16px;border:1px solid #E6F0F3;border-radius:6px;padding:16px 32px}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 32px 8px;border-bottom:none!important;border-bottom-right-radius:0;border-bottom-left-radius:0}@media screen and (max-width: 912px){.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(-180deg)!important;transition:transform .3s linear}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(0)!important;transition:transform .3s linear}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]:focus{box-shadow:none}.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 32px 16px}@media screen and (max-width: 912px){.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 16px 16px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{width:24px;height:24px}.p-dialog-mask[_ngcontent-%COMP%]{width:100%;height:100%;background-color:#0006;top:0;z-index:20;pointer-events:all!important}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]{padding:32px 24px 16px;color:#231815;font-size:24px;text-align:center;display:flex;justify-content:center}.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]{padding:32px 24px 0;background:linear-gradient(180deg,#f3f1ea,#fff)}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]{display:none}.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]{padding:24px 24px 40px;display:flex;justify-content:center}.p-checkbox[_ngcontent-%COMP%]{width:18px;height:18px;display:inline-flex;align-items:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]{width:18px;height:18px;border:2px solid #CDCDCD;border-radius:3px;transition:.3s ease;background:#fff;position:relative;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:hover{border-color:#b8a676}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:focus{outline:none;border-color:#b8a676;box-shadow:0 0 0 2px #b8a67633}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]{background:#b8a676!important;border-color:#b8a676!important}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{color:#fff!important;font-size:12px;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:not(.p-highlight)   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(0);opacity:0}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]:before{content:\\\"\\\\e915\\\";font-family:primeicons;font-weight:400;font-style:normal;font-size:12px;display:inline-block}.p-checkbox-label[_ngcontent-%COMP%]{margin-left:8px;cursor:pointer;-webkit-user-select:none;user-select:none}.input[_ngcontent-%COMP%]{width:100%;border:none;padding:12.5px 0;border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px;height:4px}[_ngcontent-%COMP%]::-webkit-scrollbar-button{background:transparent;border-radius:2px}[_ngcontent-%COMP%]::-webkit-scrollbar-track-piece{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:4px;background-color:#90969d33}[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:transparent}.p-radiobutton-label[_ngcontent-%COMP%]{color:#231815!important;font-weight:400;cursor:pointer;transition:all .3s ease}.p-radiobutton-label[_ngcontent-%COMP%]:hover{color:#ae9b66!important}label[for*=requirement_][_ngcontent-%COMP%]{color:#231815!important;font-weight:400}label[for*=requirement_][_ngcontent-%COMP%]:hover{color:#ae9b66!important}input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%], .p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%] + label[_ngcontent-%COMP%]{color:#ae9b66!important;font-weight:600}.planreview-box[_ngcontent-%COMP%]{display:flex;flex-direction:row}@media screen and (max-width: 912px){.planreview-box[_ngcontent-%COMP%]{flex-direction:column}}.no-data[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:48px 32px;background:linear-gradient(135deg,#f8f7f4,#f3f1ea);border:2px dashed rgba(184,166,118,.3);border-radius:16px;text-align:center;position:relative;overflow:hidden;min-height:200px;transition:all .3s ease;width:100%}.no-data[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle at center,rgba(184,166,118,.03) 0%,transparent 70%);animation:_ngcontent-%COMP%_float 6s ease-in-out infinite;z-index:0}.no-data[_ngcontent-%COMP%]   .no-data-content[_ngcontent-%COMP%]{position:relative;z-index:1;display:flex;flex-direction:column;align-items:center;gap:16px}.no-data[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%]{width:80px;height:80px;background:linear-gradient(135deg,#b8a676,#ae9b66);border-radius:50%;display:flex;align-items:center;justify-content:center;box-shadow:0 4px 12px #ae9b664d;animation:_ngcontent-%COMP%_pulse 2s ease-in-out infinite}.no-data[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:40px;height:40px;fill:#fff;filter:drop-shadow(0 1px 2px rgba(0,0,0,.1))}.no-data[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#231815;margin:0;line-height:1.3;letter-spacing:-.5px}.no-data[_ngcontent-%COMP%]   .no-data-description[_ngcontent-%COMP%]{font-size:16px;color:#23181599;line-height:1.5;max-width:400px;margin:0}.no-data[_ngcontent-%COMP%]   .no-data-status[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;padding:8px 16px;background:#ffdd6433;border:1px solid rgba(255,221,100,.4);border-radius:20px;font-size:14px;font-weight:500;color:#b8860b}.no-data[_ngcontent-%COMP%]   .no-data-status[_ngcontent-%COMP%]:before{content:\\\"\\\";width:8px;height:8px;background:gold;border-radius:50%;animation:_ngcontent-%COMP%_blink 1.5s ease-in-out infinite}.no-data[_ngcontent-%COMP%]:hover{border-color:#b8a67680;transform:translateY(-2px);box-shadow:0 8px 24px #ae9b6666}.no-data[_ngcontent-%COMP%]:hover   .no-data-icon[_ngcontent-%COMP%]{transform:scale(1.05)}@media screen and (max-width: 912px){.no-data[_ngcontent-%COMP%]{padding:36px 24px;min-height:180px}.no-data[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%]{width:64px;height:64px}.no-data[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:32px;height:32px}.no-data[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-size:20px}.no-data[_ngcontent-%COMP%]   .no-data-description[_ngcontent-%COMP%]{font-size:14px}}@media screen and (max-width: 640px){.no-data[_ngcontent-%COMP%]{padding:24px 16px;min-height:160px}.no-data[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%]{width:56px;height:56px}.no-data[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:28px;height:28px}.no-data[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-size:18px}.no-data[_ngcontent-%COMP%]   .no-data-description[_ngcontent-%COMP%]{font-size:13px}}@keyframes _ngcontent-%COMP%_float{0%,to{transform:translate(0) rotate(0)}33%{transform:translate(30px,-30px) rotate(120deg)}66%{transform:translate(-20px,20px) rotate(240deg)}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.05)}}@keyframes _ngcontent-%COMP%_blink{0%,50%{opacity:1}51%,to{opacity:.3}}[_nghost-%COMP%]     .custom-tabview .p-tabview-nav{background:transparent;border:none;border-bottom:2px solid #E5E7EB}[_nghost-%COMP%]     .custom-tabview .p-tabview-nav li{margin-right:1.5rem}[_nghost-%COMP%]     .custom-tabview .p-tabview-nav li .p-tabview-nav-link{background:transparent;border:none;border-bottom:2px solid transparent;color:#666;padding:1rem .5rem;font-weight:500;transition:all .3s}[_nghost-%COMP%]     .custom-tabview .p-tabview-nav li .p-tabview-nav-link:hover{color:#b8a676;border-color:#b8a676}[_nghost-%COMP%]     .custom-tabview .p-tabview-nav li .p-tabview-nav-link:focus{box-shadow:none}[_nghost-%COMP%]     .custom-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link{background:transparent;border-bottom:2px solid #B8A676;color:#b8a676}[_nghost-%COMP%]     .custom-tabview .p-tabview-panels{padding:1.5rem 0;background:transparent;border:none}[_nghost-%COMP%]     .fit-size{width:100%;height:100%;object-fit:contain}@media not all and (min-width: 1280px){.max-xl\\\\:grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}}@media not all and (min-width: 1024px){.max-lg\\\\:bottom-12[_ngcontent-%COMP%]{bottom:3rem}.max-lg\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-lg\\\\:grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}}@media not all and (min-width: 768px){.max-md\\\\:bottom-14[_ngcontent-%COMP%]{bottom:3.5rem}.max-md\\\\:my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.max-md\\\\:ml-0[_ngcontent-%COMP%]{margin-left:0}.max-md\\\\:mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.max-md\\\\:mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.max-md\\\\:hidden[_ngcontent-%COMP%]{display:none}.max-md\\\\:h-\\\\__ph-0__[_ngcontent-%COMP%]{height:65%}.max-md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:450px!important}.max-md\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:500px}.max-md\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-md\\\\:max-w-full[_ngcontent-%COMP%]{max-width:100%}.max-md\\\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.max-md\\\\:flex-col[_ngcontent-%COMP%]{flex-direction:column}.max-md\\\\:flex-col-reverse[_ngcontent-%COMP%]{flex-direction:column-reverse}.max-md\\\\:flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.max-md\\\\:items-start[_ngcontent-%COMP%]{align-items:flex-start}}@media not all and (min-width: 640px){.max-sm\\\\:bottom-10[_ngcontent-%COMP%]{bottom:2.5rem}.max-sm\\\\:ml-8[_ngcontent-%COMP%]{margin-left:2rem}.max-sm\\\\:block[_ngcontent-%COMP%]{display:block}.max-sm\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:340px!important}.max-sm\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:380px}.max-sm\\\\:\\\\!grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))!important}.max-sm\\\\:grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.max-sm\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.max-sm\\\\:gap-0[_ngcontent-%COMP%]{gap:0px}.max-sm\\\\:px-0[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.max-sm\\\\:pl-0[_ngcontent-%COMP%]{padding-left:0}.max-sm\\\\:text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}}@media (min-width: 640px){.sm\\\\:mx-0[_ngcontent-%COMP%]{margin-left:0;margin-right:0}.sm\\\\:mb-0[_ngcontent-%COMP%]{margin-bottom:0}.sm\\\\:mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.sm\\\\:flex[_ngcontent-%COMP%]{display:flex}.sm\\\\:items-center[_ngcontent-%COMP%]{align-items:center}.sm\\\\:pl-3[_ngcontent-%COMP%]{padding-left:.75rem}}@media (min-width: 768px){.md\\\\:flex[_ngcontent-%COMP%]{display:flex}.md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:634px!important}.md\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.md\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.md\\\\:pb-20[_ngcontent-%COMP%]{padding-bottom:5rem}}@media (min-width: 1024px){.lg\\\\:\\\\!mx-0[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.lg\\\\:\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.lg\\\\:flex[_ngcontent-%COMP%]{display:flex}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:800px!important}.lg\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.lg\\\\:\\\\!px-4[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.lg\\\\:\\\\!px-\\\\__ph-0__[_ngcontent-%COMP%]{padding-left:22px!important;padding-right:22px!important}.lg\\\\:\\\\!py-\\\\__ph-0__[_ngcontent-%COMP%]{padding-top:18px!important;padding-bottom:18px!important}.lg\\\\:text-center[_ngcontent-%COMP%]{text-align:center}}\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "FormsModule", "BaseFilePipe", "CommonModule", "LoadingService", "finalize", "AccordionModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "item_r1", "RegularDetails", "CSelectName", "ɵɵpipeBind2", "ctx_r1", "getItemTotal", "ɵɵtemplate", "StepIiiChoiceComponent_div_1_p_accordionTab_2_ng_template_1_span_4_Template", "StepIiiChoiceComponent_div_1_p_accordionTab_2_ng_template_1_div_5_Template", "ɵɵtextInterpolate", "CItemName", "ɵɵproperty", "CShowPrice", "ɵɵelement", "ɵɵpipeBind1", "detail_r3", "CFile", "ɵɵsanitizeUrl", "StepIiiChoiceComponent_div_1_p_accordionTab_2_div_2_img_1_Template", "StepIiiChoiceComponent_div_1_p_accordionTab_2_ng_template_1_Template", "StepIiiChoiceComponent_div_1_p_accordionTab_2_div_2_Template", "getTotalPrice", "StepIiiChoiceComponent_div_1_p_accordionTab_2_Template", "StepIiiChoiceComponent_div_1_div_3_Template", "selectedData", "ɵɵlistener", "StepIiiChoiceComponent_button_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "next", "StepIiiChoiceComponent", "constructor", "_regularChangeService", "router", "messageService", "checkUserDataService", "nextEvent", "goBackEvent", "ngOnInit", "getDataStep3", "getSelectedData", "hasUnsetPrice", "some", "item", "detail", "CPrice", "add", "severity", "summary", "emit", "goBackHandle", "step", "loading", "apiRegularChangeItemGetSumaryRegularChangeItemPost$Json", "pipe", "subscribe", "res", "Entries", "reduce", "total", "detailsTotal", "detailTotal", "sum", "_", "ɵɵdirectiveInject", "i1", "RegularChangeItemService", "i2", "Router", "i3", "MessageService", "i4", "CheckUserDataService", "_2", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "StepIiiChoiceComponent_Template", "rf", "ctx", "StepIiiChoiceComponent_div_1_Template", "StepIiiChoiceComponent_div_2_Template", "StepIiiChoiceComponent_button_3_Template", "length", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i6", "Accordion", "AccordionTab", "PrimeTemplate", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\components\\step-iii-choice\\step-iii-choice.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\components\\step-iii-choice\\step-iii-choice.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { GetSumaryRegularChangeItemRes } from '../../../../../services/api/models';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';\r\nimport { CommonModule, DecimalPipe } from '@angular/common';\r\nimport { LoadingService } from '../../../../shared/services/loading.service';\r\nimport { RegularChangeItemService } from '../../../../../services/api/services';\r\nimport { finalize } from 'rxjs';\r\nimport { Router } from '@angular/router';\r\nimport { MessageService } from 'primeng/api';\r\nimport { CheckUserDataService } from '../../services/check-user-data.service';\r\nimport { AccordionModule } from 'primeng/accordion';\r\n\r\n@Component({\r\n  selector: 'app-step-iii-choice',\r\n  standalone: true,\r\n  imports: [\r\n    FormsModule,\r\n    CommonModule,\r\n    DecimalPipe,\r\n    BaseFilePipe,\r\n    AccordionModule\r\n  ],\r\n  templateUrl: './step-iii-choice.component.html',\r\n  styleUrl: './step-iii-choice.component.scss'\r\n})\r\nexport class StepIiiChoiceComponent implements OnInit {\r\n  @Input() selectedData: GetSumaryRegularChangeItemRes[] = [];\r\n  @Output() nextEvent = new EventEmitter();\r\n  @Output() goBackEvent = new EventEmitter();\r\n  constructor(\r\n    private _regularChangeService: RegularChangeItemService,\r\n    private router: Router,\r\n    private messageService: MessageService,\r\n    private checkUserDataService: CheckUserDataService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.getDataStep3();\r\n    this.selectedData = this.checkUserDataService.getSelectedData();\r\n  }\r\n  next() {\r\n    const hasUnsetPrice = this.selectedData.some(item =>\r\n      item.RegularDetails?.some(detail => detail.CPrice === null)\r\n    );\r\n\r\n    if (hasUnsetPrice) {\r\n      this.messageService.add({\r\n        severity: 'warn',\r\n        summary: '警告',\r\n        detail: '有項目尚未設定價格，請確認後再繼續。'\r\n      });\r\n      return;\r\n    }\r\n    this.nextEvent.emit();\r\n  }\r\n\r\n  goBackHandle(step: number) {\r\n    this.goBackEvent.emit(step)\r\n  }\r\n\r\n  getDataStep3() {\r\n    LoadingService.loading(true);\r\n    this._regularChangeService.apiRegularChangeItemGetSumaryRegularChangeItemPost$Json()\r\n      .pipe(\r\n        finalize(() => LoadingService.loading(false))\r\n      )\r\n      .subscribe(res => {\r\n        if (res.Entries) {\r\n          this.selectedData = res.Entries;\r\n        }\r\n      });\r\n  }\r\n  getTotalPrice(): number {\r\n    return this.selectedData.reduce((total, item) => {\r\n      const detailsTotal = item.RegularDetails?.reduce((detailTotal, detail) => {\r\n        return detailTotal + (detail.CPrice || 0);\r\n      }, 0) || 0;\r\n      return total + detailsTotal;\r\n    }, 0);\r\n  }\r\n  getItemTotal(item: any): number {\r\n    return item.RegularDetails?.reduce((sum: number, detail: any) => {\r\n      return sum + (detail.CPrice || 0);\r\n    }, 0) || 0;\r\n  }\r\n\r\n}\r\n", "<section class=\"flex flex-col justify-center items-center w-full\">\r\n  <div class=\"w-full max-w-4xl mt-6\" *ngIf=\"selectedData.length > 0\">\r\n    <p-accordion [multiple]=\"true\">\r\n      <p-accordionTab *ngFor=\"let item of selectedData; let i = index\">\r\n        <ng-template pTemplate=\"header\">\r\n          <div class=\"flex justify-between items-center w-full\">\r\n            <div class=\"flex flex-col\">\r\n              <div class=\"font-bold text-xl\">{{item.CItemName}}</div>\r\n              <span *ngIf=\"item.RegularDetails && item.RegularDetails[0]\" class=\"font-bold text-[#B69867] text-lg mt-1\">\r\n                選擇項目：{{item.RegularDetails[0].CSelectName}}\r\n              </span>\r\n            </div>\r\n            <div class=\"font-bold text-[#B69867] text-lg\" *ngIf=\"item.CShowPrice\">小計: NT$ {{getItemTotal(item) |\r\n              number:'1.0-0'}}</div>\r\n          </div>\r\n        </ng-template>\r\n        <div *ngFor=\"let detail of item.RegularDetails\" class=\"mb-4 px-4\">\r\n          <img class=\"fit-size rounded shadow\" [src]=\"detail.CFile | addBaseFile\" *ngIf=\"detail.CFile\">\r\n        </div>\r\n      </p-accordionTab>\r\n    </p-accordion>\r\n    <div class=\"bg-[#F3F1EA80] p-4 mt-6 rounded-lg flex justify-between items-center\"\r\n      *ngIf=\"selectedData[0].CShowPrice\">\r\n      <span class=\"font-bold text-[#333333]\">總計金額</span>\r\n      <span class=\"font-bold text-lg text-[#B69867]\">NT$ {{getTotalPrice() | number:'1.0-0'}}</span>\r\n    </div>\r\n  </div>\r\n  <div class=\"no-data\" *ngIf=\"selectedData.length <= 0\">\r\n    表單尚未鎖定\r\n  </div>\r\n  <button class=\"button2 !w-48 butn1 flex justify-center items-center mt-4 mb-4\" *ngIf=\"selectedData.length > 0\" pButton\r\n    (click)=\"next()\">\r\n    下一步\r\n  </button>\r\n</section>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAE9E,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,yCAAyC;AACtE,SAASC,YAAY,QAAqB,iBAAiB;AAC3D,SAASC,cAAc,QAAQ,6CAA6C;AAE5E,SAASC,QAAQ,QAAQ,MAAM;AAI/B,SAASC,eAAe,QAAQ,mBAAmB;;;;;;;;;;ICHrCC,EAAA,CAAAC,cAAA,eAA0G;IACxGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,oCAAAC,OAAA,CAAAC,cAAA,IAAAC,WAAA,MACF;;;;;IAEFR,EAAA,CAAAC,cAAA,cAAsE;IAAAD,EAAA,CAAAE,MAAA,GACpD;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAD8CH,EAAA,CAAAI,SAAA,EACpD;IADoDJ,EAAA,CAAAK,kBAAA,uBAAAL,EAAA,CAAAS,WAAA,OAAAC,MAAA,CAAAC,YAAA,CAAAL,OAAA,gBACpD;;;;;IANhBN,EAFJ,CAAAC,cAAA,cAAsD,cACzB,cACM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAY,UAAA,IAAAC,2EAAA,mBAA0G;IAG5Gb,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAY,UAAA,IAAAE,0EAAA,kBAAsE;IAExEd,EAAA,CAAAG,YAAA,EAAM;;;;IAP6BH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAe,iBAAA,CAAAT,OAAA,CAAAU,SAAA,CAAkB;IAC1ChB,EAAA,CAAAI,SAAA,EAAmD;IAAnDJ,EAAA,CAAAiB,UAAA,SAAAX,OAAA,CAAAC,cAAA,IAAAD,OAAA,CAAAC,cAAA,IAAmD;IAIbP,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAiB,UAAA,SAAAX,OAAA,CAAAY,UAAA,CAAqB;;;;;IAKtElB,EAAA,CAAAmB,SAAA,cAA6F;;;;;IAAxDnB,EAAA,CAAAiB,UAAA,QAAAjB,EAAA,CAAAoB,WAAA,OAAAC,SAAA,CAAAC,KAAA,GAAAtB,EAAA,CAAAuB,aAAA,CAAkC;;;;;IADzEvB,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAY,UAAA,IAAAY,kEAAA,kBAA6F;IAC/FxB,EAAA,CAAAG,YAAA,EAAM;;;;IADqEH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAiB,UAAA,SAAAI,SAAA,CAAAC,KAAA,CAAkB;;;;;IAd/FtB,EAAA,CAAAC,cAAA,qBAAiE;IAa/DD,EAZA,CAAAY,UAAA,IAAAa,oEAAA,yBAAgC,IAAAC,4DAAA,iBAYkC;IAGpE1B,EAAA,CAAAG,YAAA,EAAiB;;;;IAHSH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAiB,UAAA,YAAAX,OAAA,CAAAC,cAAA,CAAsB;;;;;IAOhDP,EAFF,CAAAC,cAAA,cACqC,eACI;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,eAA+C;IAAAD,EAAA,CAAAE,MAAA,GAAwC;;IACzFF,EADyF,CAAAG,YAAA,EAAO,EAC1F;;;;IAD2CH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAK,kBAAA,SAAAL,EAAA,CAAAS,WAAA,OAAAC,MAAA,CAAAiB,aAAA,iBAAwC;;;;;IAtBzF3B,EADF,CAAAC,cAAA,aAAmE,qBAClC;IAC7BD,EAAA,CAAAY,UAAA,IAAAgB,sDAAA,4BAAiE;IAiBnE5B,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAY,UAAA,IAAAiB,2CAAA,iBACqC;IAIvC7B,EAAA,CAAAG,YAAA,EAAM;;;;IAxBSH,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAiB,UAAA,kBAAiB;IACKjB,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAiB,UAAA,YAAAP,MAAA,CAAAoB,YAAA,CAAiB;IAmBjD9B,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAiB,UAAA,SAAAP,MAAA,CAAAoB,YAAA,IAAAZ,UAAA,CAAgC;;;;;IAKrClB,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAE,MAAA,6CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IACNH,EAAA,CAAAC,cAAA,iBACmB;IAAjBD,EAAA,CAAA+B,UAAA,mBAAAC,iEAAA;MAAAhC,EAAA,CAAAiC,aAAA,CAAAC,GAAA;MAAA,MAAAxB,MAAA,GAAAV,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAS1B,MAAA,CAAA2B,IAAA,EAAM;IAAA,EAAC;IAChBrC,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADPX,OAAM,MAAOmC,sBAAsB;EAIjCC,YACUC,qBAA+C,EAC/CC,MAAc,EACdC,cAA8B,EAC9BC,oBAA0C;IAH1C,KAAAH,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,oBAAoB,GAApBA,oBAAoB;IAPrB,KAAAb,YAAY,GAAoC,EAAE;IACjD,KAAAc,SAAS,GAAG,IAAInD,YAAY,EAAE;IAC9B,KAAAoD,WAAW,GAAG,IAAIpD,YAAY,EAAE;EAMtC;EAEJqD,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACjB,YAAY,GAAG,IAAI,CAACa,oBAAoB,CAACK,eAAe,EAAE;EACjE;EACAX,IAAIA,CAAA;IACF,MAAMY,aAAa,GAAG,IAAI,CAACnB,YAAY,CAACoB,IAAI,CAACC,IAAI,IAC/CA,IAAI,CAAC5C,cAAc,EAAE2C,IAAI,CAACE,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAK,IAAI,CAAC,CAC5D;IAED,IAAIJ,aAAa,EAAE;MACjB,IAAI,CAACP,cAAc,CAACY,GAAG,CAAC;QACtBC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,IAAI;QACbJ,MAAM,EAAE;OACT,CAAC;MACF;;IAEF,IAAI,CAACR,SAAS,CAACa,IAAI,EAAE;EACvB;EAEAC,YAAYA,CAACC,IAAY;IACvB,IAAI,CAACd,WAAW,CAACY,IAAI,CAACE,IAAI,CAAC;EAC7B;EAEAZ,YAAYA,CAAA;IACVlD,cAAc,CAAC+D,OAAO,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACpB,qBAAqB,CAACqB,uDAAuD,EAAE,CACjFC,IAAI,CACHhE,QAAQ,CAAC,MAAMD,cAAc,CAAC+D,OAAO,CAAC,KAAK,CAAC,CAAC,CAC9C,CACAG,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,OAAO,EAAE;QACf,IAAI,CAACnC,YAAY,GAAGkC,GAAG,CAACC,OAAO;;IAEnC,CAAC,CAAC;EACN;EACAtC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACG,YAAY,CAACoC,MAAM,CAAC,CAACC,KAAK,EAAEhB,IAAI,KAAI;MAC9C,MAAMiB,YAAY,GAAGjB,IAAI,CAAC5C,cAAc,EAAE2D,MAAM,CAAC,CAACG,WAAW,EAAEjB,MAAM,KAAI;QACvE,OAAOiB,WAAW,IAAIjB,MAAM,CAACC,MAAM,IAAI,CAAC,CAAC;MAC3C,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;MACV,OAAOc,KAAK,GAAGC,YAAY;IAC7B,CAAC,EAAE,CAAC,CAAC;EACP;EACAzD,YAAYA,CAACwC,IAAS;IACpB,OAAOA,IAAI,CAAC5C,cAAc,EAAE2D,MAAM,CAAC,CAACI,GAAW,EAAElB,MAAW,KAAI;MAC9D,OAAOkB,GAAG,IAAIlB,MAAM,CAACC,MAAM,IAAI,CAAC,CAAC;IACnC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;EACZ;EAAC,QAAAkB,CAAA,G;qBA3DUjC,sBAAsB,EAAAtC,EAAA,CAAAwE,iBAAA,CAAAC,EAAA,CAAAC,wBAAA,GAAA1E,EAAA,CAAAwE,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA5E,EAAA,CAAAwE,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA9E,EAAA,CAAAwE,iBAAA,CAAAO,EAAA,CAAAC,oBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtB3C,sBAAsB;IAAA4C,SAAA;IAAAC,MAAA;MAAArD,YAAA;IAAA;IAAAsD,OAAA;MAAAxC,SAAA;MAAAC,WAAA;IAAA;IAAAwC,UAAA;IAAAC,QAAA,GAAAtF,EAAA,CAAAuF,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC1BnC7F,EAAA,CAAAC,cAAA,iBAAkE;QA8BhED,EA7BA,CAAAY,UAAA,IAAAmF,qCAAA,iBAAmE,IAAAC,qCAAA,iBA0Bb,IAAAC,wCAAA,oBAInC;QAGrBjG,EAAA,CAAAG,YAAA,EAAU;;;QAjC4BH,EAAA,CAAAI,SAAA,EAA6B;QAA7BJ,EAAA,CAAAiB,UAAA,SAAA6E,GAAA,CAAAhE,YAAA,CAAAoE,MAAA,KAA6B;QA0B3ClG,EAAA,CAAAI,SAAA,EAA8B;QAA9BJ,EAAA,CAAAiB,UAAA,SAAA6E,GAAA,CAAAhE,YAAA,CAAAoE,MAAA,MAA8B;QAG4BlG,EAAA,CAAAI,SAAA,EAA6B;QAA7BJ,EAAA,CAAAiB,UAAA,SAAA6E,GAAA,CAAAhE,YAAA,CAAAoE,MAAA,KAA6B;;;mBDb3GxG,WAAW,EACXE,YAAY,EAAAuG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAEZ3G,YAAY,EACZI,eAAe,EAAAwG,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,YAAA,EAAA5B,EAAA,CAAA6B,aAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}