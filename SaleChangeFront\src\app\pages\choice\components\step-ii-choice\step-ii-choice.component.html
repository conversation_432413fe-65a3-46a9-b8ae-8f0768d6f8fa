<section class="flex flex-col justify-center items-center w-full pb-4" id="choice-selecting">
  @switch (currentTypeUI) {
  @case (0) {
  <div class="flex flex-col text-center" style="padding-bottom: 24px;">
    <span class="text-black text-xl font-bold">
      選擇項目
    </span>
    <span class="text-base mt-2">
      點擊項目可前往選擇
    </span>
  </div>
  <div class="flex flex-col lg:!w-[800px] w-full m-auto pb-5 px-4" *ngIf="listRegularChangeItem.length > 0">
    <div
      class="lg:flex items-center justify-between cursor-pointer bg-[#F3F1EA99] rounded-lg mb-2 p-4 lg:!px-4 lg:!py-[18px]"
      *ngFor="let regularChangeItem of listRegularChangeItem">
      <div class="lg:flex items-center">
        <div class="bg-[#B8A676] px-4 py-1 h-[31px] w-[88px] mr-3 flex justify-center items-center rounded">
          <span class="text-base text-white">
            {{regularChangeItem.CTotalAnswer + '選' + regularChangeItem.CRequireAnswer}}
          </span>
        </div>
        <span class="text-xl text-black block lg:text-center w-full text-left">
          {{regularChangeItem.CItemName}}
        </span>
      </div>
      <button class="flex justify-center text-white
          items-center font-medium rounded-3xl
          bg-gray-500 p-2
          w-full lg:!w-[180px] h-[47px] py-3 text-base" [disabled]="isDisabledOptionStep2" pButton
        (click)="gotoPickItem(regularChangeItem)" [ngStyle]="handleStyleOption(regularChangeItem.CIsSelect!)">
        {{hanldeTitleOption(regularChangeItem.CIsSelect!)}}
      </button>
    </div>
  </div>
  <div class="flex flex-col w-full m-auto mt-4" *ngIf="listRegularChangeItem.length <= 0">
    <div class="no-data">
      表單尚未鎖定
    </div>
  </div>

  <button class="text-base btn-next w-[180px] h-[47px] butn1 flex justify-center items-center my-4" pButton
    [disabled]="checkAllSelect()" [ngClass]="{'btn-enable': !checkAllSelect(), 'btn-disable': checkAllSelect()}"
    (click)="next()" *ngIf="listRegularChangeItem.length > 0">
    下一步
  </button>
  }
  @case (1) {
  <div class="flex flex-col text-center justify-center items-center">
    <span class="text-2xl text-black" *ngIf="regularChangeDetail">
      {{regularChangeDetail.CItemName!}}
    </span>
    <span class="text-base mt-2" *ngIf="regularChangeDetail">
      請選擇 {{regularChangeDetail.CRequireAnswer!}} 個選項，點擊可放大檢視圖片詳細.
    </span>
    <div *ngIf="regularChangeDetail?.CDesignFileUrl"
      class="bg-[#008FC7] bg-opacity-[0.04] px-3 py-2 mt-2 cursor-pointer w-[50%] rounded-md"
      (click)="enlargeimg(regularChangeDetail.CDesignFileUrl!)">
      <div class="flex items-center justify-center">
        <img class="mr-2" src="/assets/designFile.svg">
        <span class="text-[#008FC7]">
          檢視設計概念
        </span>
      </div>
    </div>
  </div>
  <div class="flex max-sm:block justify-center my-6" *ngIf="!!regularChangeDetail">
    @for (regularChangeDetailData of regularChangeDetail.regularChangeDetails!; track $index) {
    <div class="flex items-center justify-center flex-col mr-5"
      [ngStyle]="{'width': 100 / regularChangeDetail.regularChangeDetails!.length! + '%'}">
      <div class="box">
        <img class="fit-size" [src]="regularChangeDetailData.CPicture?.CFile! | addBaseFile"
          (click)="enlargeimg(regularChangeDetailData.CInfoPicture?.CFile!, regularChangeDetailData.CSelectName)">
      </div>
      <div class="radiobox max-sm:bottom-10 max-md:bottom-14 max-lg:bottom-12">
        @if (regularChangeDetail.CRequireAnswer!
        <= 1) { <p-radioButton class="mr-2" name="plan"
          [inputId]="regularChangeDetailData.CRegularChangeDetailId?.toString()" [value]="regularChangeDetailData"
          [(ngModel)]="selectedRegularChangeDetail" />
        } @else {
        <p-checkbox class="mr-2" name="plan" [inputId]="regularChangeDetailData.CRegularChangeDetailId?.toString()"
          [value]="regularChangeDetailData" [(ngModel)]="selectedRegularChangeDetail"
          (onChange)="validateTotalAnswer()" />
        }
        <label class="cursor-pointer text-xl text-center w-full"
          [for]="regularChangeDetailData.CRegularChangeDetailId?.toString()">
          {{regularChangeDetailData.CSelectName}}
        </label>
      </div>
      <div class="flex justify-center text-base">
        <span>
          {{regularChangeDetailData.CDescription}}
        </span>
      </div>
      <div class="flex justify-center text-sm mt-1" *ngIf="regularChangeDetailData.CPrice">
        <span class="text-[#B8A676] font-medium">
          NT$ {{formatPrice(regularChangeDetailData.CPrice)}}
        </span>
      </div>
    </div>
    }
  </div>
  <div class="flex items-center justify-center my-6" id="action">
    <button class="button1 butn1 flex justify-center items-center mr-4 !w-40" pButton (click)="reset()">
      返回列表
    </button>
    <button class="button2 butn1 flex justify-center items-center ml-4 !w-40" pButton (click)="save()">
      儲存
    </button>
  </div>
  }
  @case (2) {
  <div class="flex flex-col text-center justify-center items-center">
    <span class="text-2xl text-black" *ngIf="regularChangeDetail">
      {{regularChangeDetail.CItemName!}}
    </span>
    <span class="text-base mt-2" *ngIf="regularChangeDetail">
      請選擇 {{regularChangeDetail.CRequireAnswer!}} 個選項，點擊可放大檢視圖片詳細。
    </span>
    <div *ngIf="regularChangeDetail?.CDesignFileUrl"
      class="bg-[#008FC7] bg-opacity-[0.04] px-3 py-2 mt-2 cursor-pointer w-[50%] rounded-md"
      (click)="enlargeimg(regularChangeDetail.CDesignFileUrl!)">
      <div class="flex items-center justify-center">
        <img class="mr-2 fit-size" src="/assets/designFile.svg">
        <span class="text-[#008FC7]">
          檢視設計概念
        </span>
      </div>
    </div>
  </div>
  <div class="grid grid-cols-10 max-xl:grid-cols-6 max-lg:grid-cols-4 my-6 w-full" *ngIf="!!regularChangeDetail">
    @for (regularChangeDetailData of regularChangeDetail.regularChangeDetails!; track $index) {
    <div class="relative !w-[107px] m-auto mb-4">
      <div class="relative !w-[107px] !h-[107px]">
        <label [for]="regularChangeDetailData.CRegularChangeDetailId?.toString()">
          <img class="fit-img-size" [src]="regularChangeDetailData.CPicture?.CFile! | addBaseFile"
            (click)="enlargeimg(regularChangeDetailData.CInfoPicture?.CFile!,regularChangeDetailData.CSelectName)">
          @if (regularChangeDetail.CRequireAnswer!
          <= 1) { <p-radioButton class="absolute left-3 top-3" name="itemColor"
            [inputId]="regularChangeDetailData.CRegularChangeDetailId?.toString()" [value]="regularChangeDetailData"
            [(ngModel)]="selectedRegularChangeDetail" />
          }@else {
          <p-checkbox class="absolute left-0 top-1" name="itemColor"
            [inputId]="regularChangeDetailData.CRegularChangeDetailId?.toString()" [value]="regularChangeDetailData"
            [(ngModel)]="selectedRegularChangeDetail" (onChange)="validateTotalAnswer()" />
          }
        </label>
      </div>
      <div class="text-center mt-1" *ngIf="regularChangeDetailData.CPrice">
        <span class="text-[#B8A676] font-medium text-xs">
          NT$ {{formatPrice(regularChangeDetailData.CPrice)}}
        </span>
      </div>
    </div>
    }
  </div>
  <div class="flex items-center justify-center my-6 max-lg:w-full" id="action">
    <button class="button1 butn1 flex justify-center items-center m-2" pButton (click)="reset()">
      返回列表
    </button>
    <button class="button2 butn1 flex justify-center items-center m-2" pButton (click)="save()">
      儲存
    </button>
  </div>
  }
  @case (3){
  <div class="flex flex-col text-center justify-center items-center">
    <span class="text-2xl text-black" *ngIf="buildingSampleSelection">
      {{buildingSampleSelection.CItemName!}}
    </span>
    <span class="text-base mt-2">
      請選擇 1 個選項，點擊可放大檢視圖片詳細。
    </span>
    <div *ngIf="regularChangeDetail?.CDesignFileUrl"
      class="bg-[#008FC7] bg-opacity-[0.04] px-3 py-2 mt-2 cursor-pointer w-[50%] rounded-md"
      (click)="enlargeimg(buildingSampleSelection.CDesignFileUrl!)">
      <div class="flex items-center justify-center">
        <img class="mr-2" src="/assets/designFile.svg">
        <span class="text-[#008FC7]">
          檢視設計概念
        </span>
      </div>
    </div>
  </div>
  <div *ngIf="!!buildingSampleSelection && !!selectedBuildingSample"
    class="flex flex-row max-md:flex-col-reverse my-6 flex-wrap w-full justify-between">
    <div class="w-[760px]">
      <div class="flex flex-row justify-between">
        <div class="w-[660px] h-[145px]">
          <p-dropdown class="drop100 h-[47px]" [options]="buildingSampleSelection.buildingSamples!"
            [(ngModel)]="selectedBuildingSample" appendTo="body" optionLabel="CSelectName"
            (ngModelChange)="changeBuilding($event)" />
          <div class="text-base mt-3">備註</div>
          <div class="flex">
            <p-dropdown class="drop50 mr-4 h-[47px]" [options]="selectedBuildingSample.RegularRemark!"
              [(ngModel)]="selectedRemark" appendTo="body" optionLabel="CTypeName" (onChange)="changeRemark($event)" />
            <input pInputText class="input input-inline ml-2 fs-12 !mt-0" [(ngModel)]="selectedBuildingSample.CRemark"
              [disabled]="selectedRemark != null ? (selectedRemark.CRegularChangeItemRemarkTypeID != null ? true : false) : false"
              placeholder="在此輸入您的文字">
          </div>
        </div>
        <div class="h-[80px] w-[80px]">
          <img class="object-cover m-auto w-full h-full rounded"
            [src]="(selectedBuildingSample.CInfoPictureFile?.length && selectedBuildingSample.CInfoPictureFile![0].CFile) ? (selectedBuildingSample.CInfoPictureFile![0].CFile | addBaseFile) : ''"
            (click)="enlargeimg(
              selectedBuildingSample.CInfoPictureFile![0].CFile ? (selectedBuildingSample.CInfoPictureFile![0].CFile) :'', selectedBuildingSample.CSelectName)">
        </div>
      </div>
    </div>
    <div class="w-[400px]">
      <div class="flex flex-col max-md:my-4">
        <div class="overflow-hidden">
          <img class="object-cover w-[400px] h-[280px] rounded-md"
            (click)="enlargeimg((selectedBuildingSample.CPictureFile?.length && selectedBuildingSample.CPictureFile![0].CFile ) ? selectedBuildingSample.CPictureFile![0].CFile : '', selectedBuildingSample.CSelectName)"
            [src]="(selectedBuildingSample.CPictureFile?.length && selectedBuildingSample.CPictureFile![0].CFile ) ? (selectedBuildingSample.CPictureFile![0].CFile | addBaseFile):''">
        </div>
        <div class="text-left mt-3">
          <div class="text text-base">{{(selectedBuildingSample.CDescription)
            ? selectedBuildingSample.CDescription
            : ''
            }}</div>
          <div class="text-left mt-2" *ngIf="selectedBuildingSample.CPrice">
            <span class="text-[#B8A676] font-medium text-base">
              NT$ {{formatPrice(selectedBuildingSample.CPrice)}}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="flex items-center justify-center my-6 max-lg:w-full" id="action">
    <button class="button1 butn1 flex justify-center items-center m-2" pButton (click)="reset()">
      返回列表
    </button>
    <button class="button2 butn1 flex justify-center items-center m-2" pButton (click)="save()">
      儲存
    </button>
  </div>
  }
  }
</section>

<ng-container>
  <p-dialog [(visible)]="visibleimg" [draggable]="false" [closable]="false" [resizable]="false"
    [style]="{'width': '1000px','min-height':'auto'}" [breakpoints]="{ '600px': '100vw','auto': '90vw' }">
    <ng-template pTemplate="header">
      <span class="title">
        <span> {{ conentenlargeimg }}</span>
      </span>
    </ng-template>
    <div class="w-full flex items-center justify-center">
      <div class="w-[80%] h-auto flex items-center justify-center">
        <img class="fit-size" [src]="choiceenlargeimg! | addBaseFile">
      </div>
    </div>
    <ng-template pTemplate="footer">
      <button class="button2 m-0" (click)="closeimg()">關閉</button>
    </ng-template>
    <div>
    </div>
  </p-dialog>
</ng-container>