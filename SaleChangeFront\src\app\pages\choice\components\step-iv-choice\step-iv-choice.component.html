<section>
  <div class="flex flex-col justify-center items-center w-full">
    <span class="text-black step-title">
      客變原則
    </span>
    @if(!!detailPicture) {
    <iframe class="mt-3 w-full" [src]="detailPicture | addBaseFile" width="800" height="500">
    </iframe>
    } @else {
    <div class="w-full h-[40vh] flex justify-center items-center bg-[#d9d9d9]">
      <div class="font-semibold text-lg">
        客變說明<br>
        （內嵌PDF）
      </div>
    </div>
    }

  <div class="w-[100%] flex items-start justify-start sec-e-check">
      <div class="text-stone-600 checkbox-zone w-full">
        <div class="bg-white" style="padding: 12px 16px;">
          <p-checkbox [binary]="true" label="我已閱讀並同意以上說明內容。" [(ngModel)]="agreeToGoNextStep4"
            (ngModelChange)="changeCheckbox($event)"></p-checkbox>
        </div>
      </div>
    </div>

    <button class="button2 !w-48 butn1 !text-center flex justify-center items-center" pButton
      [disabled]="!agreeToGoNextStep4" (click)="next()">
      下一步
    </button>
  </div>
</section>
