﻿@import "../../../styles.scss";

.notice {
  margin-top: 20px;
  width: 1200px;
  border: 1px solid $bg-tertiary;
  background: $gradient-background;
  padding: 32px 24px;
  border-radius: 8px;
  min-height: 480px;
  height: 480px;
  overflow-y: auto;
  box-shadow: $shadow-sm;

  .title {
    color: $text-primary;
    font-size: 24px;
    font-weight: 600;
  }
}

button {
  margin: 48px 0;

  @media screen and (max-width: $main-webS) {
    margin: 32px 0;
  }
}

.page-title {
  font-size: 32px;
  color: $text-primary;
  text-align: left;
  width: 1200px;
  font-weight: bold;
  margin-bottom: 8px;
}
