﻿@import "src/styles.scss";

:host {
  .disabled {
    background-color: rgb(170, 170, 170);
  }


}

::ng-deep {
  .radiobox {
    width: 175px;
    height: 45px;
    box-shadow: 0 1px 8px rgba(69, 74, 79, 0.12);
    padding: 8px 24px;
    background-color: #fff;
    border-radius: 31px;
    display: flex;
    align-items: center;
    position: relative;
    top: -20px;

    @media screen and (max-width: $main-mobileL) {
      min-width: 125px;
    }

    label {
      width: max-content;
    }
  }
}

.no-data {
  padding: 10px 20px;
  background-color: #ffff00;
  font-size: 20px;
  text-align: center;
  border-radius: 5px;
}

.btn-enable {
  background: linear-gradient(90deg, $primary-gold-dark, $primary-gold-light);
  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(90deg, $primary-gold-darker, $primary-gold-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(174, 155, 102, 0.3);
  }
}

.btn-disable {
  background-color: $text-disabled !important;
  color: $text-primary !important;
  transition: all 0.3s ease;
}

.btn-next {
  padding: 12px 24px;
  color: #fff;
  border-radius: 24px;
}

.fit-img-size {
  width: 107px;
  height: 107px;
  object-fit: cover;
  object-position: center;
}