﻿@import "../../../../../styles.scss";

.btn-enable {
  background: linear-gradient(90deg, $primary-gold-dark, $primary-gold-light);
  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(90deg, $primary-gold-darker, $primary-gold-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(174, 155, 102, 0.3);
  }
}

.btn-disable {
  background-color: $text-disabled !important;
  color: $text-primary !important;
  transition: all 0.3s ease;
}

.btn-next {
  padding: 12px 24px;
  color: #fff;
  border-radius: 24px;
  transition: all 0.3s ease;
}
