﻿@import "../../../../../styles.scss";

.btn-noReq {
  border: 1px solid $border-medium;
  border-radius: 28px;
  color: $text-tertiary;
  font-size: 16px;
  transition: all 0.3s ease;

  &:hover {
    border-color: $primary-gold-light;
    color: $primary-gold-dark;
    background-color: rgba(184, 166, 118, 0.05);
  }
}

.btn-yesReq {
  background: linear-gradient(90deg, $primary-gold-dark 0%, $primary-gold-light 100%);
  border-radius: 28px;
  color: #fff;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.2);

  &:hover {
    background: linear-gradient(90deg, $primary-gold-darker 0%, $primary-gold-dark 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(174, 155, 102, 0.3);
  }
}

.group-title {
  font-size: 20px;
  font-weight: 700;
  color: $primary-gold-dark;
  padding: 16px 0 12px 0;
  margin-bottom: 20px;
  position: relative;
  display: flex;
  align-items: center;

  &::before {
    content: '';
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, $primary-gold-dark 0%, $primary-gold-light 100%);
    border-radius: 2px;
    margin-right: 12px;
  }

  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, rgba(184, 166, 118, 0.3) 0%, transparent 100%);
    margin-left: 16px;
  }
}

.group-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 248, 248, 0.6) 100%);
  border: 1px solid rgba(184, 166, 118, 0.15);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 32px;
  position: relative;
  backdrop-filter: blur(12px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.04),
    0 2px 8px rgba(184, 166, 118, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeInUp 0.6s ease-out;

  &:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 248, 248, 0.8) 100%);
    border-color: rgba(184, 166, 118, 0.25);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.06),
      0 4px 16px rgba(184, 166, 118, 0.12);
    transform: translateY(-2px);
  }

  &:nth-child(1) {
    animation-delay: 0.1s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.3s;
  }

  &:nth-child(4) {
    animation-delay: 0.4s;
  }

  &:nth-child(5) {
    animation-delay: 0.5s;
  }
}

.requirement-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 248, 248, 0.9) 100%);
  border: 1px solid rgba(184, 166, 118, 0.15);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.04),
    0 1px 3px rgba(184, 166, 118, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 8px 24px rgba(0, 0, 0, 0.08),
      0 4px 8px rgba(184, 166, 118, 0.15);
    border-color: rgba(184, 166, 118, 0.25);
  }

  &:last-child {
    margin-bottom: 0;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, $primary-gold-light 0%, $primary-gold-dark 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }
}

.requirement-name {
  margin-bottom: 20px;
}

.requirement-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  line-height: 1.4;
  letter-spacing: 0.5px;
}

.requirement-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;

  @media (min-width: 640px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.unit-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(184, 166, 118, 0.08);
  border-radius: 8px;
  border: 1px solid rgba(184, 166, 118, 0.2);
}

.unit-label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.unit-value {
  font-size: 14px;
  font-weight: 600;
  color: $primary-gold-dark;
  background: rgba(184, 166, 118, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.price-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(46, 125, 50, 0.08);
  border-radius: 8px;
  border: 1px solid rgba(46, 125, 50, 0.2);
}

.price-label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.price-value {
  font-size: 14px;
  font-weight: 600;
  color: #2e7d32;
  background: rgba(46, 125, 50, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.quantity-label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  min-width: 40px;
}

.quantity-input-wrapper {
  position: relative;
}

.modern-quantity-input {
  .p-inputnumber {
    .p-inputnumber-input {
      width: 80px;
      height: 40px;
      text-align: center;
      border: 2px solid rgba(184, 166, 118, 0.2);
      border-radius: 10px;
      font-size: 14px;
      font-weight: 600;
      color: #2c3e50;
      background: rgba(255, 255, 255, 0.9);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:focus {
        border-color: $primary-gold-light;
        box-shadow: 0 0 0 3px rgba(184, 166, 118, 0.15);
        outline: none;
        background: #fff;
      }

      &::placeholder {
        color: #999;
        font-weight: 400;
      }
    }

    .p-inputnumber-button {
      width: 32px;
      height: 40px;
      border: 2px solid rgba(184, 166, 118, 0.2);
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 248, 248, 0.8) 100%);
      color: $primary-gold-dark;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(8px);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, $primary-gold-light 0%, $primary-gold-dark 100%);
        color: white;
        border-color: $primary-gold-dark;
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(184, 166, 118, 0.3);
      }

      &:active {
        transform: scale(0.95);
      }

      &:disabled {
        background: rgba(241, 241, 241, 0.5);
        color: #ccc;
        cursor: not-allowed;
      }

      .pi {
        font-size: 12px;
        font-weight: 600;
      }
    }

    .p-inputnumber-button-down {
      border-radius: 10px 0 0 10px;
      border-right: none;
    }

    .p-inputnumber-button-up {
      border-radius: 0 10px 10px 0;
      border-left: none;
    }
  }
}

@media (max-width: 768px) {
  .group-container {
    padding: 20px 16px;
    margin-bottom: 24px;
  }

  .requirement-card {
    padding: 20px 16px;
  }

  .requirement-controls {
    gap: 12px;
  }

  .modern-quantity-input .p-inputnumber .p-inputnumber-input {
    width: 70px;
    height: 36px;
  }

  .modern-quantity-input .p-inputnumber .p-inputnumber-button {
    width: 28px;
    height: 36px;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.requirement-card {
  animation: slideInRight 0.5s ease-out;

  &:nth-child(1) {
    animation-delay: 0.1s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.3s;
  }

  &:nth-child(4) {
    animation-delay: 0.4s;
  }

  &:nth-child(5) {
    animation-delay: 0.5s;
  }
}

.unit-label,
.quantity-label {
  font-weight: 500;
  color: #64748b;
  white-space: nowrap;
  user-select: none;
}