import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule, NgFor } from '@angular/common';
import { LoadingService } from '../../../../shared/services/loading.service';
import { concatMap, finalize, tap } from 'rxjs';
import { GetHouseReview } from '../../../../../services/api/models';
import { ButtonModule } from 'primeng/button';
import { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';
import { HouseService } from '../../../../../services/api/services';
import { LocalStorageService } from '../../../../shared/services/local-storage.service';
import { STORAGE_KEY } from '../../../../shared/constant/constant';

@Component({
  selector: 'app-step-v-choice',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,

    BaseFilePipe,

    NgFor
  ],
  templateUrl: './step-v-choice.component.html',
  styleUrl: './step-v-choice.component.scss'
})
export class StepVChoiceComponent implements OnInit {
  @Input() listHouseReview: GetHouseReview[] = []
  @Output() nextEvent = new EventEmitter()

  @Output() refreshHouseReview = new EventEmitter()
  @Output() listHouseReviewChange = new EventEmitter()

  currentHouseReview!: GetHouseReview
  isReviewing: boolean = false;

  constructor(
    private _houseService: HouseService
  ) {}

  ngOnInit(): void { }

  gotoPickItem(houseReview: GetHouseReview) {
    this.currentHouseReview = houseReview;
    this.isReviewing = true
  }

  handleStyleOption(isReview: boolean) {
    if (isReview) {
      return { 'background': 'linear-gradient(90deg, #AE9B66 0%, #B8A676 100%)', 'color': 'white' }
    }
    return { 'backgroundColor': '#E5E3E1', 'color': '#3A4246B2' }
  }
  hanldeTitleOption(isReview: boolean) {
    return !isReview ? '未審閱' : '已審閱'
  }

  next() {
    this.nextEvent.emit();
  }

  handleReview() {
    this._houseService.apiHouseUpdateHouseReviewPost$Json({
      body: {
        CHouseReviewID: this.currentHouseReview.CHouseReviewId!
      }
    }).pipe(
      tap(() => this.refreshHouseReview.emit()),
      tap(res => {
        if (res.StatusCode == 0) {
          this.currentHouseReview = {} as GetHouseReview
          this.isReviewing = false;
        }
      })
    ).subscribe();
  }

  checkDisable() {
    return !(this.listHouseReview.length! > 0 && this.listHouseReview.every(x => x.CIsReview))
  }
}
